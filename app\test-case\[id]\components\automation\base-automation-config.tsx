'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  GitBranch,
  Terminal,
  Settings,
  ExternalLink,
  Globe,
  Monitor,
  Clock,
  CheckCircle,
  XCircle,
  Activity,
  Edit,
  Plus,
  Play
} from 'lucide-react';
import { AutomationConfig } from '../../types';

interface BaseAutomationConfigProps {
  config?: AutomationConfig;
  framework: 'midscene' | 'playwright' | 'cypress' | 'selenium';
  frameworkIcon: React.ComponentType<any>;
  frameworkColor: string;
  frameworkBg: string;
  frameworkBorder: string;
  onEdit?: () => void;
  onCreate?: () => void;
  onRun?: () => void;
}

export default function BaseAutomationConfig({
  config,
  framework,
  frameworkIcon: FrameworkIcon,
  frameworkColor,
  frameworkBg,
  frameworkBorder,
  onEdit,
  onCreate,
  onRun
}: BaseAutomationConfigProps) {
  const statusInfo = config ? 
    (config.isActive 
      ? { icon: CheckCircle, color: 'text-green-600', text: 'Active' }
      : { icon: XCircle, color: 'text-red-600', text: 'Inactive' }
    ) : null;

  if (!config) {
    return (
      <div className={`bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border ${frameworkBorder} dark:border-opacity-50`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${frameworkBg} ${frameworkBorder} border`}>
              <FrameworkIcon className={`w-5 h-5 ${frameworkColor}`} />
            </div>
            <div>
              <h3 className="text-lg font-semibold capitalize">{framework}</h3>
              <p className="text-sm text-slate-500">Not configured</p>
            </div>
          </div>
        </div>
        
        <div className="text-center py-8">
          <FrameworkIcon className="w-12 h-12 text-slate-400 mx-auto mb-3" />
          <h4 className="text-md font-medium text-slate-600 dark:text-slate-400 mb-2">
            No {framework} configuration
          </h4>
          <p className="text-slate-500 dark:text-slate-500 mb-4 text-sm">
            Set up {framework} automation to run tests with this framework
          </p>
          <Button 
            onClick={onCreate}
            variant="outline"
            size="sm"
            className={`${frameworkBorder} hover:${frameworkBg}`}
          >
            <Plus className="w-4 h-4 mr-2" />
            Configure {framework}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border ${frameworkBorder} dark:border-opacity-50`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${frameworkBg} ${frameworkBorder} border`}>
            <FrameworkIcon className={`w-5 h-5 ${frameworkColor}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold capitalize">{framework}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                {config.framework.toUpperCase()}
              </Badge>
              {statusInfo && (
                <div className="flex items-center gap-1">
                  <statusInfo.icon className={`w-3 h-3 ${statusInfo.color}`} />
                  <span className={`text-xs ${statusInfo.color}`}>{statusInfo.text}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            title="Edit Configuration"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onRun}
            className={`${frameworkColor.replace('text-', 'border-').replace('-600', '-500')} ${frameworkColor} hover:${frameworkColor.replace('text-', 'bg-').replace('-600', '-50')} hover:${frameworkColor.replace('-600', '-600')}`}
            title="Run Test"
          >
            <Play className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Framework and Environment Info */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Monitor className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Browser</span>
            </div>
            <Badge variant="outline" className="font-mono">
              {config.browser}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Globe className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Environment</span>
            </div>
            <Badge variant="outline" className="font-mono">
              {config.environment}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Updated</span>
            </div>
            <Badge variant="outline" className="font-mono">
              {config.updatedAt ? new Date(config.updatedAt).toLocaleDateString() : 'N/A'}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <GitBranch className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Branch</span>
            </div>
            <Badge variant="outline" className="font-mono">
              {config.branch}
            </Badge>
          </div>
        </div>

        {/* Repository Info */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <ExternalLink className="w-4 h-4 text-slate-600" />
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Repository</span>
          </div>
          <a 
            href={config.repository} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline font-mono text-sm break-all"
          >
            {config.repository}
          </a>
        </div>

        {/* Commands */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-slate-600" />
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Commands</span>
          </div>
          <div className="bg-slate-900 dark:bg-slate-800 rounded-lg p-4 space-y-1">
            {config.commands.map((command, index) => (
              <div key={index} className="text-green-400 font-mono text-sm">
                <span className="text-slate-500">$ </span>{command}
              </div>
            ))}
          </div>
        </div>

        {/* Parameters */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Settings className="w-4 h-4 text-slate-600" />
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Parameters</span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {Object.entries(config.parameters)
              .filter(([key]) => {
                // 对于midscene框架，过滤掉yaml_content参数
                if (framework === 'midscene' && key === 'yaml_content') {
                  return false;
                }
                return true;
              })
              .map(([key, value]) => (
              <div key={key} className="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 border border-slate-200 dark:border-slate-600">
                <div className="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide">
                  {key}
                </div>
                <div className="text-sm font-mono text-slate-900 dark:text-slate-100 mt-1 break-all">
                  {value}
                </div>
              </div>
            ))}
          </div>
        </div>


      </div>
    </div>
  );
}
