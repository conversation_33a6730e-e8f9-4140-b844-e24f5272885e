#!/usr/bin/env node

/**
 * 直接查询数据库中的测试步骤数据
 */

const { drizzle } = require('drizzle-orm/better-sqlite3');
const Database = require('better-sqlite3');
const { eq, asc } = require('drizzle-orm');

// 简化的schema定义
const testStep = {
  id: 'id',
  testCaseId: 'testCaseId', 
  stepNumber: 'stepNumber',
  action: 'action',
  expected: 'expected',
  type: 'type',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

async function checkTestSteps() {
  console.log('🔍 检查数据库中的测试步骤...\n');

  try {
    // 连接数据库
    const sqlite = new Database('./data/sqlite.db');
    const db = drizzle(sqlite);

    const testCaseId = 'ff281180-160f-4b67-8248-6abecabb7d8d';
    
    console.log('📋 测试用例ID:', testCaseId);
    console.log('');

    // 直接执行SQL查询
    const steps = sqlite.prepare(`
      SELECT * FROM teststep 
      WHERE testCaseId = ? 
      ORDER BY stepNumber ASC
    `).all(testCaseId);

    console.log('📊 查询结果:');
    console.log('步骤数量:', steps.length);
    console.log('');

    if (steps.length > 0) {
      console.log('✅ 找到以下步骤:');
      steps.forEach((step, index) => {
        console.log(`\n步骤 ${index + 1}:`);
        console.log(`  ID: ${step.id}`);
        console.log(`  步骤号: ${step.stepNumber}`);
        console.log(`  操作: ${step.action}`);
        console.log(`  预期: ${step.expected}`);
        console.log(`  类型: ${step.type}`);
        console.log(`  备注: ${step.notes || '无'}`);
        console.log(`  创建时间: ${new Date(step.createdAt).toLocaleString()}`);
        console.log(`  更新时间: ${new Date(step.updatedAt).toLocaleString()}`);
      });
    } else {
      console.log('❌ 没有找到任何步骤');
    }

    // 检查所有测试用例的步骤
    console.log('\n📋 检查所有测试用例的步骤数量:');
    const allSteps = sqlite.prepare(`
      SELECT testCaseId, COUNT(*) as count 
      FROM teststep 
      GROUP BY testCaseId
    `).all();

    if (allSteps.length > 0) {
      allSteps.forEach(item => {
        console.log(`  测试用例 ${item.testCaseId}: ${item.count} 个步骤`);
      });
    } else {
      console.log('  没有找到任何测试步骤');
    }

    sqlite.close();

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    
    if (error.message.includes('no such table')) {
      console.log('💡 提示: teststep表不存在，请检查数据库初始化');
    } else if (error.message.includes('no such file')) {
      console.log('💡 提示: 数据库文件不存在，请检查路径');
    }
  }
}

// 运行检查
checkTestSteps();
