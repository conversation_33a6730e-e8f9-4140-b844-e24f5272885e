'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  Plus,
  Bot,
  FileText
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function DatasetModule({ 
  testCase, 
  isEditing, 
  onUpdate,
  onAIGenerate 
}: ModuleProps) {
  
  const getTypeColor = (type: string) => {
    const colors = {
      'string': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'number': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'boolean': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'date': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    return colors[type as keyof typeof colors] || colors.string;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-orange-200 dark:border-orange-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Database className="w-5 h-5 text-orange-600" />
            <h2 className="text-xl font-semibold">Test Dataset</h2>
            <Badge variant="outline" className="ml-2">
              {testCase.datasets.length} tables
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={onAIGenerate}
              className="border-purple-200 hover:bg-purple-50"
            >
              <Bot className="w-4 h-4 mr-2" />
              AI Generate Data
            </Button>
            {isEditing && (
              <Button 
                variant="outline" 
                size="sm"
                className="border-orange-200 hover:bg-orange-50"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Table
              </Button>
            )}
          </div>
        </div>

        {testCase.datasets.length > 0 ? (
          <div className="space-y-6">
            {testCase.datasets.map((dataset) => (
              <div key={dataset.id} className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
                <div className="bg-slate-50 dark:bg-slate-800/50 px-4 py-3 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-orange-600" />
                      <h3 className="font-medium text-slate-800 dark:text-slate-200">
                        {dataset.name}
                      </h3>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {dataset.data.length} rows
                    </Badge>
                  </div>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-slate-100 dark:bg-slate-800">
                      <tr>
                        {dataset.columns.map((column) => (
                          <th key={column.name} className="px-4 py-3 text-left">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-slate-700 dark:text-slate-300 text-sm">
                                {column.name}
                              </span>
                              <Badge className={getTypeColor(column.type)} size="sm">
                                {column.type}
                              </Badge>
                              {column.required && (
                                <span className="text-red-500 text-xs">*</span>
                              )}
                            </div>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {dataset.data.slice(0, 5).map((row, index) => (
                        <tr key={index} className="border-t border-slate-200 dark:border-slate-700">
                          {dataset.columns.map((column) => (
                            <td key={column.name} className="px-4 py-3 text-sm text-slate-600 dark:text-slate-400">
                              {row[column.name]?.toString() || '-'}
                            </td>
                          ))}
                        </tr>
                      ))}
                      {dataset.data.length > 5 && (
                        <tr className="border-t border-slate-200 dark:border-slate-700">
                          <td 
                            colSpan={dataset.columns.length} 
                            className="px-4 py-3 text-center text-sm text-slate-500 dark:text-slate-500"
                          >
                            ... and {dataset.data.length - 5} more rows
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Database className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
              No test data defined
            </h3>
            <p className="text-slate-500 dark:text-slate-500 mb-4">
              Create datasets to provide test data for this test case
            </p>
            <div className="flex gap-2 justify-center">
              <Button 
                onClick={onAIGenerate}
                variant="outline"
                className="border-purple-200 hover:bg-purple-50"
              >
                <Bot className="w-4 h-4 mr-2" />
                Generate with AI
              </Button>
              {isEditing && (
                <Button 
                  variant="outline"
                  className="border-orange-200 hover:bg-orange-50"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Dataset
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
