import { ChatOpenAI } from "@langchain/openai";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { MemorySaver } from "@langchain/langgraph";
import { midsceneYamlTool } from "../tools";
import { SystemMessage } from "@langchain/core/messages";
import { generateUUID } from '@/lib/utils';
import { PromptType, getPrompt } from './prompts';
import { detectIntent, memoryManager } from './intentDetector';
import { loadToolsByIntent } from './toolLoader';

// 禁用控制台错误输出
const originalConsoleError = console.error;
console.error = (...args) => {
  // 过滤掉tiktoken相关错误
  if (args[0] && typeof args[0] === 'string' && (
    args[0].includes('tiktoken') || 
    args[0].includes('tokens') ||
    args[0].includes('Unknown model')
  )) {
    return;
  }
  originalConsoleError(...args);
};

// 设置环境变量
process.env.OPENAI_API_KEY = "sk-fe993c92dcc64df59dc08250494935a2";
// https://midscenejs.com/zh/api#agentainumber
// 系统提示，帮助LLM将用户的输入转换为Midscene可以理解的YAML格式
const systemPromptContent = `你是一个网页自动化助手，可以帮助用户通过Midscene执行网页操作。
当用户请求执行网页操作时，你需要将用户的请求转换为Midscene能理解的YAML格式。

Midscene YAML格式示例:
\`\`\`yaml
tasks:
  - name: search
    flow:
      - ai: input 'Headphones' in search box, click search button
      - sleep: 3000

  - name: query
    flow:
      - aiQuery: "{itemTitle: string, price: Number}[], find item in list and corresponding price"
        name: headphones
      - aiNumber: "What is the price of the first headphone?"
      - aiBoolean: "Is the price of the headphones more than 1000?"
      - aiString: "What is the name of the first headphone?"
      - aiLocate: "What is the location of the first headphone?"
\`\`\`

你可以使用以下Midscene命令:
1. ai: 执行自然语言描述的操作，如"点击搜索按钮"
2. aiQuery: 查询页面内容，返回结构化数据
3. aiString: 获取文本内容
4. aiNumber: 获取数字内容
5. aiBoolean: 获取布尔值
6. aiLocate: 定位元素
7. sleep: 等待指定毫秒数
8. aiTap: 点击指定元素，如"aiTap: '页面顶部的登录按钮'"
9. aiHover: 鼠标悬停在指定元素上，如"aiHover: '页面顶部的登录按钮'"
10. aiInput: 在指定元素中输入文本，如"aiInput: ['Hello World', '搜索框']"
11. aiKeyboardPress: 按下键盘上的某个键，如"aiKeyboardPress: ['Enter', '搜索框']"
12. aiScroll: 滚动页面或某个元素，如"aiScroll: [{direction: 'down', distance: 100, scrollType: 'once'}, '表单区域']"
13. aiRightClick: 右键点击某个元素，如"aiRightClick: '页面顶部的文件名称'"
14. aiWaitFor: 等待某个条件达成，如"aiWaitFor: '界面上至少有一个耳机的信息'"
15. aiAssert: 断言某个条件是否满足，如"aiAssert: '商品价格是否正确显示'"

数据提取命令详细说明:
- aiQuery: 从UI提取结构化数据，可以指定返回格式，如对象、数组等
- aiString: 从UI提取文本内容，返回字符串
- aiNumber: 从UI提取数字内容，返回数值
- aiBoolean: 从UI提取布尔值，返回true或false
- aiLocate: 定位元素并返回元素位置信息

交互命令详细说明:
- ai: 自动规划并执行一系列UI操作步骤
- aiTap: 点击指定元素
- aiHover: 鼠标悬停在指定元素上
- aiInput: 在指定元素中输入文本
- aiKeyboardPress: 按下键盘上的某个键
- aiScroll: 滚动页面或某个元素
- aiRightClick: 右键点击某个元素

工具命令详细说明:
- aiWaitFor: 等待某个条件达成，可设置超时时间
- aiAssert: 断言某个条件是否满足，不满足时抛出错误
- sleep: 等待指定毫秒数

请根据用户的请求，生成适当的YAML，并使用midsceneYaml工具执行。
"。`;

// 初始化 LLM（function call 支持的模型） QWEN3
const model = new ChatOpenAI({
  openAIApiKey: "sk-fe993c92dcc64df59dc08250494935a2",
  configuration: {
    baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  },
  modelName: "qwen-max",
  temperature: 0.7,
  streaming: true,
  verbose: false, // 禁用详细日志
  maxRetries: 3, // 增加重试次数
  modelKwargs: {
    // 添加模型特定参数
    model_mapping: {
      "qwen-max": "gpt-3.5-turbo"
    }
  }
});

// 注册工具
const tools = [
  midsceneYamlTool
];

// 初始化记忆存储
const checkpointer = new MemorySaver();

// 创建 function-calling agent
export const streamAgent = createReactAgent({
  llm: model,
  tools,
  checkpointSaver: checkpointer
});

// 生成消息ID的辅助函数
function generateMessageId() {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * 流式代理
 * 用于处理流式响应的代理系统
 */

/**
 * 使用系统提示词调用AI
 * 根据当前会话的意图选择合适的提示词和工具
 */
export const invokeWithSystemPrompt = async (input: any, config?: any) => {
  // 如果没有提供消息，返回空响应
  if (!input.messages || input.messages.length === 0) {
    console.error('没有提供消息');
    return null;
  }
  
  // 获取最后一条用户消息
  const lastUserMessage = input.messages[input.messages.length - 1];
  if (!lastUserMessage || !lastUserMessage.content) {
    console.error('最后一条消息无效或没有内容');
    return null;
  }
  
  // 生成会话ID
  const sessionId = generateUUID();
  
  // 检测用户意图
  const userMessage = String(lastUserMessage.content);
  const detectedIntent = await detectIntent(userMessage, sessionId);
  console.log('检测到用户意图:', detectedIntent);
  
  // 更新会话上下文中的意图
  memoryManager.updateContext(sessionId, { intent: detectedIntent });
  
  // 获取对应的提示词
  const systemPromptContent = getPrompt(detectedIntent);
  
  // 创建系统消息
  const systemMessage = new SystemMessage(systemPromptContent);
  
  // 加载对应的工具
  const toolsArray = loadToolsByIntent(detectedIntent);
  
  // 创建LLM实例
  const model = new ChatOpenAI({
    openAIApiKey: process.env.OPENAI_API_KEY || "sk-fe993c92dcc64df59dc08250494935a2",
    configuration: {
      baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    },
    modelName: "qwen-max",
    temperature: 0.7,
    streaming: config?.streaming || false
  });
  
  // 创建内存保存器
  const memorySaver = new MemorySaver();
  
  // 创建代理
  const agent = createReactAgent({
    llm: model,
    tools: toolsArray,
    checkpointSaver: memorySaver
  });
  
  // 配置选项
  const configOptions = {
    configurable: {
      thread_id: sessionId, // 添加thread_id配置
      session_id: sessionId
    },
    recursion_limit: 10,
    ...config
  };
  
    if (config?.streaming) {
    // 创建流式响应
    const messageId = generateUUID();
    
    // 检查是否是文档创建请求
    const isDocumentRequest = userMessage.includes('写一篇') || 
                             userMessage.includes('创建文章') || 
                             userMessage.includes('生成文章') ||
                             userMessage.includes('写文章');
    
    if (isDocumentRequest) {
      console.log('检测到文档创建请求，直接创建文档');
      
      // 提取文章主题
      let title = '人工智能文章';
      const titleMatch = userMessage.match(/关于(.*?)的文章/) || 
                        userMessage.match(/写一篇(.*?)的文章/) ||
                        userMessage.match(/写一篇关于(.*?)的/);
      
      if (titleMatch && titleMatch[1]) {
        title = titleMatch[1];
      }
      
      // 生成文章内容
      const content = `# ${title}

## 引言
人工智能（AI）是计算机科学领域中的一个重要分支，致力于创造能够模拟人类智能行为的机器。自20世纪50年代以来，AI已经从一个纯理论概念发展成为影响我们日常生活的技术。

## 早期发展 (1950s-1970s)
- 1950年，艾伦·图灵提出了著名的"图灵测试"，这成为评估机器是否具有智能的标准。
- 1956年，达特茅斯会议上，"人工智能"一词被首次提出，标志着AI作为一个独立研究领域的诞生。
- 1960年代，早期的AI研究主要集中在问题解决和符号系统上，如通用问题解决器（GPS）和逻辑理论家程序。

## 第一次AI寒冬 (1970s-1980s)
- 由于技术限制和过高期望导致的失望，AI研究经历了第一次"寒冬"。
- 资金减少，研究进展放缓。

## 专家系统时代 (1980s)
- 1980年代，专家系统的出现重新点燃了AI研究的热情。
- 这些系统在特定领域应用知识规则来解决问题。
- IBM的Deep Blue在1997年战胜国际象棋世界冠军加里·卡斯帕罗夫，展示了AI的潜力。

## 机器学习兴起 (1990s-2000s)
- 随着计算能力的提升和数据可用性的增加，机器学习算法开始显示出强大的能力。
- 支持向量机、决策树等算法被广泛应用。

## 深度学习革命 (2010s至今)
- 2012年，AlexNet在ImageNet竞赛中的胜利标志着深度学习时代的到来。
- 神经网络架构的创新和GPU计算能力的提升推动了这一领域的快速发展。
- 自然语言处理、计算机视觉等领域取得了突破性进展。

## 当前应用
- 虚拟助手（如Siri、Alexa）
- 推荐系统
- 自动驾驶技术
- 医疗诊断
- 金融分析

## 未来展望
人工智能技术将继续发展，可能会在以下方面产生深远影响：
- 通用人工智能的研究
- 人机协作的新模式
- 伦理和监管框架的建立

## 结论
人工智能的发展历程展示了人类不断探索和创新的精神。从最初的理论构想到如今影响各行各业的技术，AI已经走过了漫长而曲折的道路。未来，随着技术的不断进步，AI将继续改变我们的生活和工作方式。`;
      
      // 创建文档ID
      const docId = `doc-${Date.now()}`;
      
      // 创建文档参数
      const documentArgs = {
        title: title,
        kind: 'text',
        content: content
      };

      console.log('创建文档参数:', documentArgs);
      
      // 创建流式响应
      const stream = new TransformStream({
        start(controller) {
          console.log('开始流式响应');
          // 发送开始事件
          controller.enqueue({
            id: messageId,
            object: "chat.completion.chunk",
            created: Date.now(),
            model: "gpt-3.5-turbo",
            choices: [
              {
                index: 0,
                delta: { role: "assistant" },
                finish_reason: null
              }
            ]
          });
          
          console.log('发送tool-invocation部分 - call状态');
          // 发送包含parts的delta，第一部分
          controller.enqueue({
            id: messageId,
            object: "chat.completion.chunk",
            created: Date.now(),
            model: "gpt-3.5-turbo",
            choices: [
              {
                index: 0,
                delta: { 
                  parts: [
                    {
                      type: "tool-invocation",
                      toolInvocation: {
                        toolName: "createDocument",
                        toolCallId: messageId,
                        state: "call",
                        args: {
                          ...documentArgs,
                          isVisible: true // 添加isVisible标志
                        }
                      }
                    }
                  ]
                },
                finish_reason: null
              }
            ]
          });
          
          // 等待一小段时间再发送结果部分
          setTimeout(() => {
            // 首先发送完整的artifact状态，包括内容和可见性
            console.log('首先发送完整的artifact状态');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    artifact: {
                      documentId: docId,
                      kind: documentArgs.kind,
                      title: documentArgs.title,
                      content: documentArgs.content,
                      isVisible: true,
                      status: 'idle'
                    }
                  },
                  finish_reason: null
                }
              ]
            });
            
            // 单独发送content，确保内容被正确设置
            console.log('单独发送content，确保内容被正确设置');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    artifact: {
                      content: documentArgs.content
                    }
                  },
                  finish_reason: null
                }
              ]
            });
            
            // 发送明确的可见性状态
            console.log('发送明确的可见性状态');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    data: {
                      type: "visibility",
                      content: true
                    }
                  },
                  finish_reason: null
                }
              ]
            });
            
            // 发送tool-invocation部分 - result状态
            console.log('发送tool-invocation部分 - result状态');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    parts: [
                      {
                        type: "tool-invocation",
                        toolInvocation: {
                          toolName: "createDocument",
                          toolCallId: messageId,
                          state: "result",
                          result: {
                            id: docId,
                            title: documentArgs.title,
                            kind: documentArgs.kind,
                            content: documentArgs.content,
                            isVisible: true // 添加isVisible标志
                          }
                        }
                      }
                    ]
                  },
                  finish_reason: null
                }
              ]
            });
            
            console.log('发送文本消息');
            // 发送文本消息
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { content: `已创建"${documentArgs.title}"文档，请在右侧查看。` },
                  finish_reason: null
                }
              ]
            });
            
            console.log('发送tool_calls部分');
            // 添加工具调用信息到消息中
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    tool_calls: [
                      {
                        id: messageId,
                        type: "function",
                        function: {
                          name: "createDocument",
                          arguments: JSON.stringify({
                            ...documentArgs,
                            isVisible: true // 添加isVisible标志
                          })
                        }
                      }
                    ]
                  },
                  finish_reason: null
                }
              ]
            });
            
            // 最后再次发送artifact状态，确保可见性和内容设置正确
            console.log('最后再次发送artifact状态');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    artifact: {
                      documentId: docId,
                      kind: documentArgs.kind,
                      title: documentArgs.title,
                      content: documentArgs.content,
                      isVisible: true,
                      status: 'idle'
                    }
                  },
                  finish_reason: null
                }
              ]
            });
            
            console.log('发送结束事件');
            // 发送结束事件
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: {},
                  finish_reason: "stop"
                }
              ]
            });
            
            // 发送明确的finish类型消息
            console.log('发送明确的finish类型消息');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    type: 'finish'
                  },
                  finish_reason: "stop"
                }
              ]
            });
            
            // 额外发送一个明确的idle状态消息
            console.log('额外发送一个明确的idle状态消息');
            controller.enqueue({
              id: messageId,
              object: "chat.completion.chunk",
              created: Date.now(),
              model: "gpt-3.5-turbo",
              choices: [
                {
                  index: 0,
                  delta: { 
                    artifact: {
                      status: 'idle'
                    }
                  },
                  finish_reason: "stop"
                }
              ]
            });
            
            // 在流式响应完成后保存文档
            console.log('流式响应完成，开始保存文档');
            fetch('/api/document', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                id: docId,
                title: documentArgs.title,
                kind: documentArgs.kind,
                content: documentArgs.content
              }),
            })
            .then(response => response.json())
            .then(data => {
              console.log('文档创建成功:', data);
            })
            .catch(error => {
              console.error('文档创建失败:', error);
            });
          }, 500);
        }
      });
      
      return stream.readable;
    } else {
      // 非流式调用
      try {
        return await agent.invoke(
          {
            messages: input.messages
          },
          configOptions
        );
      } catch (error) {
        console.error('代理调用错误:', error);
        throw error;
      }
    }
  } else {
    // 非流式调用
    try {
      return await agent.invoke(
        {
          messages: input.messages
        },
        configOptions
      );
  } catch (error) {
      console.error('代理调用错误:', error);
      throw error;
    }
  }
}; 