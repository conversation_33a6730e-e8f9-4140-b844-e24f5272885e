import { tool } from 'ai';
import { z } from 'zod';
import { createOrUpdateAutomationConfig } from '@/lib/db/queries';
import { TestingService } from '@/lib/services/testing-service';

// 测试步骤Schema
const testStepSchema = z.object({
  step: z.number().describe('步骤编号'),
  action: z.string().describe('操作描述'),
  expected: z.string().describe('预期结果'),
  type: z.enum(['manual', 'automated']).optional().describe('步骤类型'),
  notes: z.string().optional().describe('备注信息')
});

// 更新测试用例工具
export const updateTestCaseTool = tool({
  description: '仅在用户明确要求更新测试用例信息时使用。更新测试用例的基本信息，如名称、描述、优先级等',
  parameters: z.object({
    name: z.string().optional().describe('测试用例名称'),
    description: z.string().optional().describe('测试用例描述'),
    preconditions: z.string().optional().describe('前置条件'),
    priority: z.enum(['high', 'medium', 'low']).optional().describe('优先级'),
    tags: z.array(z.string()).optional().describe('标签列表'),
    nature: z.enum(['functional', 'performance', 'security', 'usability']).optional().describe('测试性质'),
    type: z.enum(['regression', 'smoke', 'integration', 'unit']).optional().describe('测试类型'),
  }),
  execute: async (params) => {
    const updates = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    return `TESTCASE_UPDATE: ${JSON.stringify(updates)}

✅ 测试用例信息已成功更新！

📝 **更新内容**：
${params.name ? `• 名称: ${params.name}` : ''}
${params.description ? `• 描述: ${params.description}` : ''}
${params.preconditions ? `• 前置条件: ${params.preconditions}` : ''}
${params.priority ? `• 优先级: ${params.priority}` : ''}
${params.tags ? `• 标签: ${params.tags.join(', ')}` : ''}
${params.nature ? `• 测试性质: ${params.nature}` : ''}
${params.type ? `• 测试类型: ${params.type}` : ''}

🎯 更新已自动保存到测试用例中，您可以在左侧的"基本信息"模块中查看最新内容。`;
  },
});

// 生成测试步骤工具
export const generateTestStepsTool = tool({
  description: '仅在用户明确要求生成测试步骤时使用。基于测试用例描述生成详细的测试步骤，严格按照用户要求的数量生成',
  parameters: z.object({
    testCaseName: z.string().describe('测试用例名称'),
    description: z.string().describe('测试用例描述'),
    stepCount: z.number().describe('必须生成的步骤数量，严格按照用户要求'),
    includeAutomation: z.boolean().optional().default(false).describe('是否包含自动化步骤'),
    module: z.enum(['login', 'registration', 'search', 'payment', 'profile', 'general']).optional().default('general').describe('功能模块类型'),
    steps: z.array(z.object({
      action: z.string().describe('具体的操作步骤'),
      expected: z.string().describe('预期结果')
    })).describe('AI生成的具体测试步骤列表'),
  }),
  execute: async ({ testCaseName, description, stepCount, includeAutomation, module, steps: providedSteps }) => {
    console.log('🚀 generateTestStepsTool executed with params:', {
      testCaseName,
      description,
      stepCount,
      includeAutomation,
      module,
      providedStepsCount: providedSteps?.length || 0
    });

    const steps = [];

    // 根据不同模块生成专业的测试步骤
    const moduleTemplates = {
      login: [
        { action: '打开登录页面', expected: '登录页面正确显示，包含用户名和密码输入框' },
        { action: '输入有效的用户名和密码', expected: '输入框接受输入，无验证错误' },
        { action: '点击登录按钮', expected: '成功登录并跳转到主页面' },
        { action: '验证登录状态', expected: '显示用户信息，登录状态正确' },
        { action: '测试登录会话', expected: '会话保持有效，页面刷新后仍保持登录状态' },
        { action: '测试登出功能', expected: '成功登出，返回登录页面' },
        { action: '验证登出后状态', expected: '无法访问需要登录的页面' }
      ],
      registration: [
        { action: '打开注册页面', expected: '注册页面正确显示，包含所有必填字段' },
        { action: '填写用户基本信息', expected: '所有字段正确接受输入，格式验证正常' },
        { action: '设置密码', expected: '密码强度验证正常，确认密码匹配' },
        { action: '同意服务条款', expected: '条款链接可点击，复选框可选中' },
        { action: '提交注册信息', expected: '注册成功，收到确认信息' },
        { action: '验证邮箱激活', expected: '收到激活邮件，激活链接有效' },
        { action: '完成账户激活', expected: '账户激活成功，可正常登录' }
      ],
      search: [
        { action: '打开搜索页面', expected: '搜索页面正确显示，搜索框可用' },
        { action: '输入搜索关键词', expected: '搜索框接受输入，显示搜索建议' },
        { action: '执行搜索操作', expected: '搜索结果正确显示，相关性良好' },
        { action: '验证搜索结果', expected: '结果数量准确，分页功能正常' },
        { action: '测试搜索过滤', expected: '过滤条件生效，结果正确筛选' },
        { action: '测试搜索排序', expected: '排序功能正常，结果顺序正确' },
        { action: '验证空搜索结果', expected: '无结果时显示友好提示' }
      ],
      payment: [
        { action: '选择商品并添加到购物车', expected: '商品成功添加，购物车数量更新' },
        { action: '进入结算页面', expected: '结算页面显示正确的商品和价格信息' },
        { action: '选择支付方式', expected: '支付方式选项正确显示，可正常选择' },
        { action: '填写支付信息', expected: '支付表单验证正常，信息格式正确' },
        { action: '确认订单信息', expected: '订单详情准确，总价计算正确' },
        { action: '提交支付请求', expected: '支付处理成功，收到确认信息' },
        { action: '验证支付结果', expected: '订单状态更新，收到支付凭证' }
      ],
      profile: [
        { action: '打开个人资料页面', expected: '个人资料页面正确显示当前信息' },
        { action: '编辑基本信息', expected: '编辑表单可用，当前信息正确填充' },
        { action: '修改个人信息', expected: '信息修改成功，验证规则正常' },
        { action: '上传头像图片', expected: '图片上传成功，头像正确显示' },
        { action: '更改密码', expected: '密码修改成功，新密码生效' },
        { action: '设置隐私选项', expected: '隐私设置保存成功，权限正确应用' },
        { action: '保存所有更改', expected: '所有修改保存成功，页面显示更新后信息' }
      ]
    };

    // 获取模块模板或使用通用模板
    const template = moduleTemplates[module] || [];

    // 优先使用AI提供的具体步骤
    if (providedSteps && providedSteps.length > 0) {
      // 使用AI提供的具体步骤
      for (let i = 0; i < Math.min(providedSteps.length, stepCount); i++) {
        const stepData = {
          step: i + 1,
          action: providedSteps[i].action,
          expected: providedSteps[i].expected,
          type: (includeAutomation && i > Math.floor(stepCount / 2)) ? 'automated' : 'manual'
        };
        steps.push(stepData);
      }

      // 如果AI提供的步骤少于要求的数量，用模板补充
      for (let i = providedSteps.length; i < stepCount; i++) {
        const stepData = {
          step: i + 1,
          action: template[i] ? template[i].action : `执行测试步骤 ${i + 1}：${description}相关的操作验证`,
          expected: template[i] ? template[i].expected : `步骤 ${i + 1} 执行成功，功能表现符合预期`,
          type: (includeAutomation && i > Math.floor(stepCount / 2)) ? 'automated' : 'manual'
        };
        steps.push(stepData);
      }
    } else {
      // 回退到模板生成
      for (let i = 1; i <= stepCount; i++) {
        let stepData;

        if (template[i - 1]) {
          // 使用模块模板
          stepData = {
            step: i,
            action: template[i - 1].action,
            expected: template[i - 1].expected,
            type: (includeAutomation && i > Math.floor(stepCount / 2)) ? 'automated' : 'manual'
          };
        } else {
          // 基于描述生成个性化步骤
          stepData = {
            step: i,
            action: `执行测试步骤 ${i}：${description}相关的操作验证`,
            expected: `步骤 ${i} 执行成功，功能表现符合预期`,
            type: (includeAutomation && i > Math.floor(stepCount / 2)) ? 'automated' : 'manual'
          };
        }

        steps.push(stepData);
      }
    }

    // 返回隐藏的JSON数据和用户友好的消息
    const result = `TESTCASE_STEPS: ${JSON.stringify(steps)}

✅ 已成功为测试用例"${testCaseName}"生成 ${steps.length} 个详细的测试步骤！

📋 **生成的步骤概览**：
${steps.map(step =>
  `• **步骤 ${step.step}**: ${step.action}`
).join('\n')}

🎯 这些步骤已自动添加到您的测试用例中，您可以在左侧的"测试步骤"模块中查看和编辑详细内容。

💡 如果您需要：
- 修改某个步骤的内容
- 添加更多步骤
- 调整步骤顺序
- 设置自动化类型

请告诉我具体需求，我会帮您进一步优化！`;

    console.log('✅ generateTestStepsTool result:', {
      stepsGenerated: steps.length,
      resultLength: result.length,
      containsTestcaseSteps: result.includes('TESTCASE_STEPS:'),
      firstStep: steps[0]
    });

    return result;
  },
});

// 生成自动化配置工具
export const generateAutomationConfigTool = tool({
  description: '为测试用例生成自动化测试配置',
  parameters: z.object({
    testCaseName: z.string().describe('测试用例名称'),
    framework: z.enum(['selenium', 'playwright', 'cypress', 'midscene']).optional().default('midscene').describe('自动化框架'),
    browser: z.enum(['chrome', 'firefox', 'safari', 'edge']).optional().default('chrome').describe('浏览器类型'),
    environment: z.enum(['dev', 'test', 'staging', 'prod']).optional().default('test').describe('测试环境'),
  }),
  execute: async ({ testCaseName, framework, browser, environment }) => {
    const config = {
      repository: 'https://github.com/company/test-automation',
      branch: 'main',
      commands: framework === 'midscene' 
        ? ['npm run midscene', `npm run test:${testCaseName.toLowerCase().replace(/\s+/g, '-')}`]
        : [`npm run ${framework}`, `npm run test:${framework}`],
      parameters: {
        browser,
        environment,
        headless: 'true',
        timeout: '30000',
        retries: '2'
      }
    };

    return `AUTOMATION_CONFIG: ${JSON.stringify(config)}

🤖 已生成自动化配置：

**框架**: ${framework}
**浏览器**: ${browser}
**环境**: ${environment}

**执行命令**:
${config.commands.map(cmd => `• ${cmd}`).join('\n')}

**参数配置**:
${Object.entries(config.parameters).map(([key, value]) => `• ${key}: ${value}`).join('\n')}`;
  },
});

// 生成Midscene配置工具
export const generateMidsceneConfigTool = tool({
  description: '为测试用例生成Midscene自动化配置，包括YAML测试脚本和配置参数',
  parameters: z.object({
    testCaseId: z.string().describe('测试用例ID'),
    url: z.string().describe('要测试的网站URL'),
    title: z.string().describe('测试标题'),
    description: z.string().optional().describe('测试描述和用户需求'),
    repository: z.string().optional().default('https://github.com/company/midscene-automation').describe('代码仓库地址'),
    branch: z.string().optional().default('main').describe('分支名称'),
    browser: z.enum(['chrome', 'firefox', 'safari', 'edge']).optional().default('chrome').describe('浏览器类型'),
    environment: z.enum(['dev', 'test', 'staging', 'prod']).optional().default('test').describe('测试环境'),
  }),
  execute: async ({ testCaseId, url, title, description, repository, branch, browser, environment }) => {
    try {
      console.log(`🔧 开始生成Midscene配置 - testCaseId: ${testCaseId}, title: ${title}`);

      // 创建TestingService实例
      const testingService = new TestingService();

      // 生成YAML配置
      const yamlResult = await testingService.generateTestingYaml(url, title, description);

      if (!yamlResult.success) {
        return `❌ 生成Midscene配置失败：${yamlResult.message}

请提供更详细的测试需求，例如：
- 需要测试哪些具体功能
- 用户操作流程
- 预期的测试结果`;
      }

      // 准备自动化配置数据
      const configData = {
        repository: repository || 'https://github.com/company/midscene-automation',
        branch: branch || 'main',
        commands: [
          'npm install',
          'npm run midscene:test',
          'npm run midscene:report'
        ],
        parameters: {
          browser: browser || 'chrome',
          environment: environment || 'test',
          headless: 'true',
          timeout: '30000',
          viewport: '1920x1080',
          retries: '2',
          yaml_content: yamlResult.yaml
        },
        framework: 'midscene' as const,
        browser: browser || 'chrome',
        environment: environment || 'test',
        isActive: true
      };

      // 保存到数据库
      await createOrUpdateAutomationConfig(testCaseId, configData);
      console.log(`✅ Midscene配置已成功保存到数据库`);

      return `✅ 成功生成并保存Midscene自动化配置！

🎯 **测试目标**: ${title}
🌐 **测试URL**: ${url}
🔧 **框架**: Midscene
🌍 **浏览器**: ${browser}
🏗️ **环境**: ${environment}

📋 **生成的YAML配置**:
\`\`\`yaml
${yamlResult.yaml}
\`\`\`

🚀 **执行命令**:
${configData.commands.map(cmd => `• ${cmd}`).join('\n')}

💾 配置已保存到数据库，您可以在自动化配置模块中查看和管理。

💡 **下一步**:
1. 在自动化配置模块中查看完整配置
2. 点击运行按钮执行测试
3. 根据需要调整配置参数`;

    } catch (error) {
      console.error(`❌ generateMidsceneConfig执行失败:`, error);
      return `❌ 生成Midscene配置时发生错误：${error instanceof Error ? error.message : String(error)}

请稍后重试，或联系技术支持。`;
    }
  },
});

// 分析测试覆盖度工具
export const analyzeTestCoverageTool = tool({
  description: '分析测试用例的覆盖度并提供改进建议',
  parameters: z.object({
    testCase: z.object({
      name: z.string(),
      description: z.string(),
      steps: z.array(testStepSchema),
      tags: z.array(z.string()),
    }).describe('测试用例信息'),
  }),
  execute: async ({ testCase }) => {
    const analysis = {
      coverageScore: Math.floor(Math.random() * 30) + 70, // 70-100分
      strengths: [
        '测试步骤清晰明确',
        '包含了主要的功能验证点',
        '预期结果描述详细'
      ],
      improvements: [
        '建议增加边界值测试',
        '可以添加异常情况处理验证',
        '考虑增加性能相关的验证点'
      ],
      missingAreas: [
        '错误处理测试',
        '数据验证测试',
        '用户体验测试'
      ]
    };

    return `COVERAGE_ANALYSIS: ${JSON.stringify(analysis)}

📊 测试覆盖度分析报告

**覆盖度评分**: ${analysis.coverageScore}/100

**✅ 优势**:
${analysis.strengths.map(item => `• ${item}`).join('\n')}

**🔧 改进建议**:
${analysis.improvements.map(item => `• ${item}`).join('\n')}

**⚠️ 缺失领域**:
${analysis.missingAreas.map(item => `• ${item}`).join('\n')}

建议优先关注缺失领域，以提高测试的全面性和有效性。`;
  },
});

// 生成模块化内容工具
export const generateModuleContentTool = tool({
  description: '根据不同模块生成专业的测试内容，支持多种模块类型',
  parameters: z.object({
    module: z.enum(['testcase', 'automation', 'data', 'requirements', 'reporting']).describe('目标模块类型'),
    contentType: z.enum(['steps', 'config', 'dataset', 'mapping', 'report']).describe('内容类型'),
    count: z.number().optional().default(5).describe('生成内容的数量'),
    context: z.object({
      name: z.string().describe('测试用例或项目名称'),
      description: z.string().describe('描述信息'),
      requirements: z.array(z.string()).optional().describe('相关需求'),
    }).describe('上下文信息'),
  }),
  execute: async ({ module, contentType, count, context }) => {
    const modulePrompts = {
      testcase: {
        steps: `基于"${context.name}"生成${count}个详细的测试步骤，每个步骤包含操作和预期结果`,
        config: `为"${context.name}"生成测试配置，包括环境设置、数据准备和执行参数`,
      },
      automation: {
        config: `生成"${context.name}"的自动化测试配置，包括框架选择、脚本结构和CI/CD集成`,
        steps: `生成${count}个自动化测试步骤，包含定位器、操作和断言`,
      },
      data: {
        dataset: `为"${context.name}"生成${count}组测试数据，包括正常、边界和异常数据`,
        config: `生成数据驱动测试配置，包括数据源、参数化和数据清理`,
      },
      requirements: {
        mapping: `生成"${context.name}"的需求-测试用例映射关系，确保覆盖度`,
        report: `生成需求覆盖度分析报告，包括覆盖率和风险评估`,
      },
      reporting: {
        report: `生成"${context.name}"的测试执行报告模板，包括结果统计和分析`,
        config: `生成报告配置，包括指标定义、图表类型和输出格式`,
      }
    };

    const prompt = modulePrompts[module]?.[contentType] || `生成${module}模块的${contentType}内容`;

    // 根据模块和内容类型生成具体内容
    let generatedContent = [];

    if (module === 'testcase' && contentType === 'steps') {
      for (let i = 1; i <= count; i++) {
        generatedContent.push({
          step: i,
          action: `执行${context.name}相关的测试操作 ${i}`,
          expected: `验证步骤 ${i} 的预期结果符合需求`,
          priority: i <= 3 ? 'high' : 'medium'
        });
      }
    } else if (module === 'automation' && contentType === 'config') {
      generatedContent = {
        framework: 'midscene',
        browser: 'chrome',
        environment: 'test',
        scripts: Array.from({length: count}, (_, i) => `test_${context.name.toLowerCase()}_${i + 1}.js`),
        parameters: {
          timeout: 30000,
          retries: 2,
          headless: true
        }
      };
    } else if (module === 'data' && contentType === 'dataset') {
      for (let i = 1; i <= count; i++) {
        generatedContent.push({
          id: i,
          type: i === 1 ? 'valid' : i === count ? 'invalid' : 'boundary',
          data: `测试数据集 ${i} for ${context.name}`,
          expected: `数据集 ${i} 的预期行为`
        });
      }
    }

    return `MODULE_CONTENT: ${JSON.stringify({ module, contentType, content: generatedContent })}

🎯 已为 **${module}** 模块生成 **${contentType}** 内容：

**提示词**: ${prompt}

**生成内容**:
${JSON.stringify(generatedContent, null, 2)}

这些内容已根据${module}模块的专业要求进行优化，可以直接使用或进一步定制。`;
  },
});

// 导出所有工具
export const testCaseTools = {
  updateTestCase: updateTestCaseTool,
  generateTestSteps: generateTestStepsTool,
  generateAutomationConfig: generateAutomationConfigTool,
  generateMidsceneConfig: generateMidsceneConfigTool,
  analyzeTestCoverage: analyzeTestCoverageTool,
  generateModuleContent: generateModuleContentTool,
};
