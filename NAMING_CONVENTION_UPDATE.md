# 文件命名规范更新

## 🎯 问题识别

您正确地指出了项目中文件命名不一致的问题。经过分析，发现项目中存在两种命名风格：

1. **PascalCase**: `app/test-case/[id]/components/` 目录下的组件
2. **kebab-case**: `components/` 目录下的全局组件

## 📋 现代前端最佳实践

根据现代前端开发的最佳实践和社区标准：

### ✅ 推荐使用 kebab-case 的原因

1. **跨平台兼容性**: 避免大小写敏感的文件系统问题
2. **URL友好**: 更适合Web环境和路由
3. **可读性**: 更容易阅读和理解
4. **一致性**: 与CSS类名、HTML属性等保持一致
5. **工具支持**: 大多数现代工具和框架推荐kebab-case

### 📚 业界标准参考

- **Vue.js**: 官方推荐kebab-case
- **Angular**: 官方推荐kebab-case  
- **Next.js**: 页面文件使用小写/kebab-case
- **Nuxt.js**: 推荐kebab-case
- **大多数开源项目**: 使用kebab-case

## 🔧 已完成的更新

### 重命名的文件

| 原文件名 (PascalCase) | 新文件名 (kebab-case) |
|---------------------|---------------------|
| `BaseAutomationConfig.tsx` | `base-automation-config.tsx` |
| `MidsceneConfig.tsx` | `midscene-config.tsx` |
| `PlaywrightConfig.tsx` | `playwright-config.tsx` |
| `CypressConfig.tsx` | `cypress-config.tsx` |
| `SeleniumConfig.tsx` | `selenium-config.tsx` |

### 更新的导入语句

```typescript
// 之前 (PascalCase)
import MidsceneConfig from './automation/MidsceneConfig';
import PlaywrightConfig from './automation/PlaywrightConfig';
import CypressConfig from './automation/CypressConfig';
import SeleniumConfig from './automation/SeleniumConfig';

// 现在 (kebab-case)
import MidsceneConfig from './automation/midscene-config';
import PlaywrightConfig from './automation/playwright-config';
import CypressConfig from './automation/cypress-config';
import SeleniumConfig from './automation/selenium-config';
```

## 📁 项目命名规范建议

### 组件文件
```
✅ 推荐: kebab-case
- user-profile.tsx
- automation-config.tsx
- test-case-list.tsx

❌ 避免: PascalCase
- UserProfile.tsx
- AutomationConfig.tsx
- TestCaseList.tsx
```

### 工具函数和Hooks
```
✅ 推荐: kebab-case
- use-local-storage.ts
- format-date.ts
- api-client.ts

❌ 避免: camelCase
- useLocalStorage.ts
- formatDate.ts
- apiClient.ts
```

### 页面文件 (Next.js App Router)
```
✅ 推荐: 小写
- page.tsx
- layout.tsx
- loading.tsx
- error.tsx

✅ 动态路由: kebab-case
- [test-case-id]/page.tsx
- [user-profile]/layout.tsx
```

### 目录命名
```
✅ 推荐: kebab-case
- test-case/
- user-management/
- automation-config/

❌ 避免: PascalCase
- TestCase/
- UserManagement/
- AutomationConfig/
```

## 🔍 检查清单

- [x] 重命名automation组件文件为kebab-case
- [x] 更新所有导入语句
- [x] 验证编译无错误
- [x] 删除旧的PascalCase文件
- [ ] 考虑重命名其他不一致的文件（可选）

## 🚀 后续建议

1. **逐步迁移**: 可以考虑将其他PascalCase组件文件逐步迁移到kebab-case
2. **ESLint规则**: 添加文件命名的ESLint规则来强制一致性
3. **团队约定**: 在团队中建立明确的命名规范文档

## 📖 参考资源

- [Vue.js Style Guide - Component Files](https://vuejs.org/style-guide/rules-strongly-recommended.html#component-files)
- [Angular Style Guide - File Names](https://angular.io/guide/styleguide#file-names)
- [Next.js File Conventions](https://nextjs.org/docs/app/api-reference/file-conventions)
- [Google JavaScript Style Guide](https://google.github.io/styleguide/jsguide.html#file-names)

## ✨ 总结

感谢您指出这个重要的问题！统一的命名规范不仅提高了代码的可维护性，还避免了跨平台开发中的潜在问题。现在automation组件已经使用了更符合现代标准的kebab-case命名规范。
