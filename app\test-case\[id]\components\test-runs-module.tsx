'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  SkipForward,
  User,
  Calendar,
  Timer,
  Globe,
  RefreshCw,
  TrendingUp
} from 'lucide-react';
import { ModuleProps } from '../types';

interface TestRun {
  id: string;
  runDate: string;
  status: 'passed' | 'failed' | 'running' | 'skipped';
  duration: number;
  environment: string;
  executor: string;
  logs?: string;
  screenshots: string[];
  results: Array<{
    stepId: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    error?: string;
  }>;
}

// 状态图标映射
const statusIcons = {
  passed: CheckCircle,
  failed: XCircle,
  running: Clock,
  skipped: SkipForward
};

// 状态颜色映射
const statusColors = {
  passed: 'text-green-600 bg-green-50 border-green-200',
  failed: 'text-red-600 bg-red-50 border-red-200',
  running: 'text-blue-600 bg-blue-50 border-blue-200',
  skipped: 'text-gray-600 bg-gray-50 border-gray-200'
};

export default function TestRunsModule({ testCase, isEditing, onUpdate }: ModuleProps) {
  const [testRuns, setTestRuns] = useState<TestRun[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从数据库加载测试运行数据
  const loadTestRuns = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/test-runs?testCaseId=${testCase.id}&limit=20`);
      if (!response.ok) {
        throw new Error('Failed to load test runs');
      }
      
      const data = await response.json();
      setTestRuns(data);
    } catch (err) {
      console.error('Error loading test runs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load test runs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTestRuns();
  }, [testCase.id]);

  // 计算统计数据
  const stats = {
    total: testRuns.length,
    passed: testRuns.filter(run => run.status === 'passed').length,
    failed: testRuns.filter(run => run.status === 'failed').length,
    running: testRuns.filter(run => run.status === 'running').length,
    skipped: testRuns.filter(run => run.status === 'skipped').length,
    successRate: testRuns.length > 0 ? Math.round((testRuns.filter(run => run.status === 'passed').length / testRuns.length) * 100) : 0
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 格式化持续时间
  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">加载测试运行记录...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-600 mb-2">加载失败</h3>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={loadTestRuns} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总运行次数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Play className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">成功率</p>
                <p className="text-2xl font-bold text-green-600">{stats.successRate}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">通过/失败</p>
                <p className="text-2xl font-bold">
                  <span className="text-green-600">{stats.passed}</span>
                  <span className="text-gray-400 mx-1">/</span>
                  <span className="text-red-600">{stats.failed}</span>
                </p>
              </div>
              <div className="flex space-x-1">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <XCircle className="w-4 h-4 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">运行中/跳过</p>
                <p className="text-2xl font-bold">
                  <span className="text-blue-600">{stats.running}</span>
                  <span className="text-gray-400 mx-1">/</span>
                  <span className="text-gray-600">{stats.skipped}</span>
                </p>
              </div>
              <div className="flex space-x-1">
                <Clock className="w-4 h-4 text-blue-600" />
                <SkipForward className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 运行历史 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              运行历史
            </CardTitle>
            <Button onClick={loadTestRuns} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {testRuns.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">暂无运行记录</h3>
              <p className="text-gray-500">此测试用例还没有执行过</p>
            </div>
          ) : (
            <div className="space-y-4">
              {testRuns.map((run) => {
                const StatusIcon = statusIcons[run.status];
                return (
                  <div
                    key={run.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Badge className={statusColors[run.status]}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {run.status === 'passed' && '通过'}
                          {run.status === 'failed' && '失败'}
                          {run.status === 'running' && '运行中'}
                          {run.status === 'skipped' && '跳过'}
                        </Badge>
                        <span className="font-medium">运行 #{run.id.slice(-8)}</span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(run.runDate)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Timer className="w-4 h-4" />
                          {formatDuration(run.duration)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">环境:</span>
                        <Badge variant="outline">{run.environment}</Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">执行者:</span>
                        <span>{run.executor}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">步骤结果:</span>
                        <div className="flex gap-1">
                          {run.results.map((result, index) => (
                            <div
                              key={index}
                              className={`w-3 h-3 rounded-sm ${
                                result.status === 'passed' ? 'bg-green-500' :
                                result.status === 'failed' ? 'bg-red-500' :
                                'bg-gray-400'
                              }`}
                              title={`步骤 ${index + 1}: ${result.status}${result.error ? ` - ${result.error}` : ''}`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    {run.logs && (
                      <div className="mt-3 p-3 bg-gray-100 rounded text-sm">
                        <strong>日志:</strong> {run.logs}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
