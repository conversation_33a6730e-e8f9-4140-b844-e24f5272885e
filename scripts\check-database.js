#!/usr/bin/env node

/**
 * 检查数据库文件和表结构
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

async function checkDatabase() {
  console.log('🔍 检查数据库状态...\n');

  const dbPath = './data/sqlite.db';
  
  // 检查数据库文件是否存在
  if (!fs.existsSync(dbPath)) {
    console.log('❌ 数据库文件不存在:', dbPath);
    return;
  }

  const stats = fs.statSync(dbPath);
  console.log('✅ 数据库文件存在');
  console.log('📁 文件路径:', path.resolve(dbPath));
  console.log('📊 文件大小:', (stats.size / 1024).toFixed(2), 'KB');
  console.log('📅 修改时间:', stats.mtime.toLocaleString());
  console.log('');

  try {
    // 连接数据库
    const db = new Database(dbPath);
    
    // 获取所有表
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name
    `).all();

    console.log('📋 数据库中的表:');
    if (tables.length > 0) {
      tables.forEach(table => {
        console.log(`  - ${table.name}`);
      });
    } else {
      console.log('  没有找到任何表');
    }
    console.log('');

    // 检查是否有teststep表
    const testStepTable = tables.find(t => t.name === 'teststep');
    if (testStepTable) {
      console.log('✅ teststep表存在');
      
      // 获取表结构
      const schema = db.prepare(`PRAGMA table_info(teststep)`).all();
      console.log('📊 teststep表结构:');
      schema.forEach(col => {
        console.log(`  ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`);
      });
      
      // 获取记录数量
      const count = db.prepare(`SELECT COUNT(*) as count FROM teststep`).get();
      console.log(`📈 记录数量: ${count.count}`);
      
      // 检查特定测试用例的步骤
      const testCaseId = 'ff281180-160f-4b67-8248-6abecabb7d8d';
      const steps = db.prepare(`
        SELECT * FROM teststep 
        WHERE testCaseId = ? 
        ORDER BY stepNumber
      `).all(testCaseId);
      
      console.log(`\n🎯 测试用例 ${testCaseId} 的步骤:`);
      if (steps.length > 0) {
        steps.forEach((step, index) => {
          console.log(`  步骤 ${index + 1}: ${step.action.substring(0, 50)}...`);
        });
      } else {
        console.log('  没有找到步骤');
      }
      
    } else {
      console.log('❌ teststep表不存在');
      
      // 检查是否有类似的表名
      const similarTables = tables.filter(t => 
        t.name.toLowerCase().includes('step') || 
        t.name.toLowerCase().includes('test')
      );
      
      if (similarTables.length > 0) {
        console.log('🔍 找到类似的表:');
        similarTables.forEach(table => {
          console.log(`  - ${table.name}`);
        });
      }
    }

    db.close();

  } catch (error) {
    console.error('❌ 数据库操作失败:', error.message);
  }
}

// 运行检查
checkDatabase();
