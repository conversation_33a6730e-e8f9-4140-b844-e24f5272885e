import {
  appendClientMessage,
  createDataStream,
  smoothStream,
  streamText
} from 'ai';
import { type UserType } from '@/app/auth/auth';
import { type RequestHints, systemPrompt } from '@/lib/ai/prompts';
import { generateUUID, convertToUIMessages } from '@/lib/utils';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { executeAutomationTesting } from '@/lib/ai/tools/execute-testing';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { postRequestBodySchema, type PostRequestBody } from './schema';
import { geolocation } from '@vercel/functions';
import { ChatSDKError } from '@/lib/errors';
import { saveChat, getChatById, saveMessages, getMessagesByChatId, createStreamId, getDocumentById } from '@/lib/db/queries';
import { generateTitleFromUserMessage } from '../../actions';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';
import { Session } from 'next-auth';
import { aiLogger } from '@/lib/logger';

// 创建模块专用的日志记录器
const logger = aiLogger.child('chat-api');

// 估算文本的token数量
function estimateTokens(text: string): { tokens: number, englishChars: number, chineseChars: number } {
  // 计算英文和中文字符数
  const englishChars = text.replace(/[^\x00-\xff]/g, '').length;
  const chineseChars = text.length - englishChars;
  
  // 估算token数量 (简单估算: 英文约1个token/4字符，中文约1个token/1.5字符)
  const tokens = Math.ceil(englishChars / 4 + chineseChars / 1.5);
  
  return { tokens, englishChars, chineseChars };
}

// 估算整个对话历史的token数量
function estimateConversationTokens(messages: any[]): { total: number, byRole: Record<string, number> } {
  const byRole: Record<string, number> = { user: 0, assistant: 0, system: 0 };
  let total = 0;
  
  for (const msg of messages) {
    let messageText = '';
    
    if (msg.parts) {
      // 处理parts数组或字符串
      if (typeof msg.parts === 'string') {
        try {
          const parts = JSON.parse(msg.parts);
          for (const part of parts) {
            if (part.type === 'text') {
              messageText += part.text + ' ';
            }
          }
        } catch {
          messageText = msg.parts;
        }
      } else if (Array.isArray(msg.parts)) {
        for (const part of msg.parts) {
          if (part.type === 'text') {
            messageText += part.text + ' ';
          }
        }
      }
    } else if (msg.content) {
      messageText = msg.content;
    }
    
    const { tokens } = estimateTokens(messageText);
    const role = msg.role || 'unknown';
    
    if (!byRole[role]) {
      byRole[role] = 0;
    }
    
    byRole[role] += tokens;
    total += tokens;
  }
  
  return { total, byRole };
}

export const maxDuration = 60;

// 从历史消息中提取最近的文档ID
async function extractLatestDocumentId(messages: any[]): Promise<string | null> {
  // 按创建时间降序排序消息
  const sortedMessages = [...messages].sort((a, b) => {
    const timeA = a.createdAt instanceof Date ? a.createdAt.getTime() : a.createdAt;
    const timeB = b.createdAt instanceof Date ? b.createdAt.getTime() : b.createdAt;
    return timeB - timeA; // 降序排序
  });
  
  // 遍历消息查找document-reference
  for (const msg of sortedMessages) {
    // 确保parts是一个数组
    const parts = Array.isArray(msg.parts) ? msg.parts : 
                 (typeof msg.parts === 'string' ? JSON.parse(msg.parts) : []);
    
    // 查找document-reference类型的部分
    const docRefPart = parts.find((part: any) => part.type === 'document-reference');
    if (docRefPart && docRefPart.document_id) {
      logger.info(`找到文档ID: ${docRefPart.document_id}`);
      return docRefPart.document_id;
    }
  }
  
  logger.info('未找到文档ID');
  return null;
}

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (error) {
    logger.error(`请求体解析失败: ${error instanceof Error ? error.message : String(error)}`);
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    // 获取 session
    const session = await getServerSession(authConfig);
    const user = (session && typeof session === 'object' && session !== null && 'user' in session && (session as any).user)
      ? (session as any).user
      : { id: 'anonymous-user', name: 'Anonymous User', email: '<EMAIL>', type: 'guest' };

    const { id: chatId, message, selectedChatModel, selectedVisibilityType } = requestBody;
    const visibility = selectedVisibilityType || 'private';
    let title = (requestBody as any).title;
    if (!title) {
      title = await generateTitleFromUserMessage({ message });
    }
    
    logger.info(`处理聊天请求: chatId=${chatId}, 用户=${user.id}, 模型=${selectedChatModel}`);
    
    // 1. 检查 chat 是否存在，不存在则保存
    let chat = await getChatById({ id: chatId });
    if (!chat) {
      logger.info(`创建新聊天: chatId=${chatId}, 标题=${title}, 可见性=${visibility}`);
      await saveChat({
        id: chatId,
        userId: user.id,
        title,
        visibility,
      });
    }
    // 2. 保存本轮消息
    const attachments = (message as any).attachments || (message as any).experimental_attachments || [];
    const dbMessage = {
      id: generateUUID(),
      chatId,
      role: message.role,
      parts: [{ type: 'text', text: message.content }], // 修正为 text
      attachments,
      createdAt: new Date(),
    };
    await saveMessages({ messages: [dbMessage] });
    logger.debug(`用户消息已保存: messageId=${dbMessage.id}`);

    // 3. 获取所有消息并转换为UI消息格式
    const messagesFromDb = await getMessagesByChatId({ id: chatId });
    let uiMessages = convertToUIMessages(messagesFromDb);

    logger.debug(`初始uiMessages: ${uiMessages.length}条消息`);

    // 4. 提取最近的文档ID
    let documentId = await extractLatestDocumentId(messagesFromDb);
    
    // 5. 如果找到文档ID，获取文档内容并构造新消息
    if (documentId) {
      // 获取文档内容 - 使用getDocumentById替代getDocumentsById
      const latestDocument = await getDocumentById({ id: documentId });
      
      if (latestDocument) {
        const documentContent = latestDocument.content;
        
        // 如果文档内容不太长，构造一个新的消息
        if (documentContent && documentContent.length < 2000) {
          logger.info(`文档内容长度适合拼接: ${documentContent.length}字符, 文档ID=${documentId}`);
          
          // 根据文档类型格式化内容
          let formattedContent = documentContent;
          
          // 构造包含文档内容的消息
          const documentMessage = {
            id: generateUUID(),
            chatId,
            role: 'user',
            parts: [{ 
              type: 'text', 
              text: `以下是之前创建的文档"${latestDocument.title}"的内容:\n\n${formattedContent}` 
            }],
            attachments: [],
            createdAt: new Date(),
          };
          
          // 保存文档内容消息
          await saveMessages({ messages: [documentMessage] });
          logger.info(`文档内容消息已保存: messageId=${documentMessage.id}`);
          
          // 重新获取所有消息，包括新添加的文档内容消息
          const updatedMessagesFromDb = await getMessagesByChatId({ id: chatId });
          uiMessages = convertToUIMessages(updatedMessagesFromDb);
          
          logger.debug(`更新后的uiMessages: ${uiMessages.length}条消息`);
        } else {
          logger.info(`文档内容过长，不适合拼接: ${documentContent?.length || 0}字符, 文档ID=${documentId}`);
        }
      } else {
        logger.warn(`未找到ID为${documentId}的文档`);
      }
    }

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };
    
    // 确保我们传递正确的消息给AI
    logger.info(`准备发送给AI的消息数量: ${JSON.stringify(uiMessages)}`);
    
    // 估算整个对话的token数量
    const tokenEstimation = estimateConversationTokens(uiMessages);
    logger.info(`对话历史token估算: 总计~${tokenEstimation.total} (用户:${tokenEstimation.byRole.user || 0}, AI:${tokenEstimation.byRole.assistant || 0}, 系统:${tokenEstimation.byRole.system || 0})`);
    
    // 记录消息内容和估算token数量
    try {
      // 获取最后一条用户消息
      const lastUserMessage = uiMessages.filter(m => m.role === 'user').pop();
      if (lastUserMessage) {
        // 提取消息内容
        const content = lastUserMessage.parts
          .filter(part => part.type === 'text')
          .map(part => part.text)
          .join(' ');
        
        // 使用estimateTokens函数估算token
        const { tokens, englishChars, chineseChars } = estimateTokens(content);
        
        // 记录内容摘要和token估算
        logger.info(`最后用户消息(前100字符): ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`);
        logger.info(`估算token数量: ~${tokens} (英文${englishChars}字符, 中文${chineseChars}字符)`);
        
        // 如果消息很长，记录更详细的信息
        if (content.length > 500) {
          logger.debug(`消息长度: ${content.length}字符, 估算token: ~${tokens}`);
        }
      }
    } catch (error) {
      logger.warn(`估算token失败: ${error instanceof Error ? error.message : String(error)}`);
    }
 
    const stream = createDataStream({
      execute: (dataStream) => {
        try {
          logger.info('开始生成流式响应...');
          const result = streamText({
            // 使用qwen3作为默认模型
            model: myProvider.languageModel('qwen3'),
            system: systemPrompt({ selectedChatModel, requestHints }),
            messages: uiMessages,
            maxSteps: 5,
            experimental_activeTools:
              selectedChatModel === 'chat-model-reasoning'
                ? []
                : [
                    'getWeather',
                    'createDocument',
                    'updateDocument',
                    'requestSuggestions',
                    'executeAutomationTesting',
                  ],
            experimental_transform: smoothStream({ chunking: 'word' }),
            experimental_generateMessageId: generateUUID,
            tools: {
              getWeather,
              createDocument: createDocument({ session: session as Session, dataStream, chatId }),
              updateDocument: updateDocument({ session: session as Session, dataStream }),
              requestSuggestions: requestSuggestions({
                session: session as Session,
                dataStream,
              }),
              executeAutomationTesting: executeAutomationTesting({
                session: session as Session,
                dataStream,
                chatId,
              }),
            },
            onFinish: () => {
              logger.info('流式响应生成完成');
            },
            experimental_telemetry: {
              isEnabled: isProductionEnvironment,
              functionId: 'stream-text',
            },
          });

          result.consumeStream();

          // 收集AI响应内容
          let aiResponseText = '';
          let hasToolResponse = false;
          
          result.textStream.pipeTo(
            new WritableStream({
              write(chunk) {
                aiResponseText += chunk;
                
                // 检查是否包含工具响应的标记
                if (chunk.includes('"type":"document-reference"') || 
                    chunk.includes('"type":"tool-invocation"') ||
                    chunk.includes('"toolInvocation"')) {
                  hasToolResponse = true;
                }
              },
              close() {
                // 流结束时保存AI响应
                try {
                  // 估算AI响应的token数量
                  const { tokens, englishChars, chineseChars } = estimateTokens(aiResponseText);
                  logger.info(`AI响应完成, 估算token数量: ~${tokens} (长度: ${aiResponseText.length}字符)`);
                  logger.debug(`AI响应内容(前100字符): ${aiResponseText.substring(0, 100)}${aiResponseText.length > 100 ? '...' : ''}`);
                  
                  // 检查是否已经有工具生成的文档消息
                  if (hasToolResponse) {
                    logger.info('检测到工具响应，尝试解析为JSON');
                    try {
                      // 尝试解析为JSON
                      let parts;
                      try {
                        parts = JSON.parse(aiResponseText);
                        logger.info('成功解析为JSON数组');
                      } catch (e) {
                        // 如果解析失败，可能是因为不是完整的JSON
                        // 尝试提取JSON部分
                        const startIndex = aiResponseText.indexOf('[');
                        const endIndex = aiResponseText.lastIndexOf(']');
                        
                        if (startIndex !== -1 && endIndex !== -1 && startIndex < endIndex) {
                          const jsonString = aiResponseText.substring(startIndex, endIndex + 1);
                          parts = JSON.parse(jsonString);
                          logger.info('从文本中提取并解析JSON成功');
                        } else {
                          throw new Error('无法找到完整的JSON数组');
                        }
                      }
                      
                      // 如果成功解析为JSON数组，使用它作为parts
                      if (Array.isArray(parts)) {
                        const assistantMessage = {
                          id: generateUUID(),
                          chatId,
                          role: 'assistant',
                          parts: JSON.stringify(parts),
                          attachments: JSON.stringify([]),
                          createdAt: new Date(),
                        };
                        
                        saveMessages({ messages: [assistantMessage] })
                          .then(() => logger.info(`包含文档的AI消息已保存: messageId=${assistantMessage.id}`))
                          .catch(err => logger.error(`保存AI消息失败: ${err instanceof Error ? err.message : String(err)}`));
                        
                        return; // 已保存消息，不需要继续
                      }
                    } catch (parseError) {
                      logger.error(`解析JSON失败: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
                      // 解析失败，继续使用普通文本格式
                    }
                  }
                  
                  // 普通文本消息
                  const assistantMessage = {
                    id: generateUUID(),
                    chatId,
                    role: 'assistant',
                    parts: JSON.stringify([{ type: 'text', text: aiResponseText }]),
                    attachments: JSON.stringify([]),
                    createdAt: new Date(),
                  };
                  
                  saveMessages({ messages: [assistantMessage] })
                    .then(() => logger.info(`AI消息已保存: messageId=${assistantMessage.id}`))
                    .catch(err => logger.error(`保存AI消息失败: ${err instanceof Error ? err.message : String(err)}`));
                } catch (error) {
                  logger.error(`处理AI响应时出错: ${error instanceof Error ? error.message : String(error)}`);
                }
              }
            })
          );

          result.mergeIntoDataStream(dataStream, {
            sendReasoning: true,
          });
        } catch (error) {
          logger.error(`执行流处理时出错: ${error instanceof Error ? error.message : String(error)}`);
          dataStream.writeData({
            type: 'text',
            text: '抱歉，处理您的请求时出错了。请稍后再试。',
          });
        }
      },
      onError: (error) => {
        logger.error(`流错误: ${error instanceof Error ? error.message : String(error)}`);
        return '抱歉，处理您的请求时出错了。请稍后再试。';
      },
    });

    return new Response(stream);
  } catch (error) {
    logger.error(`处理请求时出错: ${error instanceof Error ? error.stack || error.message : String(error)}`);
    return new Response('An unexpected error occurred', { status: 500 });
  }
}

export async function GET(request: Request) {
  logger.debug('收到GET请求');
  return new Response('Hello, world!', { status: 200 });
}

export async function DELETE(request: Request) {
  logger.warn('收到不支持的DELETE请求');
  return new Response('Delete operation not supported', { status: 405 });
}
