# 强制格式约束修复

## 🐛 问题持续存在

尽管之前已经更新了系统提示，但AI仍然在生成嵌套的有序列表：

```markdown
1. 打开浏览器并访问网易深圳网站
   1. 预期结果: 网页加载成功...  ← 仍然错误

2. 点击顶部"政务"菜单
   1. 预期结果: 政务页面加载成功...  ← 仍然错误
```

**根本原因**: AI模型没有严格遵守之前的格式要求，需要更强制性的约束。

## 🔧 强化修复方案

### 1. 系统提示开头强制约束

#### 在最开始添加强制警告
```typescript
const systemPrompt = `你是一个专业的测试用例助手，专门帮助用户创建、优化和管理测试用例。

🚨 强制格式约束：生成测试步骤时，预期结果必须使用项目符号格式(- **预期结果**:)，绝对禁止使用嵌套有序列表(1. 预期结果:)！

当前测试用例信息：
...
```

### 2. 强化格式要求语言

#### 修改前
```typescript
**重要格式要求**：
- 在回复中描述步骤时，不要使用"步骤 1:"、"步骤 2:"等标识
- 直接使用Markdown有序列表格式（1. 2. 3.）
- 预期结果必须使用项目符号格式（- **预期结果**:），不要使用嵌套的有序列表
- 严格按照示例格式执行，避免任何形式的重复序号
```

#### 修改后
```typescript
**严格格式要求 - 必须遵守**：
- 绝对禁止使用"步骤 1:"、"步骤 2:"等标识
- 绝对禁止使用嵌套的有序列表（如"1. 预期结果:"）
- 预期结果必须且只能使用项目符号格式：- **预期结果**:
- 任何偏离示例格式的行为都是错误的
```

### 3. 强化示例说明

#### 修改前
```typescript
**严格按照以下格式示例执行，不允许任何偏差**：

示例：
用户："为网易深圳网站生成3个测试步骤"
你的回复应该包含：
```

#### 修改后
```typescript
**强制格式模板 - 必须完全按照此格式**：

用户："为网易深圳网站生成3个测试步骤"
你的回复必须完全复制以下格式，只改变具体内容：
```

### 4. 强化禁止格式说明

#### 修改前
```typescript
**禁止使用的错误格式**：
❌ 1. 操作描述
    1. 预期结果: ...  (禁止嵌套有序列表)

❌ 1. 步骤 1: 操作描述  (禁止重复序号标识)

✅ 正确格式：
1. 操作描述
   - **预期结果**: ...  (使用项目符号)
```

#### 修改后
```typescript
**绝对禁止的错误格式 - 如果使用将被视为错误**：
❌ 错误示例1：
1. 操作描述
   1. 预期结果: ...  ← 这是错误的！绝对禁止！

❌ 错误示例2：
1. 步骤 1: 操作描述  ← 这是错误的！绝对禁止！

✅ 唯一正确格式：
1. 操作描述
   - **预期结果**: ...  ← 只有这样才是正确的！

记住：预期结果前面必须是短横线(-)，不是数字！
```

## 📊 强化策略

### 1. 语言强度升级
- "重要" → "强制"
- "不要" → "绝对禁止"
- "应该" → "必须"
- "建议" → "要求"

### 2. 视觉强化
- 添加警告emoji (🚨)
- 使用强调性语言
- 重复关键约束

### 3. 位置优化
- 在系统提示开头立即声明
- 在示例前强调
- 在禁止格式中重申

### 4. 具体化约束
- 明确指出错误示例
- 提供唯一正确格式
- 强调关键符号差异

## 🎯 期望效果

### 修复后的正确格式
```markdown
## 详细测试步骤

1. 打开浏览器并访问网易深圳网站 (https://shenzhen.news.163.com/)
   - **预期结果**: 网页加载成功，用户能够看到网易深圳的首页

2. 点击顶部"政务"菜单
   - **预期结果**: 政务页面加载成功，显示政务相关的新闻列表

3. 点击今日第一条新闻
   - **预期结果**: 今日第一条新闻页面加载成功，用户能够查看新闻内容
```

### 关键特征
- ✅ 使用项目符号 (-)
- ✅ 加粗预期结果标签 (**预期结果**)
- ✅ 无嵌套有序列表
- ✅ 无重复序号

## 🔍 监控要点

### 需要验证的行为
1. AI是否完全避免使用嵌套有序列表
2. 预期结果是否始终使用项目符号
3. 是否完全按照示例格式执行
4. 整体格式的一致性

### 如果问题仍然存在
可能需要考虑：
1. 使用更直接的格式后处理
2. 在工具层面强制格式转换
3. 考虑使用不同的AI模型
4. 实现客户端格式修正

## 📋 相关文件

### 修改的文件
- `app/api/testcase-chat/route.ts` - 强化系统提示

### 修改的部分
1. 系统提示开头 - 添加强制约束
2. 格式要求 - 使用强制性语言
3. 示例说明 - 强调完全复制
4. 禁止格式 - 明确错误示例

## 🧪 测试验证

### 测试步骤
1. 重启服务器以应用新的系统提示
2. 在AI聊天框中请求生成测试步骤
3. 仔细检查预期结果的格式
4. 验证是否完全消除嵌套有序列表

### 预期结果
- ✅ 预期结果使用项目符号格式
- ✅ 无任何嵌套有序列表
- ✅ 格式完全符合示例
- ✅ 视觉效果清晰专业

## ⚠️ 备用方案

如果强化约束仍然无效，可以考虑：

### 1. 客户端格式修正
```typescript
// 在前端处理AI回复，自动修正格式
const fixFormat = (content: string) => {
  return content.replace(/(\d+)\.\s*预期结果:/g, '   - **预期结果**:');
};
```

### 2. 工具层面处理
```typescript
// 在generateTestSteps工具中强制格式
const formatSteps = (steps) => {
  return steps.map(step => ({
    ...step,
    expected: `- **预期结果**: ${step.expected}`
  }));
};
```

## ✨ 总结

这次强化修复通过多层次的约束：

- **开头警告**: 立即声明格式约束
- **强制语言**: 使用"绝对禁止"等强制性词汇
- **具体示例**: 明确展示错误和正确格式
- **重复强调**: 在多个位置重申关键约束

期望通过这种强制性的约束，AI能够严格按照要求的格式生成测试步骤！
