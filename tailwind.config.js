/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1e88e5', // 蓝色作为主题色
          50: '#e3f2fd',
          100: '#bbdefb',
          200: '#90caf9',
          300: '#64b5f6',
          400: '#42a5f5',
          500: '#1e88e5',
          600: '#1976d2',
          700: '#1565c0',
          800: '#0d47a1',
          900: '#0a2351',
          foreground: '#ffffff',
        },
        sidebar: {
          DEFAULT: '#ffffff', // 侧边栏背景色为白色
          foreground: '#333333',
          border: 'transparent', // 透明边框
          accent: '#f3f4f6',
          'accent-foreground': '#111827',
        },
      },
    },
  },
  plugins: [],
} 