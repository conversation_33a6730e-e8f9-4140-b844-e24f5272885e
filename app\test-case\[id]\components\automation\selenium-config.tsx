'use client';

import { Globe } from 'lucide-react';
import BaseAutomationConfig from './base-automation-config';
import { AutomationConfig } from '../../types';

interface SeleniumConfigProps {
  config?: AutomationConfig;
  onEdit?: () => void;
  onCreate?: () => void;
  onRun?: () => void;
}

export default function SeleniumConfig({
  config,
  onEdit,
  onCreate,
  onRun
}: SeleniumConfigProps) {
  return (
    <BaseAutomationConfig
      config={config}
      framework="selenium"
      frameworkIcon={Globe}
      frameworkColor="text-orange-600"
      frameworkBg="bg-orange-100"
      frameworkBorder="border-orange-200"
      onEdit={onEdit}
      onCreate={onCreate}
      onRun={onRun}
    />
  );
}
