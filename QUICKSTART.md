# 🚀 快速开始指南

本指南将帮助您在 5 分钟内启动 AI Run 项目。

## 📋 前置要求

- Node.js 18+ 
- npm, yarn 或 pnpm
- Git

## ⚡ 快速启动

### 1. 克隆项目

```bash
git clone https://github.com/your-username/ai-run-nextjs.git
cd ai-run-nextjs
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

创建 `.env.local` 文件：

```bash
# 复制示例文件
cp .env.example .env.local
```

编辑 `.env.local`，至少配置以下必需项：

```env
# AI 模型配置 (必需)
QWEN_API_KEY=your_qwen_api_key_here

# 数据库配置
DB_PROVIDER=sqlite
DATABASE_URL=file:./data.db

# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```

### 4. 启动开发服务器

```bash
npm run dev
```

### 5. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

🎉 **恭喜！您的 AI Run 应用已经成功启动！**

## 🔧 常见问题

### Q: 如何获取 Qwen API 密钥？

A: 
1. 访问 [Qwen AI 官网](https://qwen.ai/)
2. 注册并登录账户
3. 在控制台中创建 API 密钥
4. 复制密钥到 `.env.local` 文件

### Q: 遇到 "An error occurred" 错误？

A: 请检查：
1. API 密钥是否正确配置
2. 网络连接是否正常
3. 查看浏览器控制台和服务器日志

### Q: 如何切换数据库？

A: 修改 `.env.local` 中的 `DB_PROVIDER`：
- `sqlite` - 使用 SQLite (推荐开发环境)
- `postgres` - 使用 PostgreSQL (推荐生产环境)

## 🐳 Docker 快速启动

如果您更喜欢使用 Docker：

```bash
# 生产环境
docker-compose -f devops/docker-compose.yml up --build -d

# 开发环境
docker-compose -f devops/docker-compose.dev.yml up --build -d
```

## 📱 功能体验

启动应用后，您可以体验以下功能：

### 🤖 AI 聊天
- 与 AI 进行智能对话
- 支持多轮上下文对话
- 实时流式响应

### 📝 文档处理
- 上传文档进行分析
- AI 驱动的文档总结
- 智能问答功能

### 💻 代码生成
- 智能代码生成
- 代码解释和优化
- 多种编程语言支持

### 🎨 界面特性
- 响应式设计
- 暗色主题支持
- 实时预览功能

## 🔍 下一步

- 📖 阅读 [完整文档](README.md)
- 🐳 了解 [Docker 部署](devops/DOCKER_README.md)
- 🤝 查看 [贡献指南](CONTRIBUTING.md)
- 🐛 报告 [问题](https://github.com/your-repo/issues)

## 📞 需要帮助？

- 📧 邮箱: <EMAIL>
- 💬 Discord: [加入社区](https://discord.gg/your-server)
- 📖 文档: [完整文档](README.md)

---

**祝您使用愉快！** 🎉 