'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  PlayCircle, 
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  AlertCircle,
  Calendar,
  User,
  Monitor,
  TrendingUp
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function TestRunsModule({
  testCase,
  onRunTest
}: ModuleProps) {
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'running':
        return <Activity className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'skipped':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'passed': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'failed': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'running': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'skipped': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getSuccessRate = () => {
    if (testCase.testRuns.length === 0) return 0;
    const passedRuns = testCase.testRuns.filter(run => run.status === 'passed').length;
    return Math.round((passedRuns / testCase.testRuns.length) * 100);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-teal-200 dark:border-teal-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <PlayCircle className="w-5 h-5 text-teal-600" />
            <h2 className="text-xl font-semibold">Test Execution History</h2>
            <Badge variant="outline" className="ml-2">
              {testCase.testRuns.length} runs
            </Badge>
          </div>
          <Button 
            onClick={onRunTest}
            className="bg-teal-600 hover:bg-teal-700"
          >
            <PlayCircle className="w-4 h-4 mr-2" />
            Run Test
          </Button>
        </div>

        {/* Statistics */}
        {testCase.testRuns.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-4 h-4 text-teal-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Success Rate</span>
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                {getSuccessRate()}%
              </span>
            </div>
            
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <PlayCircle className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Total Runs</span>
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                {testCase.testRuns.length}
              </span>
            </div>
            
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Passed</span>
              </div>
              <span className="text-2xl font-bold text-green-600">
                {testCase.testRuns.filter(run => run.status === 'passed').length}
              </span>
            </div>
            
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Failed</span>
              </div>
              <span className="text-2xl font-bold text-red-600">
                {testCase.testRuns.filter(run => run.status === 'failed').length}
              </span>
            </div>
          </div>
        )}

        {/* Test Runs List */}
        {testCase.testRuns.length > 0 ? (
          <div className="space-y-4">
            {testCase.testRuns.map((run) => (
              <div
                key={run.id}
                className="flex items-start gap-4 p-4 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 hover:bg-slate-100 dark:hover:bg-slate-800/70 transition-all duration-200"
              >
                <div className="flex items-center gap-2">
                  {getStatusIcon(run.status)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4 mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-1">
                        <Badge className={getStatusColor(run.status)}>
                          {run.status.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          Duration: {formatDuration(run.duration)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>{new Date(run.runDate).toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>{run.executor}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Monitor className="w-3 h-3" />
                          <span>{run.environment}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Step Results */}
                  {run.results.length > 0 && (
                    <div className="mt-3">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                          Step Results:
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {run.results.map((result, index) => (
                          <div
                            key={result.stepId}
                            className={`w-6 h-6 rounded text-xs flex items-center justify-center font-medium ${
                              result.status === 'passed' 
                                ? 'bg-green-100 text-green-800' 
                                : result.status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}
                            title={`Step ${index + 1}: ${result.status} (${result.duration}s)`}
                          >
                            {index + 1}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <PlayCircle className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
              No test runs yet
            </h3>
            <p className="text-slate-500 dark:text-slate-500 mb-4">
              Execute this test case to see the results and history
            </p>
            <Button 
              onClick={onRunTest}
              className="bg-teal-600 hover:bg-teal-700"
            >
              <PlayCircle className="w-4 h-4 mr-2" />
              Run First Test
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
