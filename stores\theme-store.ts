import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 主题类型定义
export type Theme = 'default' | 'blue' | 'green' | 'purple' | 'orange';

// 主题状态接口
interface ThemeState {
  theme: Theme;
  isDarkMode: boolean;
}

// 主题操作接口
interface ThemeActions {
  setTheme: (theme: Theme) => void;
  setDarkMode: (isDark: boolean) => void;
  toggleDarkMode: () => void;
  applyTheme: (theme?: Theme) => void;
  initializeTheme: () => void;
  reset: () => void;
}

// 主题Store类型
export type ThemeStore = ThemeState & ThemeActions;

// 默认状态
const defaultState: ThemeState = {
  theme: 'blue', // 默认使用蓝色主题
  isDarkMode: false,
};

// 应用主题到DOM的函数
const applyThemeToDOM = (theme: Theme, isDark: boolean) => {
  if (typeof window === 'undefined') return;

  const html = document.documentElement;

  // 移除所有主题类
  html.classList.remove('theme-blue', 'theme-green', 'theme-purple', 'theme-orange');

  // 应用新主题类
  if (theme !== 'default') {
    html.classList.add(`theme-${theme}`);
  }

  // 应用深色模式
  if (isDark) {
    html.classList.add('dark');
  } else {
    html.classList.remove('dark');
  }
};

// 创建主题状态管理Store
export const useThemeStore = create<ThemeStore>()(
  persist(
    (set, get) => ({
      ...defaultState,

      // 设置主题
      setTheme: (theme) => {
        set({ theme });
        const { isDarkMode } = get();
        applyThemeToDOM(theme, isDarkMode);
        
        // 触发自定义事件通知其他组件
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme, isDarkMode }
          }));
        }
      },

      // 设置深色模式
      setDarkMode: (isDark) => {
        set({ isDarkMode: isDark });
        const { theme } = get();
        applyThemeToDOM(theme, isDark);
        
        // 触发自定义事件
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme, isDarkMode: isDark }
          }));
        }
      },

      // 切换深色模式
      toggleDarkMode: () => {
        const { isDarkMode } = get();
        get().setDarkMode(!isDarkMode);
      },

      // 应用主题（用于初始化或强制应用）
      applyTheme: (theme) => {
        const currentTheme = theme || get().theme;
        const { isDarkMode } = get();
        applyThemeToDOM(currentTheme, isDarkMode);
      },

      // 初始化主题（从localStorage读取并应用）
      initializeTheme: () => {
        if (typeof window === 'undefined') return;

        // 先尝试从localStorage手动读取，因为persist可能还没有hydrate
        let savedTheme: Theme = 'blue';
        let savedDarkMode = false;

        try {
          const savedData = localStorage.getItem('theme-store');
          if (savedData) {
            const parsed = JSON.parse(savedData);
            if (parsed.state) {
              savedTheme = parsed.state.theme || 'blue';
              savedDarkMode = parsed.state.isDarkMode || false;
            }
          }
        } catch (error) {
          console.warn('读取主题设置失败:', error);
        }

        // 应用主题到DOM
        applyThemeToDOM(savedTheme, savedDarkMode);

        // 更新store状态（如果不同的话）
        const { theme, isDarkMode } = get();
        if (theme !== savedTheme || isDarkMode !== savedDarkMode) {
          set({ theme: savedTheme, isDarkMode: savedDarkMode });
        }
      },

      // 重置到默认状态
      reset: () => {
        set(defaultState);
        applyThemeToDOM(defaultState.theme, defaultState.isDarkMode);
      },
    }),
    {
      name: 'theme-store', // localStorage 键名
      storage: createJSONStorage(() => localStorage),
      // 持久化所有状态
      partialize: (state) => ({
        theme: state.theme,
        isDarkMode: state.isDarkMode,
      }),
      // 添加 skipHydration 以避免 SSR 问题
      skipHydration: true,
    }
  )
);

// 主题初始化Hook（用于在应用启动时调用）
export const useThemeInitializer = () => {
  const initializeTheme = useThemeStore((state) => state.initializeTheme);
  
  // 在客户端初始化主题
  if (typeof window !== 'undefined') {
    // 使用 setTimeout 确保在 hydration 后执行
    setTimeout(() => {
      initializeTheme();
    }, 0);
  }
};
