"use client";

import { useEffect, useState } from 'react';
import type { JSX } from 'react';
import { useSearchParams } from 'next/navigation';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { 
  FileText, 
  Code, 
  Image, 
  FileSpreadsheet, 
  Search, 
  Eye, 
  Trash2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const MIDSCENE_REPORT = 'midscene-report';

interface Document {
  id: string;
  name: string;
  kind: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  size?: number;
}

interface DocumentsResponse {
  documents: Document[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export default function DocumentsContent() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedKind, setSelectedKind] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const searchParams = useSearchParams();
  const limit = 10;

  // 获取文档列表
  const fetchDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const offset = (currentPage - 1) * limit;
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        sortBy,
        sortDirection,
      });

      if (selectedKind !== 'all') {
        params.append('kind', selectedKind);
      }

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      const response = await fetch(`/api/documents/?${params}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: DocumentsResponse = await response.json();
      setDocuments(data.documents || []);
      setTotal(data.total || 0);
    } catch (err) {
      console.error('获取文档失败:', err);
      setError(err instanceof Error ? err.message : '获取文档失败');
      setDocuments([]);
      setTotal(0);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [currentPage, selectedKind, sortBy, sortDirection, searchTerm]);

  // 获取文档图标
  const getDocumentIcon = (kind: string) => {
    switch (kind) {
      case 'text': return <FileText className="w-4 h-4 text-blue-600" />;
      case 'code': return <Code className="w-4 h-4 text-green-600" />;
      case MIDSCENE_REPORT: return <FileText className="w-4 h-4 text-orange-600" />;
      case 'image': return <Image className="w-4 h-4 text-purple-600" />;
      case 'sheet': return <FileSpreadsheet className="w-4 h-4 text-emerald-600" />;
      default: return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd');
  };

  // 获取文档类型名称
  const getDocumentTypeName = (kind: string) => {
    switch (kind) {
      case 'text': return '文本文档';
      case 'code': return '代码文档';
      case MIDSCENE_REPORT: return '测试文档';
      case 'image': return '图像文档';
      case 'sheet': return '表格文档';
      default: return '未知类型';
    }
  };

  // 获取文档状态徽章
  const getDocumentStatusBadge = (kind: string): JSX.Element => {
    switch (kind) {
      case 'text':
        return <span className="inline-flex items-center rounded-md bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">已完成</span>;
      case 'code':
        return <span className="inline-flex items-center rounded-md bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">已审核</span>;
      case MIDSCENE_REPORT:
        return <span className="inline-flex items-center rounded-md bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800">测试中</span>;
      case 'image':
        return <span className="inline-flex items-center rounded-md bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800">已处理</span>;
      default:
        return <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800">未处理</span>;
    }
  };

  // 删除文档方法
  const handleDelete = async (id: string) => {
    if (!window.confirm('确定要删除该文档吗？')) return;
    try {
      setIsLoading(true);
      const res = await fetch(`/api/documents?id=${id}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('删除失败');
      await fetchDocuments();
    } catch (err) {
      alert('删除失败: ' + (err instanceof Error ? err.message : err));
    } finally {
      setIsLoading(false);
    }
  };

  // 计算总页数
  const totalPages = Math.ceil(total / limit);

  return (
    <div className="min-h-screen bg-slate-100 p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Documents</h1>
        <p className="text-muted-foreground">
          Manage and view all document resources
        </p>
      </div>

      {/* 主要内容区域 - 合并的大卡片（工具栏+表格+分页） */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
        {/* 搜索和筛选区域 */}
        <div className="border-b border-gray-200 pb-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索文档名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Select value={selectedKind} onValueChange={setSelectedKind}>
                <SelectTrigger>
                  <SelectValue placeholder="文档类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="text">文本文档</SelectItem>
                  <SelectItem value="code">代码文档</SelectItem>
                  <SelectItem value={MIDSCENE_REPORT}>测试文档</SelectItem>
                  <SelectItem value="image">图像文档</SelectItem>
                  <SelectItem value="sheet">表格文档</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={`${sortBy}-${sortDirection}`} onValueChange={(value) => {
                const [field, direction] = value.split('-');
                setSortBy(field);
                setSortDirection(direction as 'asc' | 'desc');
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">创建时间 ↓</SelectItem>
                  <SelectItem value="createdAt-asc">创建时间 ↑</SelectItem>
                  <SelectItem value="updatedAt-desc">更新时间 ↓</SelectItem>
                  <SelectItem value="updatedAt-asc">更新时间 ↑</SelectItem>
                  <SelectItem value="name-asc">名称 A-Z</SelectItem>
                  <SelectItem value="name-desc">名称 Z-A</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 文档表格 */}
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : error ? (
          <div className="bg-red-50 rounded-xl border border-red-200 p-8 text-center">
            <div className="text-red-600 mb-4">
              <p className="text-lg font-medium">加载失败</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
            <Button onClick={fetchDocuments} className="bg-red-600 hover:bg-red-700">重试</Button>
          </div>
        ) : documents.length === 0 ? (
          <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文档</h3>
            <p className="text-gray-500">还没有任何文档，开始创建您的第一个文档吧！</p>
          </div>
        ) : (
          <>
            <div className="rounded-lg border border-gray-200 overflow-hidden bg-white">
              <Table>
                <TableHeader>
                  <TableRow className="h-12 bg-gray-50 border-b border-gray-200">
                    <TableHead className="font-semibold text-gray-700">文档名称</TableHead>
                    <TableHead className="font-semibold text-gray-700">类型</TableHead>
                    <TableHead className="font-semibold text-gray-700">创建者</TableHead>
                    <TableHead className="font-semibold text-gray-700">创建时间</TableHead>
                    <TableHead className="font-semibold text-gray-700">状态</TableHead>
                    <TableHead className="font-semibold text-gray-700">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow 
                      key={doc.id} 
                      className="h-14 hover:bg-blue-50 border-b border-gray-100 transition-all duration-200 group"
                    >
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {getDocumentIcon(doc.kind)}
                          <span className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                            {doc.name}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-600">{getDocumentTypeName(doc.kind)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm">
                            {doc.userId?.charAt(0)?.toUpperCase() || 'U'}
                          </div>
                          <span className="text-gray-700">{doc.userId || '未知用户'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-gray-600">{formatDate(doc.createdAt)}</span>
                      </TableCell>
                      <TableCell>
                        {getDocumentStatusBadge(doc.kind)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(doc.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 分页区域 */}
            <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                显示 {(currentPage - 1) * limit + 1} - {Math.min(currentPage * limit, total)} 条，共 {total} 条记录
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="flex items-center gap-1"
                >
                  <ChevronLeft className="w-4 h-4" />
                  上一页
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = i + 1;
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="flex items-center gap-1"
                >
                  下一页
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
