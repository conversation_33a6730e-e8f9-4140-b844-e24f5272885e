import { DataStreamWriter, tool } from 'ai';
import { Session } from 'next-auth';
import { z } from 'zod';
import { getDocumentById, saveDocument } from '@/lib/db/queries';
import { documentHandlersByArtifactKind } from '@/lib/artifacts/server';
import { generateUUID } from '@/lib/utils';
import { aiLogger } from '@/lib/logger';

// 创建模块专用的日志记录器
const logger = aiLogger.child('update-document');

interface UpdateDocumentProps {
  session: Session;
  dataStream: DataStreamWriter;
}

export const updateDocument = ({ session, dataStream }: UpdateDocumentProps) =>
  tool({
    description: 'Update a document with the given description.',
    parameters: z.object({
      id: z.string().describe('The ID of the document to update'),
      description: z
        .string()
        .describe('The description of changes that need to be made'),
    }),
    execute: async ({ id, description }) => {
      try {
      const document = await getDocumentById({ id });

      if (!document) {
          logger.error(`文档未找到: ID=${id}`);
        return {
          error: 'Document not found',
            content: JSON.stringify([
              { type: "text", text: `无法找到ID为"${id}"的文档，请确认文档ID是否正确。` }
            ])
        };
      }
  
        logger.info(`准备更新文档: ID=${id}, 标题=${document.title}, 类型=${document.kind}`);

      dataStream.writeData({
        type: 'clear',
        content: document.title,
      });

      const documentHandler = documentHandlersByArtifactKind.find(
        (documentHandlerByArtifactKind) =>
          documentHandlerByArtifactKind.kind === document.kind,
      );

      if (!documentHandler) {
          const errorMsg = `No document handler found for kind: ${document.kind}`;
          logger.error(errorMsg);
          throw new Error(errorMsg);
      }

        // 调用文档处理器的onUpdateDocument方法并获取返回值
        const result = await documentHandler.onUpdateDocument({
        document,
        description,
        dataStream,
        session,
      });

      dataStream.writeData({ type: 'finish', content: '' });

        // 只使用report_uri属性
        let reportUri = null;
        if (result && typeof result === 'object' && result.report_uri) {
          reportUri = result.report_uri;
          logger.info(`文档更新后获取到报告URI: ${reportUri}`);
        }
        
        // 构造消息内容，包含文档引用，与createDocument保持一致
        const messageContent = JSON.stringify([
          { type: "text", text: `我已经更新了文档："${document.title}"` },
          { type: "document-reference", title: document.title, document_id: id },
          {
            type: "tool-invocation",
            toolInvocation: {
              toolName: "updateDocument",
              toolCallId: generateUUID(),
              state: "result",
              result: {
                id: id,
                title: document.title,
                kind: document.kind,
                report_uri: reportUri,
                isVisible: true
              }
            }
          }
        ]);
  
        logger.info(`文档更新成功: ID=${id}`);
        
        // 返回结构化的结果，包含更新后的内容
      return {
        id,
        title: document.title,
        kind: document.kind,
          content: messageContent,
          report_uri: reportUri,
          isVisible: true,
        };
      } catch (error) {
        // 增强错误处理
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`更新文档时出错: ${errorMessage}`);
        
        return {
          error: errorMessage,
          content: JSON.stringify([
            { type: "text", text: `更新文档时出错: ${errorMessage}` }
          ])
        };
      }
    },
  });
