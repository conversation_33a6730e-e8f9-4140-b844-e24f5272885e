# AI回复格式重复序号修复

## 🐛 问题描述

**现象**: AI生成测试步骤时出现重复的序号显示：
```
1. 步骤 1: 打开浏览器并访问网易深圳网站
   1. 预期结果: 网页加载成功...

2. 步骤 2: 点击顶部政务菜单
   1. 预期结果: 政务页面加载成功...
```

**根本原因**: AI在回复中同时使用了：
- Markdown有序列表格式（1. 2. 3.）
- 文本中的步骤标识（"步骤 1:"、"步骤 2:"）

导致序号重复显示。

## 🔍 问题分析

### AI回复格式问题
```markdown
1. 步骤 1: 打开浏览器...  ← 重复的序号
   1. 预期结果: ...        ← 嵌套列表导致的序号

2. 步骤 2: 点击顶部...    ← 重复的序号
   1. 预期结果: ...        ← 嵌套列表导致的序号
```

### 期望的正确格式
```markdown
1. 打开浏览器并访问网易深圳网站
   - **预期结果**: 网页加载成功...

2. 点击顶部政务菜单
   - **预期结果**: 政务页面加载成功...
```

## 🔧 修复方案

### 1. 更新系统提示

#### 添加格式要求
```typescript
**重要格式要求**：
- 在回复中描述步骤时，不要使用"步骤 1:"、"步骤 2:"等标识
- 直接使用Markdown有序列表格式（1. 2. 3.）
- 避免重复的序号显示
```

#### 提供正确示例
```markdown
## 详细测试步骤

1. 打开网易深圳网站 (https://shenzhen.news.163.com/)
   - **预期结果**: 网页加载成功，用户能够看到网易深圳的首页

2. 点击顶部政务菜单
   - **预期结果**: 政务页面加载成功，显示政务相关的新闻列表

3. 点开今日第一条新闻
   - **预期结果**: 今日第一条新闻页面加载成功，用户能够查看新闻内容
```

### 2. 格式规范

#### 正确的Markdown结构
```markdown
1. [操作描述]
   - **预期结果**: [预期结果描述]

2. [操作描述]
   - **预期结果**: [预期结果描述]
```

#### 避免的格式
```markdown
❌ 1. 步骤 1: [操作描述]
❌    1. 预期结果: [预期结果描述]

❌ • 步骤 1: [操作描述]
❌ • 步骤 2: [操作描述]
```

## 📊 修复前后对比

### 修复前的AI回复
```markdown
生成的步骤概览：

1. 步骤 1: 打开浏览器并访问网易深圳网站
   1. 预期结果: 网页加载成功，显示网易深圳的首页

2. 步骤 2: 在首页顶部导航栏中找到并点击"政务"菜单
   1. 预期结果: 跳转到政务页面，并显示政务相关的新闻列表

3. 步骤 3: 在政务页面中，点击今日第一条新闻标题或链接
   1. 预期结果: 跳转到该条新闻的详细页面，用户能够查看新闻内容
```

### 修复后的AI回复
```markdown
## 详细测试步骤

1. 打开浏览器并访问网易深圳网站 (https://shenzhen.news.163.com/)
   - **预期结果**: 网页加载成功，显示网易深圳的首页

2. 在首页顶部导航栏中找到并点击"政务"菜单
   - **预期结果**: 跳转到政务页面，并显示政务相关的新闻列表

3. 在政务页面中，点击今日第一条新闻标题或链接
   - **预期结果**: 跳转到该条新闻的详细页面，用户能够查看新闻内容
```

## 🎯 改进效果

### 1. 视觉清晰度 ✅
- 消除重复序号
- 清晰的层级结构
- 更好的可读性

### 2. 格式一致性 ✅
- 统一的Markdown格式
- 标准的列表结构
- 专业的文档样式

### 3. 用户体验 ✅
- 减少视觉混乱
- 提高信息获取效率
- 更专业的展示效果

## 🔄 AI训练指导

### 系统提示更新
```typescript
// 在testcase-chat API中更新
const systemPrompt = `
...
**generateTestSteps工具使用指南**：
当用户要求生成测试步骤时，你必须：
1. 首先分析用户的具体需求，理解要测试的功能和流程
2. 在回复中详细描述每个步骤（用户可见）
3. 同时在工具调用的steps参数中提供相同的步骤信息
4. 确保steps数组中的每个对象都有详细的action和expected字段

**重要格式要求**：
- 在回复中描述步骤时，不要使用"步骤 1:"、"步骤 2:"等标识
- 直接使用Markdown有序列表格式（1. 2. 3.）
- 避免重复的序号显示
...
`;
```

### 示例模板
```markdown
## 详细测试步骤

1. [具体操作描述]
   - **预期结果**: [详细的预期结果]

2. [具体操作描述]
   - **预期结果**: [详细的预期结果]

3. [具体操作描述]
   - **预期结果**: [详细的预期结果]
```

## 🧪 测试验证

### 测试步骤
1. 重启服务器以应用新的系统提示
2. 在AI聊天框中请求生成测试步骤
3. 观察AI回复的格式是否符合要求
4. 验证是否消除了重复序号

### 预期结果
- ✅ AI使用标准的有序列表格式
- ✅ 不再出现"步骤 X:"标识
- ✅ 预期结果使用项目符号格式
- ✅ 整体格式清晰专业

## 📋 相关文件

### 修改的文件
- `app/api/testcase-chat/route.ts` - 更新系统提示

### 影响的功能
- AI生成测试步骤的回复格式
- 用户在聊天框中看到的步骤展示

## ✨ 总结

这次修复通过优化AI的系统提示：

- **问题根源**: AI回复格式不规范，导致重复序号
- **修复方案**: 更新系统提示，提供明确的格式要求和示例
- **修复效果**: 消除重复序号，提供清晰专业的步骤展示
- **用户体验**: 提高可读性和专业性

现在AI生成的测试步骤将具有更清晰、更专业的格式！
