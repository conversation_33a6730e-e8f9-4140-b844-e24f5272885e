"use client";

import { usePathname } from "next/navigation";
import { SimpleSidebarLeft } from "@/components/navigation/simple-sidebar-left";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { useSidebarCollapsed, useNavigationActions } from "@/stores/simple-navigation-store";
import { Button } from "@/components/ui/button";
import { Settings, Bell } from "lucide-react";
import { HorizontalNav } from "@/components/navigation/horizontal-nav";
import { NavigationLayoutSwitcher } from "@/components/navigation-layout-switcher";
import { useSimpleNavigation } from "@/hooks/use-simple-navigation";
import React from "react";

interface AdaptiveLayoutManagerProps {
  children: React.ReactNode;
}

export function AdaptiveLayoutManager({ children }: AdaptiveLayoutManagerProps) {
  const pathname = usePathname();

  // 使用简化的导航状态管理
  const { navigation, isVerticalLayout, isHorizontalLayout } = useSimpleNavigation();

  // 面包屑从状态管理中获取
  const breadcrumbs = navigation.breadcrumbs;

  // 检查是否是特殊路由（如登录页）
  const isAuthRoute = pathname.startsWith("/login") || pathname.startsWith("/register");
  const isMinimalRoute = isAuthRoute;

  // 最小布局（登录页等）
  if (isMinimalRoute) {
    return (
      <div className="flex h-dvh w-screen items-center justify-center bg-background">
        {children}
      </div>
    );
  }

  // 渲染Header组件（仅在垂直布局时显示）
  const renderHeader = () => {
    if (!isVerticalLayout) return null;

    const { toggleSidebarCollapsed } = useNavigationActions();

    return (
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-white/80 backdrop-blur-sm border-slate-200 dark:border-slate-700">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebarCollapsed}
          className="-ml-1"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </Button>
        <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
        
        {/* 面包屑导航 */}
        <div className="flex-1">
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((bc, idx) => (
                <React.Fragment key={bc.url}>
                  <BreadcrumbItem>
                    {idx < breadcrumbs.length - 1 ? (
                      <BreadcrumbLink href={bc.url} className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
                        {bc.title}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage className="font-semibold text-slate-900 dark:text-slate-100">
                        {bc.title}
                      </BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                  {idx < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {/* 右侧操作按钮 */}
        <div className="flex items-center gap-2">
          <NavigationLayoutSwitcher />
          <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
            <Bell className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </header>
    );
  };

  // 垂直布局（传统侧边栏布局）
  if (isVerticalLayout) {
    const sidebarCollapsed = useSidebarCollapsed();

    return (
      <div className="flex h-screen">
        <SimpleSidebarLeft />
        <div
          className={`flex-1 flex flex-col min-h-0 transition-all duration-300 ${
            sidebarCollapsed ? 'ml-16' : 'ml-64'
          }`}
        >
          {renderHeader()}
          <main className="flex-1 min-h-0 p-6">
            {children}
          </main>
        </div>
      </div>
    );
  }

  // 水平布局（顶部导航栏布局）
  return (
    <div className="flex flex-col h-screen">
      {/* 水平导航栏 */}
      <HorizontalNav />
      
      {/* 工具栏 */}
      <div className="flex h-12 shrink-0 items-center justify-between gap-2 border-b px-4 bg-slate-50/50 dark:bg-slate-900/50 border-slate-200 dark:border-slate-700">
        {/* 面包屑导航 */}
        <div className="flex-1">
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((bc, idx) => (
                <React.Fragment key={bc.url}>
                  <BreadcrumbItem>
                    {idx < breadcrumbs.length - 1 ? (
                      <BreadcrumbLink href={bc.url} className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
                        {bc.title}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage className="font-semibold text-slate-900 dark:text-slate-100">
                        {bc.title}
                      </BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                  {idx < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {/* 右侧操作按钮 */}
        <div className="flex items-center gap-2">
          <NavigationLayoutSwitcher />
          <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
            <Bell className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 主内容区 */}
      <main className="flex-1 min-h-0 p-6 max-w-7xl mx-auto w-full">
        {children}
      </main>
    </div>
  );
}
