# 使用官方Node.js运行时作为基础镜像
FROM node:20-slim AS base

# Puppeteer Chromium 国内镜像加速
ENV PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors

# 安装 puppeteer 运行所需依赖
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

# 安装依赖项（包括 Python3、make、g++）
FROM base AS deps
WORKDIR /app
# 设置国内npm源
RUN npm config set registry https://registry.npmmirror.com/
# 只用npm安装依赖
COPY package.json package-lock.json* ./
RUN npm ci --verbose

# 重新构建源码
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors

# 构建应用
RUN npm run build

# 用 node 用户安装 puppeteer 的 chrome
USER node
RUN npx puppeteer browsers install chrome
USER root

# 生产镜像，复制所有文件并运行 next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors

# 使用现有的 node 用户（UID 1000）
# node 用户已经在 node:20-slim 镜像中存在

COPY --from=builder /app/public ./public
COPY --from=builder /app/lib/db/init-db.js ./lib/db/init-db.js

# 设置正确的权限用于 prerender 缓存
RUN mkdir .next
RUN chown node:node .next

# 自动利用输出跟踪来复制必要的文件
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=node:node /app/.next/standalone ./
COPY --from=builder --chown=node:node /app/.next/static ./.next/static

# 复制 htmlElement.js 到 .next/dist/script
RUN mkdir -p .next/dist/script
COPY public/script/htmlElement.js .next/dist/script/htmlElement.js

# 创建数据目录并设置权限
RUN mkdir -p /app/data
RUN chown node:node /app/data
RUN chmod 775 /app/data

# 创建日志目录并设置权限
RUN mkdir -p /app/logs
RUN chown node:node /app/logs
RUN chmod 775 /app/logs

# 创建 cache 目录并授权给 node 用户
RUN mkdir -p /home/<USER>/.cache/puppeteer && chown -R node:node /home/<USER>/.cache

# 拷贝 puppeteer cache
COPY --from=builder /home/<USER>/.cache/puppeteer /home/<USER>/.cache/puppeteer

# 拷贝启动脚本并授权（必须在 USER node 之前）
COPY devops/start.sh /app/start.sh
RUN chmod +x /app/start.sh

USER node

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 用启动脚本作为入口
CMD ["/app/start.sh"] 