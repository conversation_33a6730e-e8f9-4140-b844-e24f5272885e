import { SidebarInset } from "@/components/sidebar/sidebar-main"
import { Card, CardHeader, CardDescription, CardTitle, CardAction, CardFooter, CardContent } from "@/components/ui/card"
import { Badge } from '@/components/ui/badge'
import { TrendAreaChart } from "@/components/dashboard/trend-area-chart"
import { DataTable } from "@/components/dashboard/data-table"
import { DataCard } from "@/components/dashboard/data-card"
import { TrendingUp as IconTrendingUp } from "lucide-react"
import * as React from "react"
import { getDocumentTypeCounts } from '@/lib/db/queries'
import { db } from '@/lib/db'
import { chat, document, user } from '@/lib/db/schema'
import { count } from 'drizzle-orm'
import { sql } from 'drizzle-orm'
import { MidsceneReportType, MIDSCENE_REPORT } from '@/artifacts/types';

export default async function Page() {
  // 直接服务端查数据
  const [chatCountResult] = await db.select({ count: count() }).from(chat)
  const [documentCountResult] = await db.select({ count: count() }).from(document)
  const [userCountResult] = await db.select({ count: count() }).from(user)
  const repositoryCount = 9
  let documentTypeCounts
  try {
    documentTypeCounts = await getDocumentTypeCounts()
  } catch {
    documentTypeCounts = { text: 0, code: 0, image: 0, sheet: 0 }
  }
  // 生成近7天的趋势数据（可用于卡片小图表）
  const chartDays = 7
  const chartToday = new Date()
  const chatTrend: Array<{ day: string; value: number }> = []
  const docTrend: Array<{ day: string; value: number }> = []
  const userTrend: Array<{ day: string; value: number }> = []
  const repoTrend: Array<{ day: string; value: number }> = []
  for (let i = chartDays - 1; i >= 0; i--) {
    const day = new Date(chartToday)
    day.setDate(chartToday.getDate() - i)
    day.setHours(0, 0, 0, 0)
    const dayStr = day.toISOString().slice(5, 10) // MM-DD
    // 这里用mock数据，实际可查数据库
    chatTrend.push({ day: dayStr, value: Math.floor(Math.random() * 20 + 80) })
    docTrend.push({ day: dayStr, value: Math.floor(Math.random() * 30 + 100) })
    userTrend.push({ day: dayStr, value: Math.floor(Math.random() * 5 + 20) })
    repoTrend.push({ day: dayStr, value: Math.floor(Math.random() * 2 + 8) })
  }
  const stats = [
    {
      title: 'Chat Amount',
      value: chatCountResult.count,
      percent: '+12.5%',
      chartType: 'line',
      chartKey: 'value',
      chartData: chatTrend,
    },
    {
      title: 'Document Amount',
      value: documentCountResult.count,
      percent: '+8.2%',
      chartType: 'bar',
      chartKey: 'value',
      chartData: docTrend,
    },
    {
      title: 'User Amount',
      value: userCountResult.count,
      percent: '+3.1%',
      chartType: 'line',
      chartKey: 'value',
      chartData: userTrend,
    },
    {
      title: 'Repository Amount',
      value: repositoryCount,
      percent: '+1.0%',
      chartType: 'bar',
      chartKey: 'value',
      chartData: repoTrend,
    },
  ]

  const days = 7
  const today = new Date()
  const trendData: Array<{ date: string; sheet: number; testing: number; text: number; code: number; all: number }> = []
  for (let i = days - 1; i >= 0; i--) {
    const day = new Date(today)
    day.setDate(today.getDate() - i)
    day.setHours(0, 0, 0, 0)
    const dateStr = day.toISOString().slice(0, 10)
    // 查询每种类型的数量
    const [sheetCount] = await db.select({ count: count() }).from(document).where(sql`date(createdAt, 'unixepoch') = ${dateStr} and kind = 'sheet'`)
    const [testingCount] = await db.select({ count: count() }).from(document).where(sql`date(createdAt, 'unixepoch') = ${dateStr} and kind = ${MIDSCENE_REPORT}`)
    const [textCount] = await db.select({ count: count() }).from(document).where(sql`date(createdAt, 'unixepoch') = ${dateStr} and kind = 'text'`)
    const [codeCount] = await db.select({ count: count() }).from(document).where(sql`date(createdAt, 'unixepoch') = ${dateStr} and kind = 'code'`)
    const all = Number(sheetCount?.count ?? 0) + Number(testingCount?.count ?? 0) + Number(textCount?.count ?? 0) + Number(codeCount?.count ?? 0)
    trendData.push({
      date: dateStr,
      sheet: Number(sheetCount?.count ?? 0),
      testing: Number(testingCount?.count ?? 0),
      text: Number(textCount?.count ?? 0),
      code: Number(codeCount?.count ?? 0),
      all,
    })
  }


  return (
    <SidebarInset>
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-zinc-900 dark:to-zinc-800 p-6">
        <div className="flex flex-1 flex-col gap-6">
          {/* 页面标题 */}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              View system overview and key metrics data
            </p>
          </div>

          {/* 统计卡片区 */}
           <DataCard stats={stats} />

          {/* 趋势图区 */}
          <TrendAreaChart documentTypeCounts={trendData} />

          {/* Test Case表格区 */}
          <div className="bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm shadow-lg rounded-xl border border-gray-200 dark:border-zinc-700 p-6 flex-1">

            <Card className="bg-transparent border-none shadow-none">
              <DataTable data={[
                {
                  "id": 1,
                  "header": "Cover page",
                  "type": "Cover page",
                  "status": "In Process",
                  "target": "18",
                  "limit": "5",
                  "reviewer": "Eddie Lake"
                }
              ]}/>
            </Card>
          </div>
        </div>
      </div>
    </SidebarInset>
  )
}
