"use client"

import { cn } from "@/lib/utils"
import { useNavigationLayout, useSidebarCollapsed } from "@/stores/simple-navigation-store"

interface SimpleSidebarProps {
  children: React.ReactNode
  className?: string
}

export function SimpleSidebar({ children, className }: SimpleSidebarProps) {
  const layout = useNavigationLayout()
  const sidebarCollapsed = useSidebarCollapsed()

  // 只在垂直布局时显示
  if (layout !== 'vertical') {
    return null
  }

  return (
    <aside
      className={cn(
        "fixed left-0 top-0 z-40 h-screen bg-sidebar border-r border-sidebar-border sidebar-shadow transition-all duration-300",
        sidebarCollapsed ? "w-16" : "w-64",
        className
      )}
    >
      <div className="flex h-full flex-col">
        {children}
      </div>
    </aside>
  )
}

interface SimpleSidebarHeaderProps {
  children: React.ReactNode
  className?: string
}

export function SimpleSidebarHeader({ children, className }: SimpleSidebarHeaderProps) {
  return (
    <div className={cn("flex items-center justify-between p-4 border-b border-sidebar-border", className)}>
      {children}
    </div>
  )
}

interface SimpleSidebarContentProps {
  children: React.ReactNode
  className?: string
}

export function SimpleSidebarContent({ children, className }: SimpleSidebarContentProps) {
  return (
    <div className={cn("flex-1 overflow-y-auto", className)}>
      {children}
    </div>
  )
}

interface SimpleSidebarFooterProps {
  children: React.ReactNode
  className?: string
}

export function SimpleSidebarFooter({ children, className }: SimpleSidebarFooterProps) {
  return (
    <div className={cn("p-4 border-t border-sidebar-border", className)}>
      {children}
    </div>
  )
}
