import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { DBMessage,Document } from '@/lib/db/schema';
import { ChatSDKError, type ErrorCode } from './errors';
import type {
  CoreAssistantMessage,
  CoreToolMessage,
  UIMessage,
} from 'ai';
import type { ChatMessage, ChatTools, CustomUIDataTypes } from './types';
import { formatISO } from 'date-fns';


// 支持的模型映射，可根据实际需要扩展
const MODEL_TOKENIZER_MAP: Record<string, string> = {
  'gpt-3.5-turbo': 'gpt-3.5-turbo',
  'gpt-4': 'gpt-4',
  'qwen-turbo': 'qwen-turbo',
  'qwen-plus': 'qwen-plus',
  'deepseek-chat': 'deepseek-chat',
  // 可继续扩展
};

/**
 * 统计消息数组的总 token 数
 * @param messages 消息数组（{ role, content }[]）
 * @param model 模型名（如 'gpt-3.5-turbo', 'qwen-turbo'）
 */
export function countTokens(
  messages: { role: string; content: string }[],
  model: string = 'gpt-3.5-turbo'
): number {
  const tokenizerName = MODEL_TOKENIZER_MAP[model] || 'gpt-3.5-turbo';
  let total = 0;
  for (const msg of messages) {
    total += msg.content.length;
  }
  return total;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const fetcher = async (url: string) => {
  const response = await fetch(url);

  if (!response.ok) {
    const { code, cause } = await response.json();
    throw new ChatSDKError(code as ErrorCode, cause);
  }

  return response.json();
};

export async function fetchWithErrorHandlers(
  input: RequestInfo | URL,
  init?: RequestInit,
) {
  try {
    const response = await fetch(input, init);

    if (!response.ok) {
      const { code, cause } = await response.json();
      throw new ChatSDKError(code as ErrorCode, cause);
    }

    return response;
  } catch (error: unknown) {
    if (typeof navigator !== 'undefined' && !navigator.onLine) {
      throw new ChatSDKError('offline:chat');
    }

    throw error;
  }
}

export function getLocalStorage(key: string) {
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  return [];
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function getMostRecentUserMessage(messages: Array<UIMessage>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(
  documents: Array<Document>,
  index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getTrailingMessageId({
  messages,
}: {
  messages: Array<ResponseMessage>;
}): string | null {
  const trailingMessage = messages.at(-1);

  if (!trailingMessage) return null;

  return trailingMessage.id;
}

export function sanitizeText(text: string) {
  return text.replace('<has_function_call>', '');
}

export function convertToUIMessages(messages: DBMessage[]): UIMessage[] {
  return messages.map((message): UIMessage => {
    const fixedParts = Array.isArray(message.parts)
      ? message.parts.map((part: any) => {
          if (part.type === 'text') {
            if (typeof part.text === 'string') return { ...part, type: 'text', text: part.text };
            if (typeof part.content === 'string') return { ...part, type: 'text', text: part.content };
            return { ...part, type: 'text', text: '' };
          }
          return part;
        })
      : [];
    
    // 检查是否有 tool-invocation，如果有则提取为 tool_calls
    const toolInvocations = fixedParts.filter((part: any) => part.type === 'tool-invocation');
    const toolCalls = toolInvocations.map((invocation: any) => {
      const toolInvocation = invocation.toolInvocation;
      return {
        id: toolInvocation.toolCallId,
        type: 'function',
        function: {
          name: toolInvocation.toolName,
          arguments: JSON.stringify(toolInvocation.result || {})
        }
      };
    });
    
    // 过滤掉 tool-invocation，只保留其他 parts
    const textParts = fixedParts.filter((part: any) => part.type !== 'tool-invocation');
    
    const result: any = {
      id: message.id,
      role: message.role,
      parts: textParts,
      createdAt: message.createdAt,
    };
    
    // 如果有 tool_calls，添加到结果中
    if (toolCalls.length > 0) {
      result.tool_calls = toolCalls;
    }
    
    return result;
  });
}

function safeToDate(val: any): Date {
  if (val instanceof Date && !isNaN(val.getTime())) return val;
  if (typeof val === 'number') {
    if (val > 1e12) return new Date(val); // 毫秒
    if (val > 1e9) return new Date(val * 1000); // 秒
  }
  if (typeof val === 'string') {
    const d = new Date(val);
    if (!isNaN(d.getTime())) return d;
    const ts = parseInt(val, 10);
    if (!isNaN(ts)) {
      if (ts > 1e12) return new Date(ts); // 毫秒
      if (ts > 1e9) return new Date(ts * 1000); // 秒
    }
  }
  // fallback: 当前时间
  return new Date();
}

export function getTextFromMessage(message: ChatMessage): string {
  return message.parts
    .filter((part) => part.type === 'text')
    .map((part) => part.text)
    .join('');
}