"use client";

import { useState } from "react";
import { Search, Filter, Clock, FileText, Users, MessageSquare } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([
    {
      id: 1,
      title: "Test Case: User Login Validation",
      type: "test-case",
      description: "Comprehensive test case for user authentication flow including edge cases and error handling.",
      lastModified: "2024-01-15",
      author: "<PERSON>",
      tags: ["authentication", "validation", "security"]
    },
    {
      id: 2,
      title: "API Documentation: User Management",
      type: "document",
      description: "Complete API documentation for user management endpoints including CRUD operations.",
      lastModified: "2024-01-14",
      author: "<PERSON>",
      tags: ["api", "documentation", "user-management"]
    },
    {
      id: 3,
      title: "Chat: AI Assistant Integration",
      type: "chat",
      description: "Discussion about integrating AI assistant features into the application.",
      lastModified: "2024-01-13",
      author: "AI Assistant",
      tags: ["ai", "integration", "features"]
    }
  ]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'test-case':
        return <FileText className="w-4 h-4" />;
      case 'document':
        return <FileText className="w-4 h-4" />;
      case 'chat':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'test-case':
        return 'bg-blue-100 text-blue-800';
      case 'document':
        return 'bg-green-100 text-green-800';
      case 'chat':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSearch = () => {
    // 这里可以实现实际的搜索逻辑
    console.log("Searching for:", searchQuery);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Search</h1>
          <p className="text-muted-foreground">
            Search across test cases, documents, and chat history
          </p>
        </div>
      </div>

      {/* 搜索栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search for test cases, documents, or chat history..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch}>
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 搜索结果 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Search Results</h2>
          <span className="text-sm text-muted-foreground">
            {searchResults.length} results found
          </span>
        </div>

        {searchResults.map((result) => (
          <Card key={result.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getTypeIcon(result.type)}
                  <CardTitle className="text-lg">{result.title}</CardTitle>
                  <Badge className={getTypeColor(result.type)}>
                    {result.type.replace('-', ' ')}
                  </Badge>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  {result.lastModified}
                </div>
              </div>
              <CardDescription className="mt-2">
                {result.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-4 h-4" />
                  {result.author}
                </div>
                <div className="flex gap-1">
                  {result.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {searchResults.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No results found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or filters
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
