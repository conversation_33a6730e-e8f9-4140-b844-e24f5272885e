"use client";

import { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ToastProvider } from "@/components/ui/toast-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Home,
  MessageSquare,
  FileText,
  BarChart3,
  Users,
  Calendar,
  Settings,
  PanelLeft,
  PanelTop,
  Menu,
  ChevronLeft,
  ChevronRight,
  Search,
  Trash2,
  HelpCircle,
  User,
  Building2,
  LogOut,
  Command,
  AudioWaveform
} from "lucide-react";

// 导航布局类型
type NavigationLayout = 'vertical' | 'horizontal';

// 主菜单数据
const mainMenuItems = [
  { id: 'search', title: 'Search', url: '/search', icon: Search },
  { id: 'ask-ai', title: 'Ask AI', url: '/chat', icon: MessageSquare, special: true },
  { id: 'dashboard', title: 'Dashboard', url: '/dashboard', icon: Home },
  { id: 'test-case', title: 'Test Case', url: '/test-case', icon: FileText, badge: '10' },
  { id: 'documents', title: 'Documents', url: '/documents', icon: FileText, badge: '10' },
];

// 底部菜单数据（仅垂直布局）
const bottomMenuItems = [
  { id: 'settings', title: 'Settings', url: '/settings', icon: Settings },
  { id: 'trash', title: 'Trash', url: '/trash', icon: Trash2 },
  { id: 'help', title: 'Help', url: '/help', icon: HelpCircle },
];

interface MinimalLayoutManagerProps {
  children: React.ReactNode;
}

export function MinimalLayoutManager({ children }: MinimalLayoutManagerProps) {
  const pathname = usePathname();
  const [layout, setLayout] = useState<NavigationLayout>('vertical');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // 处理特殊按钮点击
  const handleSpecialClick = (item: any, e: React.MouseEvent) => {
    if (item.id === 'ask-ai' && pathname.startsWith('/test-case')) {
      e.preventDefault();
      // 在test-case页面时，触发页面内AI助手
      // 这里可以发送自定义事件或调用全局状态管理
      window.dispatchEvent(new CustomEvent('toggle-ai-assistant'));
      return;
    }
    // 其他特殊按钮的处理逻辑可以在这里添加
  };

  // 初始化状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLayout = localStorage.getItem('navigation-layout') as NavigationLayout;
      if (savedLayout && (savedLayout === 'vertical' || savedLayout === 'horizontal')) {
        setLayout(savedLayout);
      }

      const savedCollapsed = localStorage.getItem('sidebar-collapsed');
      if (savedCollapsed !== null) {
        setSidebarCollapsed(savedCollapsed === 'true');
      }
    }
  }, []);

  // 监听设置页面的更改事件
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleSettingsChange = (event: CustomEvent) => {
        const { layout: newLayout, sidebarCollapsed: newCollapsed } = event.detail;
        setLayout(newLayout);
        setSidebarCollapsed(newCollapsed);
      };

      window.addEventListener('settings-changed', handleSettingsChange as EventListener);

      return () => {
        window.removeEventListener('settings-changed', handleSettingsChange as EventListener);
      };
    }
  }, []);

  // 保存状态到 localStorage
  const saveLayout = (newLayout: NavigationLayout) => {
    setLayout(newLayout);
    if (typeof window !== 'undefined') {
      localStorage.setItem('navigation-layout', newLayout);
    }
  };

  const saveSidebarCollapsed = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed);
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', collapsed.toString());
    }
  };

  // 切换布局
  const toggleLayout = () => {
    const newLayout = layout === 'vertical' ? 'horizontal' : 'vertical';
    saveLayout(newLayout);
  };

  // 切换侧边栏
  const toggleSidebar = () => {
    saveSidebarCollapsed(!sidebarCollapsed);
  };

  // 生成面包屑
  const generateBreadcrumbs = (pathname: string) => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ title: 'Home', url: '/' }];

    let currentPath = '';
    segments.forEach((segment) => {
      currentPath += `/${segment}`;
      // 尝试从主菜单和底部菜单中找到对应的标题
      const allMenuItems = [...mainMenuItems, ...bottomMenuItems];
      const menuItem = allMenuItems.find(item => item.url === currentPath);
      const title = menuItem?.title || segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
      breadcrumbs.push({ title, url: currentPath });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs(pathname);

  // 检查是否是特殊路由
  const isAuthRoute = pathname.startsWith("/login") || pathname.startsWith("/register");
  if (isAuthRoute) {
    return (
      <div className="flex h-dvh w-screen items-center justify-center bg-background">
        {children}
      </div>
    );
  }

  // 垂直布局
  if (layout === 'vertical') {
    return (
      <>
        <ToastProvider />
        <div className="flex h-screen">
        {/* 侧边栏 */}
        <aside
          className={cn(
            "border-r border-slate-200 fixed left-0 top-0 z-40 h-screen bg-sidebar border-r border-sidebar-border transition-all duration-300 border-r border-slate-200",
            sidebarCollapsed ? "w-16" : "w-64"
          )}
        >
          <div className="flex h-full flex-col">
            {/* 侧边栏头部 - 项目切换按钮 */}
            <div className="p-2 border-b border-sidebar-border">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "flex items-center gap-2 w-full p-2 text-sm text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",
                      sidebarCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div className="w-6 h-6 bg-primary/10 rounded flex items-center justify-center text-xs font-bold flex-shrink-0">
                      <Command className="w-4 h-4 text-primary" />
                    </div>
                    {!sidebarCollapsed && (
                      <>
                        <div className="flex flex-col items-start flex-1">
                          <span className="text-sm font-semibold leading-none text-foreground">AI Run</span>
                          <span className="text-xs text-muted-foreground leading-none">Acme Inc</span>
                        </div>
                        <ChevronRight className="w-4 h-4 ml-auto" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-64">
                  <div className="p-3">
                    <div className="text-sm font-medium mb-2">Switch Project</div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-3 p-2 bg-primary/10 rounded-md">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Command className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm">Acme Inc</div>
                          <div className="text-xs text-muted-foreground">Current Project</div>
                        </div>
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                      </div>
                      <div className="flex items-center gap-3 p-2 rounded-md cursor-pointer hover:bg-accent">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <AudioWaveform className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm">Beta Corp</div>
                          <div className="text-xs text-muted-foreground">Switch to this project</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-2 rounded-md cursor-pointer hover:bg-accent">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Building2 className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm">Gamma Ltd</div>
                          <div className="text-xs text-muted-foreground">Switch to this project</div>
                        </div>
                      </div>
                    </div>
                    <DropdownMenuSeparator className="my-3" />
                    <Button variant="outline" size="sm" className="w-full border-primary/20 text-primary">
                      <Building2 className="w-4 h-4 mr-2 text-primary" />
                      Manage Projects
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            {/* 主导航菜单 */}
            <div className="flex-1 flex flex-col">
              <nav className="flex flex-col gap-1 p-2">
                {mainMenuItems.map((item) => {
                  const exactMatch = pathname === item.url;
                  const subPathMatch = item.url !== "/" && pathname.startsWith(item.url);
                  const isActive = exactMatch || subPathMatch;

                  return (
                    <Link
                      key={item.id}
                      href={item.url}
                      onClick={(e) => item.special ? handleSpecialClick(item, e) : undefined}
                      className={cn(
                        "flex items-center gap-3 text-sm font-medium rounded-md transition-all duration-200 w-full",
                        sidebarCollapsed ? "justify-center py-2 px-2" : "py-2 px-3",
                        "hover:bg-primary/10",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "text-sidebar-foreground hover:text-primary"
                      )}
                    >
                      <span className="flex items-center justify-center flex-shrink-0 size-5">
                        <item.icon className="size-4" />
                      </span>
                      {!sidebarCollapsed && (
                        <>
                          <span className="truncate">{item.title}</span>
                          {item.badge && (
                            <span className={cn(
                              "ml-auto text-xs rounded-full px-2 py-0.5",
                              isActive ? "bg-primary-foreground/20 text-primary-foreground" : "bg-primary/10 text-primary"
                            )}>
                              {item.badge}
                            </span>
                          )}
                        </>
                      )}
                    </Link>
                  );
                })}
              </nav>

              {/* Recent Chat 区域 */}
              {!sidebarCollapsed && (
                <div className="px-2 py-4">
                  <div className="text-xs text-muted-foreground mb-2">Recent Chat</div>
                  {/* 这里可以添加最近聊天记录 */}
                </div>
              )}
            </div>
            
            {/* 侧边栏底部 */}
            <div className="border-t border-sidebar-border">
              {/* 底部菜单 */}
              <nav className="flex flex-col gap-1 p-2">
                {bottomMenuItems.map((item) => {
                  const exactMatch = pathname === item.url;
                  const subPathMatch = item.url !== "/" && pathname.startsWith(item.url);
                  const isActive = exactMatch || subPathMatch;

                  return (
                    <Link
                      key={item.id}
                      href={item.url}
                      className={cn(
                        "flex items-center gap-3 text-sm font-medium rounded-md transition-all duration-200 w-full",
                        sidebarCollapsed ? "justify-center py-2 px-2" : "py-2 px-3",
                        "hover:bg-primary/10",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "text-sidebar-foreground hover:text-primary"
                      )}
                    >
                      <span className="flex items-center justify-center flex-shrink-0 size-5">
                        <item.icon className="size-4" />
                      </span>
                      {!sidebarCollapsed && (
                        <span className="truncate">{item.title}</span>
                      )}
                    </Link>
                  );
                })}
              </nav>


            </div>
          </div>
        </aside>

        {/* 主内容区 */}
        <div 
          className={cn(
            "flex-1 flex flex-col min-h-0 transition-all duration-300",
            sidebarCollapsed ? "ml-16" : "ml-64"
          )}
        >
          {/* 头部 */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-white/80 backdrop-blur-sm border-slate-200">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleSidebar}
              className="-ml-1"
            >
              <Menu className="w-4 h-4" />
            </Button>
            
            {/* 面包屑 */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground ml-4">
              {breadcrumbs.map((bc, idx) => (
                <div key={bc.url} className="flex items-center gap-2">
                  {idx > 0 && <span>/</span>}
                  {idx < breadcrumbs.length - 1 ? (
                    <Link href={bc.url} className="hover:text-foreground">
                      {bc.title}
                    </Link>
                  ) : (
                    <span className="text-foreground font-medium">{bc.title}</span>
                  )}
                </div>
              ))}
            </div>

            {/* 右侧工具栏 */}
            <div className="ml-auto flex items-center gap-2">
              {/* 可以在这里添加其他工具按钮 */}
            </div>
          </header>
          
          {/* 主内容 */}
          <main className="flex-1 min-h-0">
            {children}
          </main>
        </div>
        </div>
      </>
    );
  }

  // 水平布局
  return (
    <>
      <ToastProvider />
      <div className="flex flex-col h-screen">
        {/* 第一层Header - 公共工具栏 */}
        <header className="flex h-14 shrink-0 items-center justify-between gap-4 border-b px-6 bg-white border-slate-200">
          {/* 左侧：项目切换按钮 */}
          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 px-2 py-1 rounded-md"
                >
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Command className="w-4 h-4 text-primary" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="text-lg font-semibold leading-none">AI Run</span>
                    <span className="text-xs text-muted-foreground leading-none">Acme Inc</span>
                  </div>
                  <ChevronRight className="w-4 h-4 text-primary" />
                </Button>
              </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-64">
              <div className="p-3">
                <div className="text-sm font-medium mb-2">Switch Project</div>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 p-2 bg-primary/10 rounded-md">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Command className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">Acme Inc</div>
                      <div className="text-xs text-muted-foreground">Current Project</div>
                    </div>
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md cursor-pointer">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <AudioWaveform className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">Beta Corp</div>
                      <div className="text-xs text-muted-foreground">Switch to this project</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md cursor-pointer">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Building2 className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">Gamma Ltd</div>
                      <div className="text-xs text-muted-foreground">Switch to this project</div>
                    </div>
                  </div>
                </div>
                <DropdownMenuSeparator className="my-3" />
                <Button variant="outline" size="sm" className="w-full border-primary/20 text-primary">
                  <Building2 className="w-4 h-4 mr-2 text-primary" />
                  Manage Projects
                </Button>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
          </div>

          {/* 中间：搜索框 */}
          <div className="flex-1 max-w-md mx-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="搜索..."
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50"
              />
            </div>
          </div>

          {/* 右侧：用户头像和工具 */}
          <div className="flex items-center gap-3">
            {/* 通知按钮 */}
            <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
              <div className="relative">
                <div className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center">
                  <span className="text-xs">🔔</span>
                </div>
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
            </Button>

            {/* 用户头像下拉菜单 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-8 h-8 p-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full hover:shadow-md transition-shadow"
                >
                  <span className="text-white font-semibold text-sm">U</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <div className="flex items-center gap-3 p-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">U</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">User</span>
                    <span className="text-xs text-muted-foreground"><EMAIL></span>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Link href="/settings" className="flex items-center gap-3 w-full">
                    <Settings className="w-4 h-4" />
                    <span>Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Link href="/trash" className="flex items-center gap-3 w-full">
                    <Trash2 className="w-4 h-4" />
                    <span>Trash</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="/help" className="flex items-center gap-3 w-full">
                    <HelpCircle className="w-4 h-4" />
                    <span>Help</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="flex items-center gap-3 p-3 text-red-600 focus:text-red-600">
                  <LogOut className="w-4 h-4" />
                  <span>Sign Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* 第二层Header - 导航菜单 */}
        <header className="flex h-12 shrink-0 items-center gap-4 border-b px-6 bg-white shadow-sm border-slate-200">
          <nav className="flex items-center gap-1">
            {mainMenuItems.map((item) => {
              const exactMatch = pathname === item.url;
              const subPathMatch = item.url !== "/" && pathname.startsWith(item.url);
              const isActive = exactMatch || subPathMatch;

              return (
                <Link
                  key={item.id}
                  href={item.url}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted"
                  )}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.title}</span>
                  {item.badge && (
                    <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>
        </header>

        {/* 主内容 */}
        <main className="flex-1 min-h-0">
          {children}
        </main>
      </div>
    </>
  );
}
