'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import TestCaseLayout from './components/testcase-layout';
import { toast } from '@/components/chat/toast';
import InformationModule from './components/information-module';
import TestStepsModule from './components/test-steps-Module';
import AutomationModule from './components/automation-module';
import RequirementsModule from './components/requirements-module';
import DatasetModule from './components/dataset-module';
import TestRunsModule from './components/test-runs-module';
import KnownIssuesModule from './components/knownIssues-module';
import { TestCase } from './types';

// Mock data
const mockTestCases: Record<string, TestCase> = {
  '1-1-1': {
    id: '1-1-1',
    name: 'Login with valid user',
    description: 'Test the login functionality with valid user credentials to ensure proper authentication flow and user experience',
    preconditions: 'User account must be created and activated. Application must be running and accessible. Database connection must be established.',
    priority: 'high',
    status: 'work-in-progress',
    weight: 'low',
    format: 'classic',
    nature: 'functional',
    type: 'regression',
    tags: ['login', 'authentication', 'smoke'],
    createdAt: '2021-09-23T00:19:00Z',
    updatedAt: '2023-10-05T22:51:00Z',
    author: 'henix_admin',
    modifier: 'guest_tr',
    executionTime: 45,
    lastRun: '2024-01-20 14:30:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to login page',
        expected: 'Login page should be displayed with username and password fields',
        type: 'manual',
        notes: 'Ensure browser is in incognito mode'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Enter valid username and password',
        expected: 'Credentials should be accepted without validation errors',
        type: 'manual'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Click login button',
        expected: 'User should be redirected to dashboard with welcome message',
        type: 'automated',
        notes: 'This step can be automated using Selenium'
      }
    ],
    automationConfigs: {
      midscene: {
        id: 'auto-1-1-1',
        repository: 'https://github.com/company/test-automation',
        branch: 'main',
        commands: [
          'npm install',
          'npm run test:login',
          'npm run test:report'
        ],
        parameters: {
          'browser': 'chrome',
          'headless': 'true',
          'timeout': '30000',
          'viewport': '1920x1080',
          'retries': '3'
        },
        framework: 'midscene',
        browser: 'chrome',
        environment: 'test',
        isActive: true,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T14:30:00Z'
      },
      playwright: {
        id: 'auto-1-1-1-pw',
        repository: 'https://github.com/company/playwright-tests',
        branch: 'develop',
        commands: [
          'npm install',
          'npx playwright test login',
          'npx playwright show-report'
        ],
        parameters: {
          'browser': 'firefox',
          'headless': 'false',
          'timeout': '45000',
          'workers': '2',
          'retries': '1'
        },
        framework: 'playwright',
        browser: 'firefox',
        environment: 'staging',
        isActive: false,
        createdAt: '2024-01-10T09:00:00Z',
        updatedAt: '2024-01-15T16:30:00Z'
      }
    },
    relatedRequirements: [
      {
        id: 'REQ-001',
        type: 'story',
        title: 'User Authentication System',
        status: 'done',
        assignee: 'john.doe',
        url: 'https://jira.company.com/REQ-001'
      },
      {
        id: 'EPIC-123',
        type: 'epic',
        title: 'Security Enhancement Phase 1',
        status: 'in-progress',
        assignee: 'jane.smith'
      }
    ],
    datasets: [
      {
        id: 'dataset-1',
        name: 'User Credentials',
        columns: [
          { name: 'username', type: 'string', required: true },
          { name: 'password', type: 'string', required: true },
          { name: 'expected_result', type: 'string', required: true }
        ],
        data: [
          { username: 'admin', password: 'admin123', expected_result: 'success' },
          { username: 'user1', password: 'password1', expected_result: 'success' },
          { username: 'invalid', password: 'wrong', expected_result: 'failure' }
        ]
      }
    ],
    testRuns: [
      {
        id: 'run-1',
        runDate: '2024-01-24T14:30:00Z',
        status: 'passed',
        duration: 45,
        environment: 'staging',
        executor: 'john.doe',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 15 },
          { stepId: 'step-2', status: 'passed', duration: 10 },
          { stepId: 'step-3', status: 'passed', duration: 20 }
        ]
      },
      {
        id: 'run-2',
        runDate: '2024-01-23T16:45:00Z',
        status: 'failed',
        duration: 38,
        environment: 'production',
        executor: 'jane.smith',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 15 },
          { stepId: 'step-2', status: 'failed', duration: 23, error: 'Element not found: login button' }
        ]
      },
      {
        id: 'run-3',
        runDate: '2024-01-23T10:15:00Z',
        status: 'passed',
        duration: 42,
        environment: 'staging',
        executor: 'mike.wilson',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 12 },
          { stepId: 'step-2', status: 'passed', duration: 18 },
          { stepId: 'step-3', status: 'passed', duration: 12 }
        ]
      },
      {
        id: 'run-4',
        runDate: '2024-01-22T09:30:00Z',
        status: 'skipped',
        duration: 0,
        environment: 'development',
        executor: 'automation-bot',
        results: []
      },
      {
        id: 'run-5',
        runDate: '2024-01-21T15:20:00Z',
        status: 'passed',
        duration: 51,
        environment: 'staging',
        executor: 'sarah.connor',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 18 },
          { stepId: 'step-2', status: 'passed', duration: 15 },
          { stepId: 'step-3', status: 'passed', duration: 18 }
        ]
      },
      {
        id: 'run-6',
        runDate: '2024-01-20T11:45:00Z',
        status: 'failed',
        duration: 25,
        environment: 'production',
        executor: 'alex.turner',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 14 },
          { stepId: 'step-2', status: 'failed', duration: 11, error: 'Timeout waiting for page load' }
        ]
      },
      {
        id: 'run-7',
        runDate: '2024-01-19T14:10:00Z',
        status: 'passed',
        duration: 39,
        environment: 'staging',
        executor: 'lisa.chen',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 13 },
          { stepId: 'step-2', status: 'passed', duration: 12 },
          { stepId: 'step-3', status: 'passed', duration: 14 }
        ]
      },
      {
        id: 'run-8',
        runDate: '2024-01-18T08:30:00Z',
        status: 'running',
        duration: 0,
        environment: 'staging',
        executor: 'automation-bot',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 16 }
        ]
      }
    ],
    knownIssues: [
      {
        id: 'issue-1',
        title: 'Login fails on Safari browser',
        description: 'The login functionality does not work properly on Safari due to cookie handling issues',
        severity: 'medium',
        status: 'investigating',
        reporter: 'test.user',
        assignee: 'dev.team',
        createdAt: '2024-01-15T09:00:00Z',
        bugUrl: 'https://jira.company.com/BUG-456'
      }
    ]
  },

  '1-1-2': {
    id: '1-1-2',
    name: 'Login with invalid credentials',
    description: 'Test login functionality with invalid credentials to ensure proper error handling and security measures',
    preconditions: 'Application must be running and accessible. Test user accounts should be available.',
    priority: 'high',
    status: 'active',
    weight: 'low',
    format: 'classic',
    nature: 'functional',
    type: 'regression',
    tags: ['login', 'authentication', 'negative', 'security'],
    createdAt: '2021-09-23T00:19:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
    author: 'henix_admin',
    modifier: 'henix_admin',
    executionTime: 30,
    lastRun: '2024-01-18 16:45:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to login page',
        expected: 'Login page should be displayed',
        type: 'manual'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Enter invalid username and password',
        expected: 'Error message should be displayed',
        type: 'automated',
        notes: 'Test multiple invalid combinations'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Verify account lockout after multiple attempts',
        expected: 'Account should be temporarily locked',
        type: 'automated'
      }
    ],
    automationConfigs: {
      playwright: {
        id: 'auto-1-1-2',
        repository: 'https://github.com/company/security-tests',
        branch: 'develop',
        commands: [
          'npm install',
          'npx playwright test login-negative',
          'npx playwright show-report'
        ],
        parameters: {
          'browser': 'firefox',
          'headless': 'false',
          'timeout': '45000',
          'workers': '2',
          'retries': '1',
          'video': 'retain-on-failure'
        },
        framework: 'playwright',
        browser: 'firefox',
        environment: 'staging',
        isActive: true,
        createdAt: '2024-01-10T09:30:00Z',
        updatedAt: '2024-01-18T16:45:00Z'
      },
      selenium: {
        id: 'auto-1-1-2-sel',
        repository: 'https://github.com/company/selenium-tests',
        branch: 'main',
        commands: [
          'pip install -r requirements.txt',
          'python -m pytest tests/security/ -v',
          'python -m pytest --html=report.html'
        ],
        parameters: {
          'browser': 'chrome',
          'headless': 'true',
          'timeout': '30',
          'retries': '2',
          'parallel': 'true'
        },
        framework: 'selenium',
        browser: 'chrome',
        environment: 'test',
        isActive: true,
        createdAt: '2024-01-08T14:00:00Z',
        updatedAt: '2024-01-18T16:45:00Z'
      }
    },
    relatedRequirements: [
      {
        id: 'SEC-001',
        type: 'story',
        title: 'Implement secure login validation',
        status: 'done',
        assignee: 'security.team',
        url: 'https://jira.company.com/browse/SEC-001'
      }
    ],
    datasets: [],
    testRuns: [
      {
        id: 'run-3',
        runDate: '2024-01-24T16:45:00Z',
        status: 'passed',
        duration: 28,
        environment: 'staging',
        executor: 'security.bot',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 8 },
          { stepId: 'step-2', status: 'passed', duration: 12 },
          { stepId: 'step-3', status: 'passed', duration: 8 }
        ]
      },
      {
        id: 'run-9',
        runDate: '2024-01-23T14:20:00Z',
        status: 'failed',
        duration: 35,
        environment: 'production',
        executor: 'qa.engineer',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 10 },
          { stepId: 'step-2', status: 'failed', duration: 25, error: 'Invalid credentials error message not displayed' }
        ]
      },
      {
        id: 'run-10',
        runDate: '2024-01-22T09:15:00Z',
        status: 'passed',
        duration: 32,
        environment: 'staging',
        executor: 'automation-bot',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 9 },
          { stepId: 'step-2', status: 'passed', duration: 14 },
          { stepId: 'step-3', status: 'passed', duration: 9 }
        ]
      },
      {
        id: 'run-11',
        runDate: '2024-01-21T13:30:00Z',
        status: 'skipped',
        duration: 0,
        environment: 'development',
        executor: 'dev.team',
        results: []
      }
    ],
    knownIssues: []
  },

  '1-2-1': {
    id: '1-2-1',
    name: 'User profile update',
    description: 'Test user profile information update functionality including validation and data persistence',
    preconditions: 'User must be logged in with valid session. Profile page must be accessible.',
    priority: 'medium',
    status: 'active',
    weight: 'medium',
    format: 'classic',
    nature: 'functional',
    type: 'regression',
    tags: ['profile', 'update', 'validation'],
    createdAt: '2021-10-15T14:20:00Z',
    updatedAt: '2024-01-22T11:15:00Z',
    author: 'henix_admin',
    modifier: 'qa.team',
    executionTime: 60,
    lastRun: '2024-01-22 11:15:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to user profile page',
        expected: 'Profile page loads with current user information',
        type: 'automated'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Update profile fields with valid data',
        expected: 'Fields accept new values and show validation feedback',
        type: 'automated'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Save profile changes',
        expected: 'Success message displayed and data persisted',
        type: 'automated'
      },
      {
        id: 'step-4',
        step: 4,
        action: 'Verify changes in database',
        expected: 'Updated data matches input values',
        type: 'automated',
        notes: 'Database verification step'
      }
    ],
    automationConfigs: {
      cypress: {
        id: 'auto-1-2-1',
        repository: 'https://github.com/company/e2e-tests',
        branch: 'feature/profile-tests',
        commands: [
          'yarn install',
          'yarn cypress:run --spec "cypress/e2e/profile/**"',
          'yarn cypress:report'
        ],
        parameters: {
          'browser': 'chrome',
          'headless': 'true',
          'timeout': '60000',
          'baseUrl': 'https://staging.company.com',
          'video': 'true',
          'screenshots': 'true',
          'env': 'staging'
        },
        framework: 'cypress',
        browser: 'chrome',
        environment: 'staging',
        isActive: true,
        createdAt: '2024-01-12T08:45:00Z',
        updatedAt: '2024-01-22T11:15:00Z'
      },
      midscene: {
        id: 'auto-1-2-1-ms',
        repository: 'https://github.com/company/midscene-tests',
        branch: 'main',
        commands: [
          'npm install',
          'npm run test:profile',
          'npm run test:report'
        ],
        parameters: {
          'browser': 'chrome',
          'headless': 'true',
          'timeout': '45000',
          'viewport': '1366x768',
          'retries': '2'
        },
        framework: 'midscene',
        browser: 'chrome',
        environment: 'test',
        isActive: false,
        createdAt: '2024-01-20T10:30:00Z',
        updatedAt: '2024-01-22T11:15:00Z'
      }
    },
    relatedRequirements: [
      {
        id: 'PROF-123',
        type: 'story',
        title: 'User profile management enhancement',
        status: 'in-progress',
        assignee: 'frontend.team',
        url: 'https://jira.company.com/browse/PROF-123'
      }
    ],
    datasets: [
      {
        id: 'dataset-1',
        name: 'Profile Test Data',
        columns: [
          { name: 'firstName', type: 'string', required: true },
          { name: 'lastName', type: 'string', required: true },
          { name: 'email', type: 'string', required: true },
          { name: 'phone', type: 'string', required: false }
        ],
        data: [
          { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '+1234567890' },
          { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', phone: '+0987654321' }
        ]
      }
    ],
    testRuns: [
      {
        id: 'run-4',
        runDate: '2024-01-22T11:15:00Z',
        status: 'passed',
        duration: 58,
        environment: 'staging',
        executor: 'cypress.runner',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 12 },
          { stepId: 'step-2', status: 'passed', duration: 18 },
          { stepId: 'step-3', status: 'passed', duration: 15 },
          { stepId: 'step-4', status: 'passed', duration: 13 }
        ]
      }
    ],
    knownIssues: []
  },

  '2-1-1': {
    id: '2-1-1',
    name: 'API Authentication Test',
    description: 'Test API authentication endpoints and token validation mechanisms',
    preconditions: 'API server must be running. Test credentials must be configured.',
    priority: 'high',
    status: 'work-in-progress',
    weight: 'high',
    format: 'classic',
    nature: 'functional',
    type: 'integration',
    tags: ['api', 'authentication', 'security', 'integration'],
    createdAt: '2024-01-20T09:00:00Z',
    updatedAt: '2024-01-23T15:30:00Z',
    author: 'api.team',
    modifier: 'api.team',
    executionTime: 25,
    lastRun: '2024-01-23 15:30:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Send POST request to /auth/login with valid credentials',
        expected: 'Receive 200 status with valid JWT token',
        type: 'automated'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Use token to access protected endpoint',
        expected: 'Receive 200 status with expected data',
        type: 'automated'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Test token expiration handling',
        expected: 'Receive 401 status when token expires',
        type: 'automated'
      }
    ],
    automationConfigs: {
      selenium: {
        id: 'auto-2-1-1',
        repository: 'https://github.com/company/api-tests',
        branch: 'main',
        commands: [
          'pip install -r requirements.txt',
          'python -m pytest tests/auth/ -v',
          'python -m pytest --html=report.html'
        ],
        parameters: {
          'base_url': 'https://api.staging.company.com',
          'timeout': '30',
          'retries': '2',
          'parallel': 'true',
          'markers': 'auth and not slow',
          'env_file': '.env.staging'
        },
        framework: 'selenium',
        browser: 'chrome',
        environment: 'staging',
        isActive: true,
        createdAt: '2024-01-20T09:00:00Z',
        updatedAt: '2024-01-23T15:30:00Z'
      },
      playwright: {
        id: 'auto-2-1-1-pw',
        repository: 'https://github.com/company/playwright-api',
        branch: 'api-tests',
        commands: [
          'npm install',
          'npx playwright test api/auth',
          'npx playwright show-report'
        ],
        parameters: {
          'baseURL': 'https://api.staging.company.com',
          'timeout': '30000',
          'retries': '1',
          'workers': '3',
          'reporter': 'html'
        },
        framework: 'playwright',
        browser: 'chrome',
        environment: 'staging',
        isActive: true,
        createdAt: '2024-01-22T11:00:00Z',
        updatedAt: '2024-01-23T15:30:00Z'
      },
      cypress: {
        id: 'auto-2-1-1-cy',
        repository: 'https://github.com/company/cypress-api',
        branch: 'main',
        commands: [
          'npm install',
          'npx cypress run --spec "cypress/e2e/api/auth/**"',
          'npx cypress run --reporter mochawesome'
        ],
        parameters: {
          'baseUrl': 'https://api.staging.company.com',
          'timeout': '30000',
          'video': 'false',
          'screenshotOnRunFailure': 'true',
          'env': 'staging'
        },
        framework: 'cypress',
        browser: 'chrome',
        environment: 'staging',
        isActive: false,
        createdAt: '2024-01-18T09:00:00Z',
        updatedAt: '2024-01-23T15:30:00Z'
      }
    },
    relatedRequirements: [
      {
        id: 'API-456',
        type: 'epic',
        title: 'API Security Enhancement',
        status: 'in-progress',
        assignee: 'backend.team',
        url: 'https://jira.company.com/browse/API-456'
      }
    ],
    datasets: [
      {
        id: 'dataset-2',
        name: 'API Test Credentials',
        columns: [
          { name: 'username', type: 'string', required: true },
          { name: 'password', type: 'string', required: true },
          { name: 'role', type: 'string', required: true },
          { name: 'expected_status', type: 'number', required: true }
        ],
        data: [
          { username: 'admin', password: 'admin123', role: 'admin', expected_status: 200 },
          { username: 'user', password: 'user123', role: 'user', expected_status: 200 },
          { username: 'invalid', password: 'wrong', role: 'none', expected_status: 401 }
        ]
      }
    ],
    testRuns: [
      {
        id: 'run-5',
        runDate: '2024-01-23T15:30:00Z',
        status: 'failed',
        duration: 22,
        environment: 'staging',
        executor: 'pytest.runner',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 8 },
          { stepId: 'step-2', status: 'passed', duration: 6 },
          { stepId: 'step-3', status: 'failed', duration: 8, error: 'Token expiration test failed - token still valid after expected expiry' }
        ]
      }
    ],
    knownIssues: [
      {
        id: 'issue-1',
        title: 'Token expiration not working correctly',
        description: 'JWT tokens are not expiring as expected in staging environment',
        severity: 'medium',
        status: 'open',
        reporter: 'api.team',
        assignee: 'backend.team',
        bugUrl: 'https://jira.company.com/browse/BUG-789',
        createdAt: '2024-01-23T15:35:00Z'
      }
    ]
  }
};

export default function TestCasePage() {
  const params = useParams();
  const id = params.id as string;

  const [testCase, setTestCase] = useState<TestCase | null>(null);
  const [activeTab, setActiveTab] = useState('information');
  const [isEditing, setIsEditing] = useState(false);
  const [isRunning, setIsRunning] = useState(false);

  // 加载自动化配置数据
  const loadAutomationConfigs = async (testCaseId: string) => {
    try {
      const response = await fetch(`/api/automation-config?testCaseId=${testCaseId}`);
      if (response.ok) {
        const configs = await response.json();
        return configs;
      }
    } catch (error) {
      console.error('Error loading automation configs:', error);
    }
    return {};
  };

  useEffect(() => {
    const loadTestCase = async () => {
      try {
        // 首先尝试从API加载真实数据
        const response = await fetch(`/api/test-case?id=${id}`);
        if (response.ok) {
          const apiTestCase = await response.json();
          console.log('API returned test case data:', apiTestCase);
          console.log('API returned steps:', apiTestCase.steps);
          console.log('Steps count from API:', apiTestCase.steps?.length);

          // 加载自动化配置
          const automationConfigs = await loadAutomationConfigs(id);

          // 转换API数据格式为组件期望的格式
          const formattedTestCase: TestCase = {
            id: apiTestCase.id,
            name: apiTestCase.name,
            description: apiTestCase.description || '',
            preconditions: apiTestCase.preconditions || '',
            priority: apiTestCase.priority || 'medium',
            status: apiTestCase.status || 'draft',
            weight: apiTestCase.weight || 'medium',
            format: apiTestCase.format || 'classic',
            nature: apiTestCase.nature || 'functional',
            type: apiTestCase.type || 'regression',
            tags: Array.isArray(apiTestCase.tags) ? apiTestCase.tags :
                  (typeof apiTestCase.tags === 'string' ?
                    (() => {
                      try {
                        return JSON.parse(apiTestCase.tags);
                      } catch (e) {
                        console.warn('Failed to parse tags JSON:', apiTestCase.tags);
                        return [];
                      }
                    })() : []),
            createdAt: new Date(apiTestCase.createdAt).toISOString(),
            updatedAt: new Date(apiTestCase.updatedAt).toISOString(),
            author: apiTestCase.createdBy || 'Unknown',
            modifier: apiTestCase.updatedBy || 'Unknown',
            executionTime: apiTestCase.executionTime || 0,
            lastRun: apiTestCase.lastRunAt ? new Date(apiTestCase.lastRunAt).toISOString() : undefined,
            steps: apiTestCase.steps || [], // 使用API返回的步骤数据
            automationConfigs: Object.keys(automationConfigs).length > 0 ? automationConfigs : undefined,
            relatedRequirements: apiTestCase.relatedRequirements || [],
            datasets: apiTestCase.datasets || [],
            testRuns: apiTestCase.testRuns || [],
            knownIssues: apiTestCase.knownIssues || []
          };
          console.log('Formatted test case:', formattedTestCase);
          console.log('Formatted steps:', formattedTestCase.steps);
          console.log('Formatted steps count:', formattedTestCase.steps?.length);
          setTestCase(formattedTestCase);
        } else {
          // 如果API失败，尝试使用mock数据
          const mockTestCase = mockTestCases[id];
          if (mockTestCase) {
            setTestCase(mockTestCase);
          } else {
            console.error('Test case not found');
          }
        }
      } catch (error) {
        console.error('Error loading test case:', error);
        // 尝试使用mock数据作为fallback
        const mockTestCase = mockTestCases[id];
        if (mockTestCase) {
          setTestCase(mockTestCase);
        }
      }
    };

    loadTestCase();
  }, [id]);

  const handleUpdate = async (updates: Partial<TestCase>) => {
    console.log('TestCase page received update:', updates);
    console.log('Update keys:', Object.keys(updates));
    console.log('Update values:', Object.values(updates));

    // 特别检查steps数据
    if (updates.steps) {
      console.log('Steps data received:', updates.steps);
      console.log('Steps count:', updates.steps.length);
      console.log('First step:', updates.steps[0]);
    }
    if (testCase) {
      const updatedTestCase = {
        ...testCase,
        ...updates,
        updatedAt: new Date().toISOString(),
        updatedBy: 'ai-assistant' // 标记为AI更新
      };
      console.log('Updated test case:', updatedTestCase);

      try {
        // 保存到数据库
        const response = await fetch(`/api/test-case?id=${testCase.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedTestCase),
        });

        if (!response.ok) {
          throw new Error('Failed to save test case');
        }

        // 更新本地状态
        setTestCase(updatedTestCase);

        // 显示成功提示
        toast({
          type: 'success',
          description: 'Test case updated successfully by AI',
        });

        console.log('Test case saved to database successfully');
      } catch (error) {
        console.error('Failed to save test case:', error);
        toast({
          type: 'error',
          description: 'Failed to save test case updates',
        });
      }
    }
  };

  const handleSave = () => {
    // 保存逻辑
    console.log('Saving test case:', testCase);
    setIsEditing(false);
  };

  const handleAIGenerate = () => {
    // AI生成逻辑
    console.log(`Generating AI content for ${activeTab}`);
  };

  const handleRunTest = () => {
    setIsRunning(true);
    // 模拟测试运行
    setTimeout(() => {
      setIsRunning(false);
      console.log('Test completed');
    }, 3000);
  };

  if (!testCase) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading test case...</p>
        </div>
      </div>
    );
  }

  const renderModule = () => {
    const moduleProps = {
      testCase,
      isEditing,
      onUpdate: handleUpdate,
      onAIGenerate: handleAIGenerate
    };

    switch (activeTab) {
      case 'information':
        return <InformationModule {...moduleProps} />;
      case 'steps':
        return <TestStepsModule {...moduleProps} />;
      case 'automation':
        return <AutomationModule {...moduleProps} />;
      case 'requirements':
        return <RequirementsModule {...moduleProps} />;
      case 'dataset':
        return <DatasetModule {...moduleProps} />;
      case 'testruns':
        return <TestRunsModule {...moduleProps} onRunTest={handleRunTest} />;
      case 'issues':
        return <KnownIssuesModule {...moduleProps} />;
      default:
        return <InformationModule {...moduleProps} />;
    }
  };

  return (
    <TestCaseLayout
      testCase={testCase}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      isEditing={isEditing}
      onEditToggle={() => setIsEditing(!isEditing)}
      onSave={handleSave}
      onAIGenerate={handleAIGenerate}
      onRunTest={handleRunTest}
      isRunning={isRunning}
      onTestCaseUpdate={handleUpdate}
    >
      {renderModule()}
    </TestCaseLayout>
  );
}