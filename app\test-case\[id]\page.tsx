'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import TestCaseLayout from './components/TestCaseLayout';
import { toast } from '@/components/chat/toast';
import InformationModule from './components/InformationModule';
import TestStepsModule from './components/TestStepsModule';
import AutomationModule from './components/AutomationModule';
import RequirementsModule from './components/RequirementsModule';
import DatasetModule from './components/DatasetModule';
import TestRunsModule from './components/TestRunsModule';
import KnownIssuesModule from './components/KnownIssuesModule';
import { TestCase } from './types';

// Mock data
const mockTestCases: Record<string, TestCase> = {
  '1-1-1': {
    id: '1-1-1',
    name: 'Login with valid user',
    description: 'Test the login functionality with valid user credentials to ensure proper authentication flow and user experience',
    preconditions: 'User account must be created and activated. Application must be running and accessible. Database connection must be established.',
    priority: 'high',
    status: 'work-in-progress',
    weight: 'low',
    format: 'classic',
    nature: 'functional',
    type: 'regression',
    tags: ['login', 'authentication', 'smoke'],
    createdAt: '2021-09-23T00:19:00Z',
    updatedAt: '2023-10-05T22:51:00Z',
    author: 'henix_admin',
    modifier: 'guest_tr',
    executionTime: 45,
    lastRun: '2024-01-20 14:30:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to login page',
        expected: 'Login page should be displayed with username and password fields',
        type: 'manual',
        notes: 'Ensure browser is in incognito mode'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Enter valid username and password',
        expected: 'Credentials should be accepted without validation errors',
        type: 'manual'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Click login button',
        expected: 'User should be redirected to dashboard with welcome message',
        type: 'automated',
        notes: 'This step can be automated using Selenium'
      }
    ],
    automation: {
      repository: 'https://github.com/company/test-automation',
      branch: 'main',
      commands: ['npm test', 'npm run test:login'],
      parameters: {
        'browser': 'chrome',
        'headless': 'true',
        'timeout': '30000'
      }
    },
    relatedRequirements: [
      {
        id: 'REQ-001',
        type: 'story',
        title: 'User Authentication System',
        status: 'done',
        assignee: 'john.doe',
        url: 'https://jira.company.com/REQ-001'
      },
      {
        id: 'EPIC-123',
        type: 'epic',
        title: 'Security Enhancement Phase 1',
        status: 'in-progress',
        assignee: 'jane.smith'
      }
    ],
    datasets: [
      {
        id: 'dataset-1',
        name: 'User Credentials',
        columns: [
          { name: 'username', type: 'string', required: true },
          { name: 'password', type: 'string', required: true },
          { name: 'expected_result', type: 'string', required: true }
        ],
        data: [
          { username: 'admin', password: 'admin123', expected_result: 'success' },
          { username: 'user1', password: 'password1', expected_result: 'success' },
          { username: 'invalid', password: 'wrong', expected_result: 'failure' }
        ]
      }
    ],
    testRuns: [
      {
        id: 'run-1',
        runDate: '2024-01-20T14:30:00Z',
        status: 'passed',
        duration: 45,
        environment: 'staging',
        executor: 'john.doe',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 15 },
          { stepId: 'step-2', status: 'passed', duration: 10 },
          { stepId: 'step-3', status: 'passed', duration: 20 }
        ]
      },
      {
        id: 'run-2',
        runDate: '2024-01-19T10:15:00Z',
        status: 'failed',
        duration: 30,
        environment: 'production',
        executor: 'jane.smith',
        results: [
          { stepId: 'step-1', status: 'passed', duration: 15 },
          { stepId: 'step-2', status: 'failed', duration: 15, error: 'Invalid credentials' }
        ]
      }
    ],
    knownIssues: [
      {
        id: 'issue-1',
        title: 'Login fails on Safari browser',
        description: 'The login functionality does not work properly on Safari due to cookie handling issues',
        severity: 'medium',
        status: 'investigating',
        reporter: 'test.user',
        assignee: 'dev.team',
        createdAt: '2024-01-15T09:00:00Z',
        bugUrl: 'https://jira.company.com/BUG-456'
      }
    ]
  }
};

export default function TestCasePage() {
  const params = useParams();
  const id = params.id as string;

  const [testCase, setTestCase] = useState<TestCase | null>(null);
  const [activeTab, setActiveTab] = useState('information');
  const [isEditing, setIsEditing] = useState(false);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    const loadTestCase = async () => {
      try {
        // 首先尝试从API加载真实数据
        const response = await fetch(`/api/test-case?id=${id}`);
        if (response.ok) {
          const apiTestCase = await response.json();
          // 转换API数据格式为组件期望的格式
          const formattedTestCase: TestCase = {
            id: apiTestCase.id,
            name: apiTestCase.name,
            description: apiTestCase.description || '',
            preconditions: apiTestCase.preconditions || '',
            priority: apiTestCase.priority || 'medium',
            status: apiTestCase.status || 'draft',
            weight: apiTestCase.weight || 'medium',
            format: apiTestCase.format || 'classic',
            nature: apiTestCase.nature || 'functional',
            type: apiTestCase.type || 'regression',
            tags: (() => {
              try {
                return apiTestCase.tags ? JSON.parse(apiTestCase.tags) : [];
              } catch (e) {
                console.warn('Failed to parse tags JSON:', apiTestCase.tags);
                return [];
              }
            })(),
            createdAt: new Date(apiTestCase.createdAt).toISOString(),
            updatedAt: new Date(apiTestCase.updatedAt).toISOString(),
            author: apiTestCase.createdBy || 'Unknown',
            modifier: apiTestCase.updatedBy || 'Unknown',
            executionTime: apiTestCase.executionTime || 0,
            lastRun: apiTestCase.lastRunAt ? new Date(apiTestCase.lastRunAt).toISOString() : undefined,
            steps: [], // 将从API加载
            automation: undefined, // 将从API加载
            relatedRequirements: [], // 将从API加载
            datasets: [], // 将从API加载
            testRuns: [], // 将从API加载
            knownIssues: [] // 将从API加载
          };
          setTestCase(formattedTestCase);
        } else {
          // 如果API失败，尝试使用mock数据
          const mockTestCase = mockTestCases[id];
          if (mockTestCase) {
            setTestCase(mockTestCase);
          } else {
            console.error('Test case not found');
          }
        }
      } catch (error) {
        console.error('Error loading test case:', error);
        // 尝试使用mock数据作为fallback
        const mockTestCase = mockTestCases[id];
        if (mockTestCase) {
          setTestCase(mockTestCase);
        }
      }
    };

    loadTestCase();
  }, [id]);

  const handleUpdate = async (updates: Partial<TestCase>) => {
    console.log('TestCase page received update:', updates);
    console.log('Update keys:', Object.keys(updates));
    console.log('Update values:', Object.values(updates));
    if (testCase) {
      const updatedTestCase = {
        ...testCase,
        ...updates,
        updatedAt: new Date().toISOString(),
        updatedBy: 'ai-assistant' // 标记为AI更新
      };
      console.log('Updated test case:', updatedTestCase);

      try {
        // 保存到数据库
        const response = await fetch(`/api/test-case?id=${testCase.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedTestCase),
        });

        if (!response.ok) {
          throw new Error('Failed to save test case');
        }

        // 更新本地状态
        setTestCase(updatedTestCase);

        // 显示成功提示
        toast({
          type: 'success',
          description: 'Test case updated successfully by AI',
        });

        console.log('Test case saved to database successfully');
      } catch (error) {
        console.error('Failed to save test case:', error);
        toast({
          type: 'error',
          description: 'Failed to save test case updates',
        });
      }
    }
  };

  const handleSave = () => {
    // 保存逻辑
    console.log('Saving test case:', testCase);
    setIsEditing(false);
  };

  const handleAIGenerate = () => {
    // AI生成逻辑
    console.log(`Generating AI content for ${activeTab}`);
  };

  const handleRunTest = () => {
    setIsRunning(true);
    // 模拟测试运行
    setTimeout(() => {
      setIsRunning(false);
      console.log('Test completed');
    }, 3000);
  };

  if (!testCase) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading test case...</p>
        </div>
      </div>
    );
  }

  const renderModule = () => {
    const moduleProps = {
      testCase,
      isEditing,
      onUpdate: handleUpdate,
      onAIGenerate: handleAIGenerate
    };

    switch (activeTab) {
      case 'information':
        return <InformationModule {...moduleProps} />;
      case 'steps':
        return <TestStepsModule {...moduleProps} />;
      case 'automation':
        return <AutomationModule {...moduleProps} />;
      case 'requirements':
        return <RequirementsModule {...moduleProps} />;
      case 'dataset':
        return <DatasetModule {...moduleProps} />;
      case 'testruns':
        return <TestRunsModule {...moduleProps} onRunTest={handleRunTest} />;
      case 'issues':
        return <KnownIssuesModule {...moduleProps} />;
      default:
        return <InformationModule {...moduleProps} />;
    }
  };

  return (
    <TestCaseLayout
      testCase={testCase}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      isEditing={isEditing}
      onEditToggle={() => setIsEditing(!isEditing)}
      onSave={handleSave}
      onAIGenerate={handleAIGenerate}
      onRunTest={handleRunTest}
      isRunning={isRunning}
      onTestCaseUpdate={handleUpdate}
    >
      {renderModule()}
    </TestCaseLayout>
  );
}