'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

// 导入组件
import { CodeEditor } from '@/components/chat/code-editor';
import { SpreadsheetEditor } from '@/components/chat/sheet-editor';
import { TestingEditor } from '@/components/chat/testing-editor';
import { MIDSCENE_REPORT } from '@/artifacts/types';

// 文档类型定义
interface Document {
  id: string;
  title: string;
  kind: string;
  content: string;
  userId: string;
  createdAt: string | Date;
  updatedAt?: string | Date;
}

// 空的建议数组，用于CodeEditor
const emptySuggestions: any[] = [];

export default function DocumentPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/document?id=${id}`);
        
        if (!response.ok) {
          throw new Error(`获取文档失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log("获取到的文档数据:", data);
        
        // 处理返回的数据是数组的情况
        if (Array.isArray(data) && data.length > 0) {
          setDocument(data[0]);
        } else {
          setDocument(data);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取文档失败');
        console.error('获取文档失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchDocument();
    }
  }, [id]);

  // 渲染文档内容
  const renderDocumentContent = () => {
    if (!document) return null;

    switch (document.kind) {
      case 'code':
        return (
          <div className="w-full h-[calc(100vh-120px)]">
            <CodeEditor 
              content={document.content} 
              onSaveContent={(content) => console.log('保存内容', content)} 
              status="idle"
              isCurrentVersion={true}
              currentVersionIndex={0}
              suggestions={emptySuggestions}
            />
          </div>
        );
      case 'sheet':
        return (
          <div className="w-full h-[calc(100vh-120px)]">
            <SpreadsheetEditor 
              content={document.content} 
              saveContent={(content) => console.log('保存内容', content)} 
              status="idle"
              isCurrentVersion={true}
              currentVersionIndex={0}
            />
          </div>
        );
      case MIDSCENE_REPORT:
        return (
          <div className="w-full h-[calc(100vh-120px)]">
            <TestingEditor 
              content={document.content}
              status="idle"
              isCurrentVersion={true}
              currentVersionIndex={0}
              saveContent={(content) => console.log('保存内容', content)}
              mode="edit"
              getDocumentContentById={() => document.content}
              isLoading={false}
            />
          </div>
        );
      case 'text':
        return (
          <div className="bg-white rounded-lg p-6 w-full h-[calc(100vh-120px)] overflow-auto">
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: document.content }} />
          </div>
        );
      case 'image':
        return (
          <div className="bg-white rounded-lg p-6 flex justify-center items-center w-full h-[calc(100vh-120px)]">
            <img 
              src={document.content.startsWith('http') ? document.content : `/api/images/${document.id}`} 
              alt={document.title}
              className="max-h-full max-w-full object-contain rounded-md" 
            />
          </div>
        );
      default:
        return (
          <div className="bg-white rounded-lg p-6 w-full h-[calc(100vh-120px)] flex items-center justify-center">
            <p className="text-gray-500">无法预览此类型的文档</p>
          </div>
        );
    }
  };

  return (
    <div className="container-fluid w-full h-screen flex flex-col">
      <div className="flex items-center p-6 border-b">
        <Link href="/documents">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回文档列表
          </Button>
        </Link>
        
        {isLoading ? (
          <Skeleton className="h-8 w-64" />
        ) : error ? (
          <span className="text-red-500">{error}</span>
        ) : document ? (
          <h1 className="text-2xl font-bold">{document.title}</h1>
        ) : (
          <span className="text-gray-500">文档不存在</span>
        )}
      </div>

      <div className="flex-grow overflow-hidden">
        {isLoading ? (
          <div className="bg-white p-6 h-full">
            <div className="animate-pulse space-y-4 h-full">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-[calc(100%-32px)] w-full" />
            </div>
          </div>
        ) : error ? (
          <div className="bg-white p-6 text-center h-full flex flex-col items-center justify-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>重试</Button>
          </div>
        ) : document ? (
          renderDocumentContent()
        ) : (
          <div className="bg-white p-6 text-center h-full flex items-center justify-center">
            <p className="text-gray-500">文档不存在</p>
          </div>
        )}
      </div>
    </div>
  );
} 