/**
 * 意图检测器
 * 用于分析用户消息，判断其意图类别，选择合适的模式
 */

import { PromptType } from './prompts';
import OpenAI from 'openai';

// 创建OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "sk-fe993c92dcc64df59dc08250494935a2",
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
});

// 上下文记忆管理器
export class ContextMemoryManager {
  private contextMap: Map<string, {
    intent: PromptType,
    artifacts: Record<string, any>,
    automationState: Record<string, any>
  }> = new Map();
  
  // 获取或创建上下文
  getOrCreateContext(sessionId: string) {
    if (!this.contextMap.has(sessionId)) {
      this.contextMap.set(sessionId, {
        intent: "mixed",
        artifacts: {},
        automationState: {}
      });
    }
    return this.contextMap.get(sessionId)!;
  }
  
  // 更新上下文
  updateContext(sessionId: string, updates: Partial<{
    intent: PromptType,
    artifacts: Record<string, any>,
    automationState: Record<string, any>
  }>) {
    const context = this.getOrCreateContext(sessionId);
    this.contextMap.set(sessionId, { ...context, ...updates });
    return this.contextMap.get(sessionId)!;
  }
  
  // 获取当前意图
  getCurrentIntent(sessionId: string): PromptType {
    return this.getOrCreateContext(sessionId).intent;
  }
}

// 创建全局上下文管理器实例
export const memoryManager = new ContextMemoryManager();

/**
 * 快速意图检测
 * 使用关键词匹配进行初步意图判断
 */
function quickIntentDetection(message: string): PromptType {
  // 转换为小写以进行不区分大小写的匹配
  const lowercaseMessage = message.toLowerCase();
  
  // 文章生成相关关键词
  const articleKeywords = [
    '写一篇', '写篇', '帮我写', '生成文章', '创建文章', 
    '写作', '文章', '写文档', '写一个', '写个'
  ];
  
  // 检查是否包含文章生成关键词
  const hasArticleKeywords = articleKeywords.some(keyword => 
    lowercaseMessage.includes(keyword)
  );
  
  // 文章生成请求的正则表达式
  const articleRegex = /写(一篇|一个|个|篇).*?(文章|博客|报告|总结|文档)/i;
  const isArticleRequest = articleRegex.test(message) || hasArticleKeywords;
  
  // 如果是文章生成请求，直接返回artifacts意图
  if (isArticleRequest) {
    console.log('检测到文章生成请求，使用artifacts意图');
    return 'artifacts';
  }
  
  // 自动化测试关键词
  const automationKeywords = [
    '自动化', '测试', '点击', '打开', '访问', '网页', '网站', 
    '登录', '搜索', '填写', '表单', '提交', '验证', '检查'
  ];
  
  // 检查是否包含自动化测试关键词
  const hasAutomationKeywords = automationKeywords.some(keyword => 
    lowercaseMessage.includes(keyword)
  );
  
  // Artifacts关键词
  const artifactsKeywords = [
    '创建', '文档', '代码', '表格', '图像', '编辑', '修改', 
    '更新', '生成', '画', '绘制', '写代码', '生成代码'
  ];
  
  // 检查是否包含Artifacts关键词
  const hasArtifactsKeywords = artifactsKeywords.some(keyword => 
    lowercaseMessage.includes(keyword)
  );
  
  // 根据关键词匹配结果判断意图
  if (hasAutomationKeywords && !hasArtifactsKeywords) {
    return 'automation';
  } else if (!hasAutomationKeywords && hasArtifactsKeywords) {
    return 'artifacts';
  } else if (hasAutomationKeywords && hasArtifactsKeywords) {
    return 'mixed';
  } else {
    return 'mixed'; // 默认使用混合模式
  }
}

/**
 * AI意图检测函数
 * 使用AI模型判断用户意图
 */
async function aiIntentDetection(message: string): Promise<PromptType> {
  try {
    // 使用模型进行意图分类
    const response = await openai.chat.completions.create({
      model: "qwen-max",
      messages: [
        {
          role: "system",
          content: `分析用户消息，判断其意图类别:
1. automation - 用户想进行网页自动化测试或操作
2. artifacts - 用户想创建或编辑内容(文章、代码、表格等)
3. mixed - 用户的请求涉及两种能力或不明确

只返回一个单词作为答案: automation, artifacts 或 mixed`
        },
        {
          role: "user",
          content: message
        }
      ],
      temperature: 0.1,
      max_tokens: 10
    });

    const intent = response.choices[0].message.content?.trim().toLowerCase();
    
    // 验证返回的意图是否有效
    if (intent === "automation" || intent === "artifacts" || intent === "mixed") {
      return intent as PromptType;
    }
    
    // 默认返回混合模式
    return "mixed";
  } catch (error) {
    console.error("AI意图检测失败:", error);
    // 如果AI检测失败，返回混合模式
    return "mixed";
  }
}

/**
 * 意图检测函数
 * 结合快速检测和AI检测，判断用户意图
 */
export async function detectIntent(
  message: string, 
  sessionId: string,
  forceAIDetection: boolean = false
): Promise<PromptType> {
  // 获取历史意图
  const currentContext = memoryManager.getOrCreateContext(sessionId);
  const historicalIntent = currentContext.intent;
  
  // 如果不强制使用AI检测，先尝试快速检测
  if (!forceAIDetection) {
    const quickIntent = quickIntentDetection(message);
    if (quickIntent) {
      console.log(`快速意图检测结果: ${quickIntent}`);
      return quickIntent;
    }
  }
  
  // 快速检测无法确定意图，使用AI检测
  try {
    const aiIntent = await aiIntentDetection(message);
    console.log(`AI意图检测结果: ${aiIntent}`);
    return aiIntent;
  } catch (error) {
    console.error("意图检测失败:", error);
    // 如果检测失败，返回历史意图或默认为混合模式
    return historicalIntent || "mixed";
  }
} 