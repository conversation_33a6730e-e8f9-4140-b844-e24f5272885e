'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { ChevronDownIcon, ChevronRightIcon, FolderIcon, FileTextIcon, SearchIcon, FilterIcon, FolderPlus, Plus, Menu, X, Loader2, WrapText, Type, ArrowLeft, Play, Edit3, Save, CheckCircle, XCircle, Clock, AlertCircle, ExternalLink, Info, Target, User, Weight, Tag, Activity, Trash2 } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// 树节点类型定义
interface TreeNode {
  id: string;
  name: string;
  children: TreeNode[];
  isFolder?: boolean;
}

// 测试用例数据类型
interface TestStep {
  id: string;
  step: number;
  action: string;
  expected: string;
  status: 'pending' | 'passed' | 'failed' | 'skipped';
}

interface TestCase {
  id: string;
  name: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  status: 'draft' | 'active' | 'deprecated';
  tags: string[];
  steps: TestStep[];
  createdAt: string;
  updatedAt: string;
  author: string;
  executionTime?: number;
  lastRun?: string;
}

// 生成唯一ID的函数
const generateId = () => `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 模拟测试用例数据
const mockTestCases: Record<string, TestCase> = {
  '1-1-1': {
    id: '1-1-1',
    name: 'Login with valid user',
    description: 'Test the login functionality with valid user credentials to ensure proper authentication flow',
    priority: 'high',
    status: 'active',
    tags: ['login', 'authentication', 'smoke'],
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    author: 'John Doe',
    executionTime: 45,
    lastRun: '2024-01-20 14:30:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to login page',
        expected: 'Login page should be displayed with username and password fields',
        status: 'passed'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Enter valid username and password',
        expected: 'Credentials should be accepted without validation errors',
        status: 'passed'
      },
      {
        id: 'step-3',
        step: 3,
        action: 'Click login button',
        expected: 'User should be redirected to dashboard with welcome message',
        status: 'passed'
      }
    ]
  },
  '1-1-2': {
    id: '1-1-2',
    name: 'Login with invalid user',
    description: 'Test the login functionality with invalid user credentials to verify error handling',
    priority: 'medium',
    status: 'active',
    tags: ['login', 'authentication', 'negative'],
    createdAt: '2024-01-15',
    updatedAt: '2024-01-18',
    author: 'Jane Smith',
    executionTime: 30,
    lastRun: '2024-01-18 10:15:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Navigate to login page',
        expected: 'Login page should be displayed',
        status: 'passed'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Enter invalid username and password',
        expected: 'Error message should be displayed indicating invalid credentials',
        status: 'failed'
      }
    ]
  },
  '1-2-1': {
    id: '1-2-1',
    name: 'Session start',
    description: 'Test the session initialization process when user starts a new session',
    priority: 'medium',
    status: 'active',
    tags: ['session', 'initialization'],
    createdAt: '2024-01-16',
    updatedAt: '2024-01-19',
    author: 'Mike Johnson',
    executionTime: 60,
    lastRun: '2024-01-19 16:45:00',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Initialize new session',
        expected: 'Session should be created with unique session ID',
        status: 'passed'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Verify session data',
        expected: 'Session data should be properly stored and accessible',
        status: 'pending'
      }
    ]
  },
  '3-1-1': {
    id: '3-1-1',
    name: 'Button color',
    description: 'Test the consistency of button colors across different UI components',
    priority: 'low',
    status: 'draft',
    tags: ['ui', 'consistency', 'visual'],
    createdAt: '2024-01-17',
    updatedAt: '2024-01-17',
    author: 'Sarah Wilson',
    steps: [
      {
        id: 'step-1',
        step: 1,
        action: 'Check primary button color',
        expected: 'Primary buttons should use consistent blue color (#1e88e5)',
        status: 'pending'
      },
      {
        id: 'step-2',
        step: 2,
        action: 'Check secondary button color',
        expected: 'Secondary buttons should use consistent gray color',
        status: 'pending'
      }
    ]
  }
};

// 加载状态组件
const LoadingSkeleton = () => (
  <div className="space-y-2 p-4">
    {[...Array(8)].map((_, i) => (
      <div key={i} className="flex items-center gap-2 animate-pulse">
        <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className={`h-4 bg-gray-200 dark:bg-gray-700 rounded ${i % 3 === 0 ? 'w-32' : i % 3 === 1 ? 'w-24' : 'w-28'}`}></div>
      </div>
    ))}
  </div>
);

// 状态颜色映射
const statusColors = {
  pending: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  passed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  skipped: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
};

const priorityColors = {
  high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
};

// 获取状态图标
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'passed':
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    case 'failed':
      return <XCircle className="w-4 h-4 text-red-600" />;
    case 'pending':
      return <Clock className="w-4 h-4 text-gray-600" />;
    default:
      return <AlertCircle className="w-4 h-4 text-yellow-600" />;
  }
};




// 键盘快捷键提示组件
const KeyboardShortcuts = () => (
  <div className="text-xs text-slate-500 dark:text-slate-400 p-3 border-t border-slate-200 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-800/50">
    <div className="space-y-1.5">
      <div className="flex items-center justify-between">
        <span>New folder</span>
        <kbd className="px-2 py-0.5 bg-slate-200 dark:bg-slate-700 rounded text-xs font-mono">Ctrl+N</kbd>
      </div>
      <div className="flex items-center justify-between">
        <span>Search</span>
        <kbd className="px-2 py-0.5 bg-slate-200 dark:bg-slate-700 rounded text-xs font-mono">Ctrl+F</kbd>
      </div>
      <div className="flex items-center justify-between">
        <span>Toggle sidebar</span>
        <kbd className="px-2 py-0.5 bg-slate-200 dark:bg-slate-700 rounded text-xs font-mono">Ctrl+B</kbd>
      </div>
      <div className="flex items-center justify-between">
        <span>Wrap text</span>
        <kbd className="px-2 py-0.5 bg-slate-200 dark:bg-slate-700 rounded text-xs font-mono">Ctrl+W</kbd>
      </div>
      <div className="flex items-center justify-between">
        <span>Deselect</span>
        <kbd className="px-2 py-0.5 bg-slate-200 dark:bg-slate-700 rounded text-xs font-mono">Esc</kbd>
      </div>
    </div>
  </div>
);

// 模拟树状数据
const initialTestCaseTree: TreeNode[] = [
  {
    id: '1',
    name: 'Automation Tests',
    isFolder: true,
    children: [
      {
        id: '1-1',
        name: 'Authentication Tests',
        isFolder: true,
        children: [
          { id: '1-1-1', name: 'Login with valid user (Midscene)', children: [], isFolder: false },
          { id: '1-1-2', name: 'Login with invalid credentials (Playwright)', children: [], isFolder: false },
        ],
      },
      {
        id: '1-2',
        name: 'Profile Management',
        isFolder: true,
        children: [
          { id: '1-2-1', name: 'User profile update (Cypress)', children: [], isFolder: false },
        ],
      },
    ],
  },
  {
    id: '2',
    name: 'API Tests',
    isFolder: true,
    children: [
      {
        id: '2-1',
        name: 'Authentication API',
        isFolder: true,
        children: [
          { id: '2-1-1', name: 'API Authentication Test (Selenium)', children: [], isFolder: false },
        ],
      },
    ],
  },
  {
    id: '3',
    name: 'Performance Tests',
    isFolder: true,
    children: [
      {
        id: '3-1',
        name: 'Load Testing',
        isFolder: true,
        children: [
          { id: '3-1-1', name: 'Homepage load test', children: [], isFolder: false },
        ],
      },
    ],
  },
];

// 加载骨架屏组件
function TreeSkeleton() {
  return (
    <div className="space-y-2 animate-pulse">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="flex items-center gap-2 py-2">
          <div className="w-4 h-4 bg-zinc-200 dark:bg-zinc-700 rounded"></div>
          <div className="w-4 h-4 bg-zinc-200 dark:bg-zinc-700 rounded"></div>
          <div className={`h-4 bg-zinc-200 dark:bg-zinc-700 rounded ${i % 3 === 0 ? 'w-32' : i % 3 === 1 ? 'w-24' : 'w-28'}`}></div>
        </div>
      ))}
    </div>
  );
}

// 简单的树节点组件（用于服务器端渲染）
function SimpleTreeNode({
  node,
  level = 0,
  onSelect,
  selectedId,
  wrapText = false
}: {
  node: TreeNode;
  level?: number;
  onSelect: (node: TreeNode) => void;
  selectedId?: string;
  wrapText?: boolean;
}) {
  const [open, setOpen] = useState(level < 1);
  const hasChildren = node.children && node.children.length > 0;
  const isFolder = node.isFolder !== false;

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发背景点击事件
    if (hasChildren) setOpen((v) => !v);
    onSelect(node);
  };

  return (
    <div className="ml-1">
      <div
        className={`group flex items-center gap-3 py-2.5 px-3 mx-1 cursor-pointer rounded-lg transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 hover:shadow-sm hover:scale-[1.02] ${
          selectedId === node.id
            ? 'bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-md ring-1 ring-blue-200 dark:ring-blue-800'
            : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 12}px` }}
        onClick={handleNodeClick}
      >
        {/* Expand/Collapse Icon */}
        <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center">
          {hasChildren ? (
            open ? (
              <ChevronDownIcon className="w-4 h-4 text-slate-500 dark:text-slate-400 transition-transform duration-200" />
            ) : (
              <ChevronRightIcon className="w-4 h-4 text-slate-500 dark:text-slate-400 transition-transform duration-200" />
            )
          ) : (
            <div className="w-2 h-2 rounded-full bg-slate-300 dark:bg-slate-600"></div>
          )}
        </div>

        {/* Icon */}
        <div className="flex-shrink-0">
          {isFolder ? (
            <div className={`w-5 h-5 rounded-md flex items-center justify-center transition-all duration-200 ${
              selectedId === node.id
                ? 'bg-blue-500 text-white shadow-sm'
                : 'bg-blue-100 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/60'
            }`}>
              <FolderIcon className="w-3 h-3" />
            </div>
          ) : (
            <div className={`w-5 h-5 rounded-md flex items-center justify-center transition-all duration-200 ${
              selectedId === node.id
                ? 'bg-green-500 text-white shadow-sm'
                : 'bg-green-100 dark:bg-green-900/40 text-green-600 dark:text-green-400 group-hover:bg-green-200 dark:group-hover:bg-green-800/60'
            }`}>
              <FileTextIcon className="w-3 h-3" />
            </div>
          )}
        </div>

        {/* Text with Tooltip and Wrap Option */}
        <span
          className={`text-sm font-medium transition-colors duration-200 ${
            wrapText ? 'break-words leading-relaxed' : 'truncate'
          } ${
            selectedId === node.id
              ? 'text-blue-900 dark:text-blue-100'
              : 'text-slate-700 dark:text-slate-300 group-hover:text-slate-900 dark:group-hover:text-slate-100'
          }`}
          title={wrapText ? undefined : node.name} // 只在截断时显示工具提示
        >
          {node.name}
        </span>

        {/* Badge for children count */}
        {hasChildren && node.children.length > 0 && (
          <div className={`ml-auto flex-shrink-0 px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200 ${
            selectedId === node.id
              ? 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200'
              : 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400 group-hover:bg-slate-300 dark:group-hover:bg-slate-600'
          }`}>
            {node.children.length}
          </div>
        )}
      </div>

      {hasChildren && open && (
        <div className="ml-2 relative">
          <div className="absolute left-4 top-0 bottom-0 w-px bg-gradient-to-b from-slate-200 to-transparent dark:from-slate-700"></div>
          {node.children.map((child, index) => (
            <SimpleTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onSelect={onSelect}
              selectedId={selectedId}
              wrapText={wrapText}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// 可拖拽的树节点组件
function SortableTreeNode({
  node,
  level = 0,
  onSelect,
  selectedId,
  isDragging = false,
  wrapText = false
}: {
  node: TreeNode;
  level?: number;
  onSelect: (node: TreeNode) => void;
  selectedId?: string;
  isDragging?: boolean;
  wrapText?: boolean;
}) {
  const [open, setOpen] = useState(level < 1); // 默认展开前两级
  const hasChildren = node.children && node.children.length > 0;
  const isFolder = node.isFolder !== false; // 默认为文件夹，除非明确设置为false

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: node.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发背景点击事件
    if (hasChildren) setOpen((v) => !v);
    onSelect(node);
  };

  return (
    <div ref={setNodeRef} style={style} className="ml-1">
      <div
        className={`group flex items-center gap-3 py-2.5 px-3 mx-1 rounded-lg transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 hover:shadow-sm hover:scale-[1.02] ${
          selectedId === node.id
            ? 'bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-md ring-1 ring-blue-200 dark:ring-blue-800'
            : ''
        } ${isDragging ? 'opacity-60 scale-105 shadow-xl ring-2 ring-blue-400 dark:ring-blue-500 bg-white dark:bg-zinc-800' : ''}`}
        style={{ paddingLeft: `${level * 16 + 12}px` }}
      >
        {/* Expand/Collapse Button */}
        <div
          className="flex-shrink-0 w-4 h-4 flex items-center justify-center cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-700 rounded transition-colors duration-150"
          onClick={handleNodeClick}
        >
          {hasChildren ? (
            open ? (
              <ChevronDownIcon className="w-4 h-4 text-slate-500 dark:text-slate-400 transition-transform duration-200" />
            ) : (
              <ChevronRightIcon className="w-4 h-4 text-slate-500 dark:text-slate-400 transition-transform duration-200" />
            )
          ) : (
            <div className="w-2 h-2 rounded-full bg-slate-300 dark:bg-slate-600"></div>
          )}
        </div>

        {/* Icon */}
        <div
          className="flex-shrink-0 cursor-pointer"
          onClick={handleNodeClick}
        >
          {isFolder ? (
            <div className={`w-5 h-5 rounded-md flex items-center justify-center transition-all duration-200 ${
              selectedId === node.id
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'bg-primary/10 dark:bg-primary/20 text-primary group-hover:bg-primary/20 dark:group-hover:bg-primary/30'
            }`}>
              <FolderIcon className="w-3 h-3" />
            </div>
          ) : (
            <div className={`w-5 h-5 rounded-md flex items-center justify-center transition-all duration-200 ${
              selectedId === node.id
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'bg-primary/10 dark:bg-primary/20 text-primary group-hover:bg-primary/20 dark:group-hover:bg-primary/30'
            }`}>
              <FileTextIcon className="w-3 h-3" />
            </div>
          )}
        </div>

        {/* Text with Tooltip and Wrap Option */}
        <span
          className={`text-sm font-medium flex-1 cursor-pointer transition-colors duration-200 ${
            wrapText ? 'break-words leading-relaxed' : 'truncate'
          } ${
            selectedId === node.id
              ? 'text-blue-900 dark:text-blue-100'
              : 'text-slate-700 dark:text-slate-300 group-hover:text-slate-900 dark:group-hover:text-slate-100'
          }`}
          onClick={handleNodeClick}
          title={wrapText ? undefined : node.name} // 只在截断时显示工具提示
        >
          {node.name}
        </span>

        {/* Children Count Badge */}
        {hasChildren && node.children.length > 0 && (
          <div className={`flex-shrink-0 px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200 ${
            selectedId === node.id
              ? 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200'
              : 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400 group-hover:bg-slate-300 dark:group-hover:bg-slate-600'
          }`}>
            {node.children.length}
          </div>
        )}

        {/* Enhanced Drag Handle */}
        <div
          className={`flex-shrink-0 cursor-grab active:cursor-grabbing p-2 -m-1 rounded-md transition-all duration-200 ${
            isDragging
              ? 'opacity-100 bg-blue-100 dark:bg-blue-900/40'
              : 'opacity-0 group-hover:opacity-100 hover:bg-slate-100 dark:hover:bg-slate-800'
          }`}
          {...attributes}
          {...listeners}
          title="Drag to move"
        >
          <div className="w-3 h-4 flex flex-col justify-center gap-0.5">
            <div className={`w-full h-0.5 rounded transition-colors duration-200 ${
              isDragging ? 'bg-blue-500' : 'bg-slate-400 dark:bg-slate-500'
            }`}></div>
            <div className={`w-full h-0.5 rounded transition-colors duration-200 ${
              isDragging ? 'bg-blue-500' : 'bg-slate-400 dark:bg-slate-500'
            }`}></div>
            <div className={`w-full h-0.5 rounded transition-colors duration-200 ${
              isDragging ? 'bg-blue-500' : 'bg-slate-400 dark:bg-slate-500'
            }`}></div>
          </div>
        </div>
      </div>

      {hasChildren && open && (
        <div className="ml-2 relative">
          <div className="absolute left-4 top-0 bottom-0 w-px bg-gradient-to-b from-slate-200 to-transparent dark:from-slate-700"></div>
          <SortableContext items={node.children.map(child => child.id)} strategy={verticalListSortingStrategy}>
            {node.children.map((child) => (
              <SortableTreeNode
                key={child.id}
                node={child}
                level={level + 1}
                onSelect={onSelect}
                selectedId={selectedId}
                isDragging={isDragging}
                wrapText={wrapText}
              />
            ))}
          </SortableContext>
        </div>
      )}
    </div>
  );
}

export default function TestCasePage() {
  const [selected, setSelected] = useState<TreeNode | null>(null);
  const [search, setSearch] = useState('');
  const [newCase, setNewCase] = useState('');
  const [testCaseTree, setTestCaseTree] = useState<TreeNode[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [treeKey, setTreeKey] = useState(0); // 用于强制重新渲染
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [wrapText, setWrapText] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [createType, setCreateType] = useState<'folder' | 'testcase'>('testcase');
  const [selectedTestCaseDetails, setSelectedTestCaseDetails] = useState<any>(null);
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  const searchInputRef = React.useRef<HTMLInputElement>(null);

  // 加载测试用例树数据
  const loadTestCaseTree = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/test-case-tree');
      if (response.ok) {
        const tree = await response.json();
        setTestCaseTree([...tree]); // 使用展开运算符确保新数组
        setTreeKey(prev => prev + 1); // 强制重新渲染
      } else {
        console.error('Failed to fetch tree:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Failed to load test case tree:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 加载测试用例详情
  const loadTestCaseDetails = useCallback(async (testCaseId: string) => {
    try {
      const response = await fetch(`/api/test-case?id=${testCaseId}`);
      if (response.ok) {
        const testCase = await response.json();
        setSelectedTestCaseDetails(testCase);
      } else {
        console.error('Failed to load test case details');
        setSelectedTestCaseDetails(null);
      }
    } catch (error) {
      console.error('Error loading test case details:', error);
      setSelectedTestCaseDetails(null);
    }
  }, []);

  // 确保只在客户端渲染拖拽功能
  useEffect(() => {
    setIsClient(true);
    loadTestCaseTree();

    // 监听AI助手切换事件
    const handleToggleAI = () => {
      setShowAIAssistant(prev => !prev);
    };

    window.addEventListener('toggle-ai-assistant', handleToggleAI);

    return () => {
      window.removeEventListener('toggle-ai-assistant', handleToggleAI);
    };
  }, []);



  // 键盘导航支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'n': // Ctrl+N 新建文件夹
            e.preventDefault();
            handleAddFolder();
            break;
          case 'f': // Ctrl+F 聚焦搜索
            e.preventDefault();
            searchInputRef.current?.focus();
            break;
          case 'b': // Ctrl+B 切换侧边栏
            e.preventDefault();
            setSidebarCollapsed(prev => !prev);
            break;
          case 'w': // Ctrl+W 切换文本换行
            e.preventDefault();
            setWrapText(prev => !prev);
            break;
        }
      }
      // ESC 取消选中
      if (e.key === 'Escape') {
        setSelected(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 查找节点的函数
  const findNode = (nodes: TreeNode[], id: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNode(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 查找节点的父节点
  const findParentNode = (nodes: TreeNode[], targetId: string, parent: TreeNode | null = null): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === targetId) return parent;
      if (node.children) {
        const found = findParentNode(node.children, targetId, node);
        if (found !== null) return found;
      }
    }
    return null;
  };

  // 移除节点的函数
  const removeNode = (nodes: TreeNode[], id: string): TreeNode[] => {
    return nodes.filter(node => {
      if (node.id === id) return false;
      if (node.children) {
        node.children = removeNode(node.children, id);
      }
      return true;
    });
  };

  // 添加节点到指定父节点的函数
  const addNodeToParent = (nodes: TreeNode[], parentId: string, newNode: TreeNode): TreeNode[] => {
    return nodes.map(node => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...node.children, newNode]
        };
      }
      if (node.children) {
        return {
          ...node,
          children: addNodeToParent(node.children, parentId, newNode)
        };
      }
      return node;
    });
  };

  // 创建文件夹
  const handleCreateFolder = async (name: string, description?: string) => {
    try {
      setIsCreating(true);
      const parentId = selected?.isFolder ? selected.id : null;

      const response = await fetch('/api/folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          parentId
        }),
      });

      if (response.ok) {
        const newFolder = await response.json();
        await loadTestCaseTree(); // 重新加载树结构
        setShowCreateDialog(false);
        // 自动选中新创建的文件夹
        setSelected({
          id: newFolder.id,
          name: newFolder.name,
          children: [],
          isFolder: true
        });
        setActiveId(newFolder.id);
        // 显示成功提示
        alert(`文件夹 "${newFolder.name}" 创建成功！`);
      } else {
        const errorData = await response.json();
        console.error('Failed to create folder:', errorData);
        alert('创建文件夹失败，请重试');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // 创建测试用例
  const handleCreateTestCase = async (name: string, description?: string) => {
    try {
      setIsCreating(true);
      const folderId = selected?.isFolder ? selected.id : null;

      const response = await fetch('/api/test-case', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description: description || `Test case for ${name}`,
          folderId,
          priority: 'medium',
          status: 'draft'
        }),
      });

      if (response.ok) {
        const newTestCase = await response.json();
        await loadTestCaseTree(); // 重新加载树结构
        setShowCreateDialog(false);
        // 自动选中新创建的测试用例
        setSelected({
          id: newTestCase.id,
          name: newTestCase.name,
          children: [],
          isFolder: false
        });
        setActiveId(newTestCase.id);
        // 显示成功提示
        alert(`测试用例 "${newTestCase.name}" 创建成功！`);
      } else {
        const errorData = await response.json();
        console.error('Failed to create test case:', errorData);
        alert('创建测试用例失败，请重试');
      }
    } catch (error) {
      console.error('Error creating test case:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // 添加新文件夹的函数（为当前选中的folder添加子folder）
  const handleAddFolder = () => {
    setCreateType('folder');
    setShowCreateDialog(true);
  };

  // 添加新测试用例的函数
  const handleAddTestCase = () => {
    setCreateType('testcase');
    setShowCreateDialog(true);
  };

  // 删除测试用例的函数
  const handleDeleteTestCase = async (testCaseId: string) => {
    // 找到要删除的测试用例名称
    const testCaseName = selected?.name || '测试用例';

    const confirmed = window.confirm(
      `确定要删除测试用例 "${testCaseName}" 吗？\n\n此操作将永久删除该测试用例及其所有相关数据，包括：\n• 测试步骤\n• 执行历史\n• 版本记录\n\n此操作不可撤销！`
    );

    if (!confirmed) {
      return;
    }

    try {
      const response = await fetch(`/api/test-case?id=${testCaseId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '删除测试用例失败');
      }

      // 重新加载测试用例树
      await loadTestCaseTree();

      // 如果删除的是当前选中的测试用例，清除选中状态
      if (selected?.id === testCaseId) {
        setSelected(null);
        setSelectedTestCaseDetails(null);
      }

      // 显示成功消息
      console.log(`测试用例 "${testCaseName}" 删除成功`);
    } catch (error) {
      console.error('删除测试用例失败:', error);
      window.alert(`删除失败：${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除文件夹的函数
  const handleDeleteFolder = async (folderId: string) => {
    // 找到要删除的文件夹名称
    const folderName = selected?.name || '文件夹';

    // 检查文件夹是否包含子项目
    const folderNode = findNode(testCaseTree, folderId);
    const hasChildren = folderNode && folderNode.children && folderNode.children.length > 0;

    let confirmMessage = `确定要删除文件夹 "${folderName}" 吗？\n\n`;

    if (hasChildren) {
      confirmMessage += `⚠️ 警告：此文件夹包含 ${folderNode.children.length} 个子项目！\n\n此操作将永久删除：\n• 该文件夹\n• 文件夹内的所有子文件夹\n• 文件夹内的所有测试用例\n• 所有相关的测试步骤、执行历史和版本记录\n\n`;
    } else {
      confirmMessage += `此操作将永久删除该文件夹。\n\n`;
    }

    confirmMessage += `此操作不可撤销！`;

    const confirmed = window.confirm(confirmMessage);

    if (!confirmed) {
      return;
    }

    try {
      // 先尝试普通删除
      let response = await fetch(`/api/folder?id=${folderId}`, {
        method: 'DELETE',
      });

      // 如果文件夹不为空，询问是否强制删除
      if (!response.ok) {
        const errorData = await response.json();

        if (response.status === 400 && errorData.hasSubFolders !== undefined) {
          // 文件夹包含内容，询问是否强制删除
          const forceConfirm = window.confirm(
            `文件夹不为空！\n\n包含内容：\n• ${errorData.subFoldersCount} 个子文件夹\n• ${errorData.testCasesCount} 个测试用例\n\n是否强制删除文件夹及其所有内容？\n\n⚠️ 此操作将永久删除所有内容，不可撤销！`
          );

          if (forceConfirm) {
            // 强制删除
            response = await fetch(`/api/folder?id=${folderId}&force=true`, {
              method: 'DELETE',
            });
          } else {
            return; // 用户取消强制删除
          }
        }

        if (!response.ok) {
          const newErrorData = await response.json();
          throw new Error(newErrorData.message || newErrorData.error || '删除文件夹失败');
        }
      }

      // 重新加载测试用例树
      await loadTestCaseTree();

      // 如果删除的是当前选中的文件夹，清除选中状态
      if (selected?.id === folderId) {
        setSelected(null);
        setSelectedTestCaseDetails(null);
      }

      // 显示成功消息
      console.log(`文件夹 "${folderName}" 删除成功`);
    } catch (error) {
      console.error('删除文件夹失败:', error);
      window.alert(`删除失败：${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // 拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over || active.id === over.id) return;

    const activeNode = findNode(testCaseTree, active.id as string);
    const overNode = findNode(testCaseTree, over.id as string);

    if (!activeNode || !overNode) return;

    // 防止将节点拖拽到自己的子节点中
    const isDescendant = (parent: TreeNode, childId: string): boolean => {
      if (parent.id === childId) return true;
      return parent.children.some(child => isDescendant(child, childId));
    };

    if (isDescendant(activeNode, overNode.id)) return;

    // 移除原节点
    let newTree = removeNode(testCaseTree, activeNode.id);

    // 如果目标是文件夹，添加到其子节点中；否则添加到同级
    if (overNode.isFolder !== false) {
      newTree = addNodeToParent(newTree, overNode.id, activeNode);
    } else {
      // 找到目标节点的父节点，添加到同级
      const overParent = findParentNode(testCaseTree, overNode.id);
      if (overParent) {
        newTree = addNodeToParent(newTree, overParent.id, activeNode);
      } else {
        // 如果没有父节点，添加到根级别
        newTree = [...newTree, activeNode];
      }
    }

    setTestCaseTree(newTree);
  };

  // 处理节点选择
  const handleNodeSelect = (node: TreeNode) => {
    setSelected(node);
    // 如果选择的是测试用例，加载详情
    if (!node.isFolder) {
      loadTestCaseDetails(node.id);
    } else {
      setSelectedTestCaseDetails(null);
    }
  };

  // 点击空白处取消选中
  const handleBackgroundClick = (e: React.MouseEvent) => {
    // 只有点击的是背景元素本身时才取消选中
    if (e.target === e.currentTarget) {
      setSelected(null);
    }
  };

  // 渲染树节点的函数
  const renderTreeNodes = () => {
    if (!isClient) {
      // 服务器端渲染：使用简单的树节点
      return (
        <div key={`tree-${treeKey}`}>
          {testCaseTree.map((node) => (
            <SimpleTreeNode
              key={`${node.id}-${treeKey}`}
              node={node}
              onSelect={handleNodeSelect}
              selectedId={selected?.id}
              wrapText={wrapText}
            />
          ))}
        </div>
      );
    }

    // 客户端渲染：使用可拖拽的树节点
    return (
      <SortableContext items={testCaseTree.map(node => node.id)} strategy={verticalListSortingStrategy}>
        <div key={`sortable-tree-${treeKey}`}>
          {testCaseTree.map((node) => (
            <SortableTreeNode
              key={`${node.id}-${treeKey}`}
              node={node}
              onSelect={handleNodeSelect}
              selectedId={selected?.id}
              isDragging={activeId === node.id}
              wrapText={wrapText}
            />
          ))}
        </div>
      </SortableContext>
    );
  };

  const content = (
    <div className="flex flex-col h-full min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-zinc-900 dark:to-zinc-800">

      {/* Main Content Area */}
      <div className="flex flex-1 min-h-0 relative">
        {/* Left: Enhanced Tree Panel */}
        {!sidebarCollapsed && (
          <div
            className="w-96 border-r border-slate-200 dark:border-zinc-700 bg-white/60 dark:bg-zinc-900/60 backdrop-blur-sm transition-all duration-300 ease-in-out overflow-hidden"
            onClick={handleBackgroundClick}
          >
            {/* Sidebar Header with Search and Tools */}
            <div className="p-4 border-b border-slate-200 dark:border-zinc-700 space-y-3">
              {/* Header Title and Tools */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FolderIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <span className="font-medium text-slate-700 dark:text-slate-300">Test Cases</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-8 h-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-800"
                    onClick={handleAddFolder}
                    title="Add new folder"
                  >
                    <FolderPlus className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-8 h-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-800"
                    onClick={handleAddTestCase}
                    title="Add new test case"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`w-8 h-8 p-0 rounded-md transition-colors ${
                      wrapText
                        ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400'
                        : 'hover:bg-slate-100 dark:hover:bg-slate-800'
                    }`}
                    onClick={() => setWrapText(!wrapText)}
                    title={wrapText ? "截断长文件名" : "换行显示长文件名"}
                  >
                    {wrapText ? <Type className="w-4 h-4" /> : <WrapText className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              {/* Search Bar */}
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-400 dark:text-slate-500" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search test cases..."
                  value={search}
                  onChange={e => setSearch(e.target.value)}
                  className="pl-9 pr-4 h-9 text-sm border-slate-200 dark:border-slate-700 bg-white/90 dark:bg-zinc-800/90 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500"
                />
                {search && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 w-6 h-6 p-0 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                    onClick={() => setSearch('')}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>

            {/* Tree Content */}
            <div className="p-4 overflow-y-auto h-full">
              {isLoading ? <TreeSkeleton /> : renderTreeNodes()}
              <KeyboardShortcuts />
            </div>
          </div>
        )}

        {/* Right: Enhanced Main View */}
        <div className="flex-1 overflow-y-auto relative">
          {/* Collapse Button - Top Left Corner */}
          <div className="absolute top-4 left-4 z-10">
            <Button
              variant="ghost"
              size="sm"
              className="w-10 h-10 p-0 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              title={sidebarCollapsed ? "展开侧边栏" : "折叠侧边栏"}
            >
              <Menu className="w-5 h-5" />
            </Button>
          </div>

          {selected ? (
            /* Selected Item Details */
            <div className="p-8 pt-20">
              <Card className="max-w-4xl mx-auto shadow-lg border-slate-200 dark:border-zinc-700 bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm">
                <div className="p-8">
                  <div className="flex items-start gap-4 mb-6">
                    {selected.isFolder ? (
                      <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <FolderIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                      </div>
                    ) : (
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <FileTextIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
                      </div>
                    )}
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-2">{selected.name}</h2>
                      <p className="text-slate-600 dark:text-slate-400">
                        {selected.isFolder ? 'Folder' : 'Test Case'} • Created today • Last modified 2 hours ago
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons for Folder */}
                  {selected.isFolder ? (
                    <div className="flex flex-wrap gap-3 mb-6">
                      <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                        <span className="text-lg mr-2">🤖</span>
                        Generate by AI
                      </Button>
                      <Button variant="outline" className="border-primary/20 dark:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 text-primary">
                        <span className="text-lg mr-2">📄</span>
                        Import Test Case
                      </Button>
                      <Button variant="outline" className="border-primary/20 dark:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 text-primary">
                        <FolderPlus className="w-4 h-4 mr-2" />
                        Add Sub Folder
                      </Button>
                      <Button
                        variant="outline"
                        className="border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-950 hover:border-red-300 dark:hover:border-red-700 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                        onClick={() => handleDeleteFolder(selected.id)}
                        title="删除文件夹"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Folder
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-3 mb-6">
                      <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Test Step
                      </Button>
                      <Button variant="outline">Edit</Button>
                      <Button variant="outline">Duplicate</Button>
                      <Button
                        variant="outline"
                        className="border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-950 hover:border-red-300 dark:hover:border-red-700 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                        onClick={() => handleDeleteTestCase(selected.id)}
                        title="删除测试用例"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </Button>
                    </div>
                  )}

                  {/* Content Area */}
                  <div className="bg-slate-50 dark:bg-zinc-800 rounded-lg p-6">
                    {selected.isFolder ? (
                      <p className="text-slate-600 dark:text-slate-400">
                        This folder contains {selected.children.length} items. You can organize your test cases by dragging them into this folder.
                      </p>
                    ) : (
                      /* Test Case Details */
                      <div className="space-y-6">
                        {selectedTestCaseDetails ? (
                          <>
                            {/* Description */}
                            <div>
                              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                                <Info className="w-5 h-5 text-primary" />
                                Description
                              </h3>
                              <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                                {selectedTestCaseDetails.description || 'No description available'}
                              </p>
                            </div>

                            {/* Quick Stats */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-white/80 dark:bg-zinc-700/80 rounded-lg p-4 border border-slate-200 dark:border-zinc-600">
                                <div className="flex items-center gap-2 mb-2">
                                  <Target className="w-4 h-4 text-blue-600" />
                                  <span className="font-medium text-slate-800 dark:text-slate-200">Test Steps</span>
                                </div>
                                <p className="text-2xl font-bold text-blue-600">{selectedTestCaseDetails?.steps?.length || 0}</p>
                              </div>

                              <div className="bg-white/80 dark:bg-zinc-700/80 rounded-lg p-4 border border-slate-200 dark:border-zinc-600">
                                <div className="flex items-center gap-2 mb-2">
                                  <Clock className="w-4 h-4 text-green-600" />
                                  <span className="font-medium text-slate-800 dark:text-slate-200">Execution Time</span>
                                </div>
                                <p className="text-2xl font-bold text-green-600">{selectedTestCaseDetails?.executionTime || 0}s</p>
                              </div>

                              <div className="bg-white/80 dark:bg-zinc-700/80 rounded-lg p-4 border border-slate-200 dark:border-zinc-600">
                                <div className="flex items-center gap-2 mb-2">
                                  <User className="w-4 h-4 text-purple-600" />
                                  <span className="font-medium text-slate-800 dark:text-slate-200">Author</span>
                                </div>
                                <p className="text-lg font-medium text-purple-600">{selectedTestCaseDetails?.createdBy || 'Unknown'}</p>
                              </div>
                            </div>

                            {/* Call to Action */}
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700 text-center">
                              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">
                                Want to see more details?
                              </h3>
                              <p className="text-slate-600 dark:text-slate-400 mb-4">
                                View automation config, datasets, test runs, and known issues in the full details page
                              </p>
                              <Button
                                onClick={() => window.open(`/test-case/${selected.id}`, '_blank')}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                <ExternalLink className="w-4 h-4 mr-2" />
                                Open Full Test Case Details
                              </Button>
                            </div>
                          </>
                        ) : !selected.isFolder ? (
                          <>
                            {/* Basic Test Case Info */}
                            <div>
                              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                                <Info className="w-5 h-5 text-primary" />
                                Test Case Information
                              </h3>
                              <p className="text-slate-600 dark:text-slate-400 leading-relaxed mb-4">
                                Loading test case details...
                              </p>
                            </div>

                            {/* Call to Action */}
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700 text-center">
                              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">
                                View Full Details
                              </h3>
                              <p className="text-slate-600 dark:text-slate-400 mb-4">
                                Open the detailed view to see automation config, datasets, test runs, and known issues
                              </p>
                              <Button
                                onClick={() => window.open(`/test-case/${selected.id}`, '_blank')}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                <ExternalLink className="w-4 h-4 mr-2" />
                                Open Full Test Case Details
                              </Button>
                            </div>
                          </>
                        ) : (
                          <p className="text-slate-600 dark:text-slate-400">
                            This folder contains {selected.children.length} items. You can organize your test cases by dragging them into this folder.
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          ) : (
            /* Empty State with Enhanced Add Test Cases */
            <div className="flex flex-col items-center justify-center h-full p-8 pt-20">
              <div className="max-w-2xl mx-auto text-center">
                {/* Illustration */}
                <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                    <FolderIcon className="w-8 h-8 text-white" />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-3">Create Your First Test Case</h3>
                <p className="text-slate-600 dark:text-slate-400 mb-8 text-lg">
                  Start building your test suite by adding test cases and organizing them into folders
                </p>

                {/* Quick Add Form */}
                <div className="bg-white dark:bg-zinc-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-zinc-700 mb-8">
                  <div className="flex gap-3 mb-4">
                    <Input
                      placeholder="Enter test case name..."
                      value={newCase}
                      onChange={e => setNewCase(e.target.value)}
                      className="flex-1 h-12 text-base border-slate-200 dark:border-slate-700 rounded-lg"
                    />
                    <Button
                      className="h-12 px-8 text-base bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                      onClick={handleAddTestCase}
                    >
                      Add Test Case
                    </Button>
                  </div>
                </div>

                {/* Alternative Options */}
                <div className="space-y-4">
                  <p className="text-slate-500 dark:text-slate-400">Or choose from these options:</p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      variant="outline"
                      className="flex items-center gap-3 px-6 h-12 text-base rounded-lg border-slate-200 dark:border-slate-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200"
                    >
                      <span className="text-xl">🤖</span>
                      Generate with AI
                    </Button>
                    <Button
                      variant="outline"
                      className="flex items-center gap-3 px-6 h-12 text-base rounded-lg border-slate-200 dark:border-slate-700 hover:bg-green-50 dark:hover:bg-green-900/20 hover:border-green-300 dark:hover:border-green-600 transition-all duration-200"
                    >
                      <span className="text-xl">📄</span>
                      Import from CSV
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* AI Assistant Panel */}
        {showAIAssistant && (
          <div className="fixed top-0 right-0 h-full w-96 bg-white/95 dark:bg-zinc-900/95 backdrop-blur-sm border-l border-slate-200 dark:border-zinc-700 shadow-2xl z-50 flex flex-col">
            {/* AI Assistant Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-zinc-700">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <h3 className="font-semibold text-slate-800 dark:text-slate-200">Test Case Assistant</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAIAssistant(false)}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* AI Assistant Content */}
            <div className="flex-1 flex flex-col p-4">
              <div className="flex-1 bg-slate-50 dark:bg-zinc-800 rounded-lg p-4 mb-4 overflow-y-auto">
                <div className="text-center text-slate-600 dark:text-slate-400 py-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl font-bold">AI</span>
                  </div>
                  <h4 className="font-medium mb-2">AI Test Case Assistant</h4>
                  <p className="text-sm">
                    I can help you generate test steps, optimize descriptions, create automation configs, and provide improvement suggestions.
                  </p>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2 mb-4">
                <Button
                  variant="outline"
                  className="w-full justify-start text-left"
                  onClick={() => {
                    // TODO: 实现生成测试步骤功能
                    alert('Generate test steps feature coming soon!');
                  }}
                >
                  <span className="text-lg mr-2">📝</span>
                  Generate Test Steps
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left"
                  onClick={() => {
                    // TODO: 实现优化描述功能
                    alert('Optimize description feature coming soon!');
                  }}
                >
                  <span className="text-lg mr-2">✨</span>
                  Optimize Description
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left"
                  onClick={() => {
                    // TODO: 实现生成自动化配置功能
                    alert('Generate automation config feature coming soon!');
                  }}
                >
                  <span className="text-lg mr-2">🤖</span>
                  Generate Automation Config
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left"
                  onClick={() => {
                    // TODO: 实现改进建议功能
                    alert('Improvement suggestions feature coming soon!');
                  }}
                >
                  <span className="text-lg mr-2">💡</span>
                  Improvement Suggestions
                </Button>
              </div>

              {/* Chat Input */}
              <div className="flex gap-2">
                <Input
                  placeholder="Ask me anything about test cases..."
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      // TODO: 实现聊天功能
                      alert('Chat feature coming soon!');
                    }
                  }}
                />
                <Button size="sm">
                  Send
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // 根据是否在客户端决定是否包装DndContext
  if (!isClient) {
    return content;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {content}
      {/* Enhanced Drag Overlay */}
      <DragOverlay>
        {activeId ? (
          <div className="bg-white dark:bg-zinc-800 border border-primary/20 dark:border-primary/30 rounded-lg p-3 shadow-2xl ring-1 ring-primary/20 backdrop-blur-sm">
            <div className="flex items-center gap-3">
              {findNode(testCaseTree, activeId)?.isFolder !== false ? (
                <div className="w-5 h-5 bg-primary rounded-md flex items-center justify-center">
                  <FolderIcon className="w-3 h-3 text-primary-foreground" />
                </div>
              ) : (
                <div className="w-5 h-5 bg-primary rounded-md flex items-center justify-center">
                  <FileTextIcon className="w-3 h-3 text-primary-foreground" />
                </div>
              )}
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                {findNode(testCaseTree, activeId)?.name}
              </span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        ) : null}
      </DragOverlay>

      {/* 创建对话框 */}
      <CreateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        type={createType}
        onCreateFolder={handleCreateFolder}
        onCreateTestCase={handleCreateTestCase}
        isCreating={isCreating}
        parentFolder={selected?.isFolder ? selected.name : undefined}
      />
    </DndContext>
  );
}

// 创建对话框组件
function CreateDialog({
  open,
  onOpenChange,
  type,
  onCreateFolder,
  onCreateTestCase,
  isCreating,
  parentFolder
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'folder' | 'testcase';
  onCreateFolder: (name: string, description?: string) => void;
  onCreateTestCase: (name: string, description?: string) => void;
  isCreating: boolean;
  parentFolder?: string;
}) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    if (type === 'folder') {
      onCreateFolder(name.trim(), description.trim() || undefined);
    } else {
      onCreateTestCase(name.trim(), description.trim() || undefined);
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      setName('');
      setDescription('');
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {type === 'folder' ? 'Create New Folder' : 'Create New Test Case'}
          </DialogTitle>
          <DialogDescription>
            {type === 'folder'
              ? `Create a new folder${parentFolder ? ` inside "${parentFolder}"` : ' at the root level'}.`
              : `Create a new test case${parentFolder ? ` in "${parentFolder}"` : ' at the root level'}.`
            }
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">
                {type === 'folder' ? 'Folder Name' : 'Test Case Name'}
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={type === 'folder' ? 'Enter folder name...' : 'Enter test case name...'}
                disabled={isCreating}
                autoFocus
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={type === 'folder'
                  ? 'Enter folder description...'
                  : 'Enter test case description...'
                }
                disabled={isCreating}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!name.trim() || isCreating}
              className="min-w-[100px]"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                `Create ${type === 'folder' ? 'Folder' : 'Test Case'}`
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}