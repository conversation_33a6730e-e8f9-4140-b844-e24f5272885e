'use client';

import { useEffect } from 'react';
import { useThemeStore } from '@/stores/theme-store';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { theme, isDarkMode, initializeTheme } = useThemeStore();

  useEffect(() => {
    // 初始化主题
    initializeTheme();

    // 监听系统深色模式变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // 只有在用户没有手动设置深色模式时才跟随系统
      const hasManualDarkMode = localStorage.getItem('theme-store')?.includes('isDarkMode');
      if (!hasManualDarkMode) {
        useThemeStore.getState().setDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [initializeTheme]);

  // 监听主题变化，确保DOM同步
  useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      const { theme: newTheme, isDarkMode: newIsDark } = event.detail;
      console.log('Theme changed:', { theme: newTheme, isDarkMode: newIsDark });
    };

    window.addEventListener('theme-changed', handleThemeChange as EventListener);

    return () => {
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  return <>{children}</>;
}
