'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Link, 
  ExternalLink,
  FileText,
  User,
  Plus
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function RequirementsModule({ 
  testCase, 
  isEditing, 
  onUpdate 
}: ModuleProps) {
  
  const getStatusColor = (status: string) => {
    const colors = {
      'open': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'in-progress': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'done': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'blocked': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    return colors[status as keyof typeof colors] || colors.open;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'story':
        return <FileText className="w-4 h-4 text-blue-600" />;
      case 'epic':
        return <FileText className="w-4 h-4 text-purple-600" />;
      case 'task':
        return <FileText className="w-4 h-4 text-green-600" />;
      case 'document':
        return <FileText className="w-4 h-4 text-orange-600" />;
      default:
        return <FileText className="w-4 h-4 text-slate-600" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-green-200 dark:border-green-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Link className="w-5 h-5 text-green-600" />
            <h2 className="text-xl font-semibold">Related Requirements</h2>
            <Badge variant="outline" className="ml-2">
              {testCase.relatedRequirements.length} linked
            </Badge>
          </div>
          {isEditing && (
            <Button 
              variant="outline" 
              size="sm"
              className="border-green-200 hover:bg-green-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Link Requirement
            </Button>
          )}
        </div>

        {testCase.relatedRequirements.length > 0 ? (
          <div className="space-y-4">
            {testCase.relatedRequirements.map((requirement) => (
              <div
                key={requirement.id}
                className="flex items-start gap-4 p-4 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 hover:bg-slate-100 dark:hover:bg-slate-800/70 transition-all duration-200"
              >
                <div className="flex items-center gap-2">
                  {getTypeIcon(requirement.type)}
                  <Badge variant="secondary" className="text-xs">
                    {requirement.type.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-1">
                        {requirement.title}
                      </h4>
                      <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-400">
                        <span className="font-mono">{requirement.id}</span>
                        {requirement.assignee && (
                          <div className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            <span>{requirement.assignee}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(requirement.status)}>
                        {requirement.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                      {requirement.url && (
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Link className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
              No requirements linked
            </h3>
            <p className="text-slate-500 dark:text-slate-500 mb-4">
              Link this test case to related requirements, stories, or documents
            </p>
            {isEditing && (
              <Button 
                variant="outline"
                className="border-green-200 hover:bg-green-50"
              >
                <Plus className="w-4 h-4 mr-2" />
                Link First Requirement
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
