"use client"

import * as React from "react"
import {
  BarChart3,
  Calendar,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Users,
} from "lucide-react"

import { SimpleSidebar, SimpleSidebarHeader, SimpleSidebarContent, SimpleSidebarFooter } from "./simple-sidebar"
import { SimpleNavMain } from "./simple-nav-main"
import { NavigationLayoutSwitcher } from "../navigation-layout-switcher"
import { useSidebarCollapsed } from "@/stores/simple-navigation-store"

// 菜单数据
const data = {
  navMain: [
    {
      title: "仪表板",
      url: "/dashboard",
      icon: Home,
    },
    {
      title: "聊天",
      url: "/chat",
      icon: MessageSquare,
      badge: "AI",
    },
    {
      title: "测试用例",
      url: "/test-case",
      icon: FileText,
    },
    {
      title: "测试计划",
      url: "/test-plan",
      icon: FileText,
    },
    {
      title: "测试报告",
      url: "/test-report",
      icon: BarChart3,
    },
    {
      title: "项目列表",
      url: "/projects",
      icon: BarChart3,
    },
    {
      title: "任务管理",
      url: "/tasks",
      icon: FileText,
    },
    {
      title: "团队",
      url: "/team",
      icon: Users,
    },
    {
      title: "日历",
      url: "/calendar",
      icon: Calendar,
    },
    {
      title: "设置",
      url: "/settings",
      icon: Settings,
    },
  ],
}

export function SimpleSidebarLeft() {
  const sidebarCollapsed = useSidebarCollapsed()

  return (
    <SimpleSidebar>
      <SimpleSidebarHeader>
        <div className="flex items-center gap-2">
          {!sidebarCollapsed && (
            <div className="flex flex-col">
              <span className="text-lg font-semibold">AI Run</span>
              <span className="text-xs text-muted-foreground">测试管理平台</span>
            </div>
          )}
        </div>
      </SimpleSidebarHeader>
      
      <SimpleSidebarContent>
        <SimpleNavMain items={data.navMain} />
      </SimpleSidebarContent>
      
      <SimpleSidebarFooter>
        <div className="flex items-center justify-center">
          {!sidebarCollapsed && (
            <NavigationLayoutSwitcher showLabels={false} />
          )}
        </div>
      </SimpleSidebarFooter>
    </SimpleSidebar>
  )
}
