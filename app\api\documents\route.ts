import { NextRequest, NextResponse } from 'next/server';
import { getDocuments } from '@/lib/db/queries';
import { dbLogger } from '@/lib/logger';

// 创建模块专用的日志记录器
const logger = dbLogger.child('documents-api');

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const kind = searchParams.get('kind') || undefined;
    const userId = searchParams.get('userId') || undefined; // 允许通过参数筛选用户
    const sortBy = searchParams.get('sortBy') as 'createdAt' | 'title' || 'createdAt';
    const sortDirection = searchParams.get('sortDirection') as 'asc' | 'desc' || 'desc';
    
    logger.info(`获取文档列表: userId=${userId}, limit=${limit}, offset=${offset}, kind=${kind}`);
    
    // 调用数据库查询
    const result = await getDocuments({
      limit,
      offset,
      userId,
      kind: kind as any,
      sortBy,
      sortDirection,
    });
    
    logger.info(`成功获取文档列表，返回${result.documents.length}条记录`);
    
    // 返回结果
    return new NextResponse(JSON.stringify(result), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    logger.error(`获取文档列表失败: ${error instanceof Error ? error.message : String(error)}`);
    
    return new NextResponse(
      JSON.stringify({
        error: '获取文档列表失败',
        message: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
} 