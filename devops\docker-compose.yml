version: '3.8'

services:
  # Next.js 应用服务
  app:
    build:
      context: ..
      dockerfile: devops/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - DB_PROVIDER=sqlite
      - DATABASE_URL=file:./data.db
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key-here
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
    restart: unless-stopped
    depends_on:
      - db

  # PostgreSQL 数据库服务 (可选，如果你想使用PostgreSQL而不是SQLite)
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ai_run
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis 缓存服务 (可选，用于会话存储)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
volumes:
  postgres_data:
  redis_data:

