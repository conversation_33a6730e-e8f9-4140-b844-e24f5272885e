'use client';

import { useEffect, useState } from 'react';
import { useThemeStore } from '@/stores/theme-store';
import { Button } from '@/components/ui/button';

export default function ThemeTestPage() {
  const { theme, isDarkMode, setTheme } = useThemeStore();
  const [htmlClasses, setHtmlClasses] = useState('');
  const [localStorageData, setLocalStorageData] = useState('');

  useEffect(() => {
    // 更新HTML类列表显示
    const updateClasses = () => {
      setHtmlClasses(document.documentElement.className);
    };

    // 更新localStorage数据显示
    const updateLocalStorage = () => {
      const data = localStorage.getItem('theme-store');
      setLocalStorageData(data || 'null');
    };

    updateClasses();
    updateLocalStorage();

    // 监听DOM变化
    const observer = new MutationObserver(updateClasses);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });

    return () => observer.disconnect();
  }, [theme, isDarkMode]);

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold text-primary">主题测试页面</h1>
      
      <div className="space-y-4">
        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">当前主题状态</h2>
          <p><strong>主题:</strong> {theme}</p>
          <p><strong>深色模式:</strong> {isDarkMode ? '是' : '否'}</p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">HTML类列表</h2>
          <p className="font-mono text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
            {htmlClasses || '(无类)'}
          </p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">localStorage数据</h2>
          <pre className="font-mono text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
            {localStorageData}
          </pre>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">主题切换测试</h2>
          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={() => setTheme('blue')}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              蓝色主题
            </Button>
            <Button 
              onClick={() => setTheme('green')}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              绿色主题
            </Button>
            <Button 
              onClick={() => setTheme('purple')}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              紫色主题
            </Button>
            <Button 
              onClick={() => setTheme('orange')}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              橙色主题
            </Button>
          </div>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">主题色测试元素</h2>
          <div className="space-y-2">
            <div className="w-full h-4 bg-primary rounded"></div>
            <div className="w-full h-4 bg-primary/80 rounded"></div>
            <div className="w-full h-4 bg-primary/60 rounded"></div>
            <div className="w-full h-4 bg-primary/40 rounded"></div>
            <div className="w-full h-4 bg-primary/20 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
