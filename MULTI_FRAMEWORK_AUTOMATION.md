# 多框架自动化配置架构

## 🎯 架构概述

重新设计了自动化配置功能，将Midscene、Playwright、Cypress、Selenium四个框架独立成单独的组件，支持一个测试用例配置多个自动化框架。

## 🏗️ 组件架构

### 1. 基础组件 (BaseAutomationConfig)
- **路径**: `app/test-case/[id]/components/automation/BaseAutomationConfig.tsx`
- **功能**: 提供通用的自动化配置UI模板
- **特性**:
  - 支持配置存在/不存在两种状态
  - 统一的操作按钮（编辑、删除、创建、运行）
  - 可配置的框架图标和颜色
  - 响应式布局

### 2. 框架特定组件

#### MidsceneConfig
- **路径**: `app/test-case/[id]/components/automation/MidsceneConfig.tsx`
- **图标**: Bot (紫色)
- **特色**: AI驱动的自动化测试

#### PlaywrightConfig
- **路径**: `app/test-case/[id]/components/automation/PlaywrightConfig.tsx`
- **图标**: Activity (绿色)
- **特色**: 现代Web应用端到端测试

#### CypressConfig
- **路径**: `app/test-case/[id]/components/automation/CypressConfig.tsx`
- **图标**: CheckCircle (蓝色)
- **特色**: 前端测试框架

#### SeleniumConfig
- **路径**: `app/test-case/[id]/components/automation/SeleniumConfig.tsx`
- **图标**: Globe (橙色)
- **特色**: 传统Web自动化测试

### 3. 主容器组件 (AutomationModule)
- **路径**: `app/test-case/[id]/components/AutomationModule.tsx`
- **功能**: 
  - 管理所有框架组件
  - 提供统一的操作处理
  - 2x2网格布局展示

## 📊 数据结构

### 类型定义更新

```typescript
// 单个自动化配置
interface AutomationConfig {
  id?: string;
  repository: string;
  branch: string;
  commands: string[];
  parameters: Record<string, string>;
  framework: 'selenium' | 'playwright' | 'cypress' | 'midscene';
  browser: 'chrome' | 'firefox' | 'safari' | 'edge';
  environment: 'dev' | 'test' | 'staging' | 'prod';
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 多框架配置集合
interface AutomationConfigs {
  midscene?: AutomationConfig;
  playwright?: AutomationConfig;
  cypress?: AutomationConfig;
  selenium?: AutomationConfig;
}

// 测试用例结构更新
interface TestCase {
  // ... 其他字段
  automationConfigs?: AutomationConfigs; // 替代原来的 automation
}
```

### Mock数据示例

```typescript
// 测试用例 1-1-1: 支持 Midscene + Playwright
automationConfigs: {
  midscene: {
    framework: 'midscene',
    repository: 'https://github.com/company/test-automation',
    branch: 'main',
    isActive: true,
    // ... 其他配置
  },
  playwright: {
    framework: 'playwright',
    repository: 'https://github.com/company/playwright-tests',
    branch: 'develop',
    isActive: false,
    // ... 其他配置
  }
}
```

## 🔧 API更新

### GET /api/automation-config

#### 获取所有框架配置
```
GET /api/automation-config?testCaseId=1-1-1
```
返回:
```json
{
  "midscene": { /* 配置对象 */ },
  "playwright": { /* 配置对象 */ }
}
```

#### 获取特定框架配置
```
GET /api/automation-config?testCaseId=1-1-1&framework=midscene
```
返回:
```json
{ /* midscene配置对象 */ }
```

### POST /api/automation-config
创建或更新特定框架的配置，需要在请求体中指定framework字段。

## 🎨 UI特性

### 1. 网格布局
- 2x2网格展示四个框架
- 响应式设计，移动端自动调整为单列

### 2. 状态展示
- **已配置**: 显示完整配置信息和操作按钮
- **未配置**: 显示空状态和"Configure"按钮

### 3. 框架识别
- 每个框架有独特的图标和颜色主题
- 状态指示器显示配置是否激活
- 框架徽章显示

### 4. 操作按钮
- **编辑**: 修改现有配置
- **删除**: 删除配置
- **创建**: 创建新配置
- **运行**: 执行测试

## 📈 扩展性

### 1. 添加新框架
1. 创建新的框架组件 (如 `WebdriverIOConfig.tsx`)
2. 在 `AutomationModule.tsx` 中添加组件引用
3. 更新类型定义中的框架枚举
4. 添加对应的图标和颜色

### 2. 自定义配置
每个框架组件都可以独立扩展，支持框架特定的配置选项。

## 🔍 测试用例示例

### 1-1-1: Login with valid user
- **Midscene**: 激活状态，Chrome浏览器，test环境
- **Playwright**: 未激活状态，Firefox浏览器，staging环境

### 1-1-2: Login with invalid credentials  
- **Playwright**: 激活状态，安全测试配置
- **Selenium**: 激活状态，Python测试套件

### 1-2-1: User profile update
- **Cypress**: 激活状态，E2E测试配置
- **Midscene**: 未激活状态，AI测试配置

### 2-1-1: API Authentication Test
- **Selenium**: 激活状态，API测试配置
- **Playwright**: 激活状态，API测试配置
- **Cypress**: 未激活状态，API测试配置

## 🚀 使用流程

1. **查看配置**: 在测试用例详情页面点击"Automation"标签
2. **选择框架**: 查看四个框架的配置状态
3. **配置框架**: 点击"Configure"按钮设置新框架
4. **管理配置**: 使用编辑/删除按钮管理现有配置
5. **运行测试**: 点击"Run Test"执行特定框架的测试

这种架构提供了更好的灵活性和可扩展性，允许团队根据需要为不同的测试场景选择最适合的自动化框架。
