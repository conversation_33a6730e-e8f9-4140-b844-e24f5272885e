import { Chat<PERSON>penAI } from '@langchain/openai';
import { LangChainAdapter } from 'ai';
import { MockLanguageModelV1 } from 'ai/test';
import { simulateReadableStream } from 'ai';
import { CallbackManager } from '@langchain/core/callbacks/manager';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';

// 定义流部分类型，确保与LanguageModelV1StreamPart兼容
type TextDeltaStreamPart = {
  type: 'text-delta';
  textDelta: string;
};

type FinishStreamPart = {
  type: 'finish';
  finishReason: 'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
};

// 创建LangChain ChatOpenAI模型
export const createLangChainModel = (modelName: string = 'gpt-3.5-turbo', apiKey?: string) => {
  const model = new ChatOpenAI({
    modelName,
    temperature: 0.7,
    openAIApiKey: apiKey || process.env.OPENAI_API_KEY || "sk-fe993c92dcc64df59dc08250494935a2",
    configuration: {
      baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    }
  });
  
  return model;
};

// 创建一个纯文本流处理函数，用于移除前缀格式问题
export function createCleanTextStream(originalStream: AsyncIterable<any>): ReadableStream<any> {
  const reader = originalStream[Symbol.asyncIterator]();
  const encoder = new TextEncoder();
  
  return new ReadableStream({
    async pull(controller) {
      try {
        const { value, done } = await reader.next();
        
        // 记录原始值以便调试
        console.log("原始流数据:", JSON.stringify(value));
        
        if (done) {
          console.log("流结束");
          // 发送SSE格式的结束事件
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();
          return;
        }
        
        // 处理有内容的消息块
        if (value && value.content && typeof value.content === 'string') {
          console.log("原始内容:", value.content);
          
          // 移除可能的前缀格式问题
          let cleanContent = value.content;
          
          // 移除类似 0:"text" 格式的前缀
          const regex1 = /^\d+:"(.*)"$/;
          const match1 = regex1.exec(cleanContent);
          if (match1) {
            cleanContent = match1[1];
            console.log("匹配到数字前缀格式，清理后:", cleanContent);
          }
          
          // 移除类似 f:{"messageId":"xxx"} 0:"text" 格式的前缀
          const regex2 = /^f:\{"messageId":"[^"]+"\}\s*(\d+:".*")/;
          const match2 = regex2.exec(cleanContent);
          if (match2) {
            cleanContent = match2[1];
            console.log("匹配到messageId前缀格式，第一次清理后:", cleanContent);
            
            // 再次应用第一个正则表达式
            const nestedMatch = regex1.exec(cleanContent);
            if (nestedMatch) {
              cleanContent = nestedMatch[1];
              console.log("进一步清理数字前缀，最终结果:", cleanContent);
            }
          }
          
          // 直接移除整个f:{"messageId":"xxx"} 0:"text" 格式
          const regex3 = /^f:\{"messageId":"[^"]+"\}\s*\d+:"(.*)"/;
          const match3 = regex3.exec(cleanContent);
          if (match3) {
            cleanContent = match3[1];
            console.log("使用组合正则表达式清理，结果:", cleanContent);
          }
          
          console.log("最终清理后的内容:", cleanContent);
          
          // 使用SSE格式发送数据
          const chunk = {
            type: 'text',
            content: cleanContent
          };
          const data = JSON.stringify(chunk);
          controller.enqueue(encoder.encode(`data: ${data}\n\n`));
        } else {
          console.log("跳过无内容或非字符串内容的消息块");
        }
      } catch (e) {
        console.error("流处理错误:", e);
        // 发送错误信息
        const errorChunk = {
          type: 'error',
          error: {
            message: e instanceof Error ? e.message : '未知错误'
          }
        };
        const data = JSON.stringify(errorChunk);
        controller.enqueue(encoder.encode(`data: ${data}\n\n`));
        controller.close();
      }
    }
  });
}

// 创建LangChain适配器模型
export const langChainModel = new MockLanguageModelV1({
  doGenerate: async ({ prompt }) => {
    try {
      const model = new ChatOpenAI({
        openAIApiKey: "sk-fe993c92dcc64df59dc08250494935a2",
        configuration: {
          baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
        },
        modelName: "qwen-max",
        temperature: 0.7,
      });
      const response = await model.invoke(prompt.map(m => m.content).join('\n'));
      
      return {
        text: typeof response.content === 'string' ? response.content : JSON.stringify(response.content),
        rawCall: { rawPrompt: prompt, rawSettings: {} },
        finishReason: 'stop',
        usage: { promptTokens: 10, completionTokens: 20 },
      };
    } catch (error) {
      console.error('LangChain模型调用错误:', error);
      return {
        text: '抱歉，处理您的请求时出错了。',
        rawCall: { rawPrompt: prompt, rawSettings: {} },
        finishReason: 'error',
        usage: { promptTokens: 10, completionTokens: 0 },
      };
    }
  },
  doStream: async ({ prompt }) => {
    try {
      // 创建LangChain模型，使用实际的ChatOpenAI模型
      const model = new ChatOpenAI({
        modelName: 'gpt-3.5-turbo-0125', // 使用最新的模型版本
        temperature: 0.7,
        openAIApiKey: process.env.OPENAI_API_KEY || "sk-fe993c92dcc64df59dc08250494935a2",
        configuration: {
          baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        }
      });
      
      // 使用LangChain模型流式处理请求
      const langChainStream = await model.stream(prompt.map(m => m.content).join('\n'));
      
      // 使用清洁流处理
      const cleanStream = createCleanTextStream(langChainStream);
      
      return {
        stream: cleanStream,
        rawCall: { rawPrompt: prompt, rawSettings: {} },
      };
    } catch (error) {
      console.error('LangChain流式调用错误:', error);
      
      // 创建一个错误流
      const chunks: Array<TextDeltaStreamPart | FinishStreamPart> = [
        { type: 'text-delta', textDelta: '抱歉，处理您的请求时出错了。' },
        { 
          type: 'finish', 
          finishReason: 'error', 
          usage: { promptTokens: 10, completionTokens: 0, totalTokens: 10 }
        }
      ];
      
      return {
        stream: simulateReadableStream({ chunks }),
        rawCall: { rawPrompt: prompt, rawSettings: {} },
      };
    }
  },
});

// 创建使用qwen3的LangChain模型
export const langChainQwen3Model = new MockLanguageModelV1({
  doGenerate: async ({ prompt }) => {
    try {
        const model = new ChatOpenAI({
            openAIApiKey: "sk-fe993c92dcc64df59dc08250494935a2",
            configuration: {
              baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
            },
            modelName: "qwen-max",
            temperature: 0.7,
            streaming: true,
            verbose: false, // 禁用详细日志
            maxRetries: 3, // 增加重试次数
            modelKwargs: {
              // 添加模型特定参数
              model_mapping: {
                "qwen-max": "gpt-3.5-turbo"
              }
            }
          });
      const response = await model.invoke(prompt.map(m => m.content).join('\n'));
      
      return {
        text: typeof response.content === 'string' ? response.content : JSON.stringify(response.content),
        rawCall: { rawPrompt: prompt, rawSettings: {} },
        finishReason: 'stop',
        usage: { promptTokens: 10, completionTokens: 20 },
      };
    } catch (error) {
      console.error('LangChain Qwen3模型调用错误:', error);
      return {
        text: '抱歉，处理您的请求时出错了。',
        rawCall: { rawPrompt: prompt, rawSettings: {} },
        finishReason: 'error',
        usage: { promptTokens: 10, completionTokens: 0 },
      };
    }
  },
  doStream: async ({ prompt }) => {
    try {
      // 初始化 LLM（function call 支持的模型） QWEN3
      const model = new ChatOpenAI({
        openAIApiKey: "sk-fe993c92dcc64df59dc08250494935a2",
        configuration: {
          baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
        },
        modelName: "qwen-max",
        temperature: 0.7,
        streaming: true,
        verbose: false, // 禁用详细日志
        maxRetries: 3, // 增加重试次数
        modelKwargs: {
          // 添加模型特定参数
          model_mapping: {
            "qwen-max": "gpt-3.5-turbo"
          }
        }
      });
      
      // 使用LangChain模型流式处理请求
      const langChainStream = await model.stream(prompt.map(m => m.content).join('\n'));
      
      // 使用清洁流处理
      const cleanStream = createCleanTextStream(langChainStream);
      
      return {
        stream: cleanStream,
        rawCall: { rawPrompt: prompt, rawSettings: {} },
      };
    } catch (error) {
      console.error('LangChain Qwen3流式调用错误:', error);
      
      // 创建一个错误流
      const chunks: Array<TextDeltaStreamPart | FinishStreamPart> = [
        { type: 'text-delta', textDelta: '抱歉，处理您的请求时出错了。' },
        { 
          type: 'finish', 
          finishReason: 'error', 
          usage: { promptTokens: 10, completionTokens: 0, totalTokens: 10 }
        }
      ];
      
      return {
        stream: simulateReadableStream({ chunks }),
        rawCall: { rawPrompt: prompt, rawSettings: {} },
      };
    }
  },
});

// 创建API路由处理函数示例
export async function handleLangChainRequest(prompt: string) {
  try {
    // 创建LangChain模型，使用实际的ChatOpenAI模型
    const model = new ChatOpenAI({
      modelName: 'gpt-3.5-turbo-0125', // 使用最新的模型版本
      temperature: 0, // 设置为0以获得确定性输出
      openAIApiKey: process.env.OPENAI_API_KEY || "sk-fe993c92dcc64df59dc08250494935a2",
      configuration: {
        baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      }
    });
    
    // 使用LangChain模型流式处理请求
    const stream = await model.stream(prompt);
    
    // 使用LangChainAdapter转换流并返回响应
    return LangChainAdapter.toDataStreamResponse(stream);
  } catch (error) {
    console.error('处理LangChain请求时出错:', error);
    return new Response(JSON.stringify({ error: '处理请求时出错' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}