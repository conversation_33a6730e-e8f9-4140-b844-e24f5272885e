import type { InferSelectModel } from 'drizzle-orm';

const isSqlite = process.env.DB_PROVIDER === 'sqlite';

// 动态导入类型
const core = isSqlite
  ? require('drizzle-orm/sqlite-core')
  : require('drizzle-orm/pg-core');

const table = isSqlite ? core.sqliteTable : core.pgTable;
const textType = core.text;
const intType = core.integer;
const boolType = isSqlite ? () => intType() : core.boolean;
const primaryKey = core.primaryKey;
const foreignKey = core.foreignKey;
const timestampType = isSqlite ? () => intType() : core.timestamp;
const uuidType = isSqlite ? () => textType() : core.uuid;
const varcharType = isSqlite ? () => textType() : core.varchar;
const jsonType = isSqlite ? core.text : core.json;

export const user = table('user', {
  id: uuidType('id').primaryKey().notNull(),
  email: varcharType('email', { length: 64 }).notNull(),
  password: varcharType('password', { length: 64 }),
});

export type User = InferSelectModel<typeof user>;

export const chat = table('chat', {
  id: uuidType('id').primaryKey().notNull(),
  createdAt: timestampType('createdAt').notNull(),
  title: textType('title').notNull(),
  userId: uuidType('userId').notNull(),
  visibility: varcharType('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
});

export type Chat = InferSelectModel<typeof chat>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://chat-sdk.dev/docs/migration-guides/message-parts
export const messageDeprecated = table('message', {
  id: uuidType('id').primaryKey().notNull(),
  chatId: uuidType('chatId').notNull(),
  role: varcharType('role').notNull(),
  content: jsonType('content').notNull(),
  createdAt: timestampType('createdAt').notNull(),
});

export type MessageDeprecated = InferSelectModel<typeof messageDeprecated>;

export const message = table('message_v2', {
  id: uuidType('id').primaryKey().notNull(),
  chatId: uuidType('chatId').notNull(),
  role: varcharType('role').notNull(),
  parts: jsonType('parts').notNull(),
  attachments: jsonType('attachments').notNull(),
  createdAt: timestampType('createdAt').notNull(),
});

export type DBMessage = InferSelectModel<typeof message>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://chat-sdk.dev/docs/migration-guides/message-parts
export const voteDeprecated = table(
  'vote',
  {
    chatId: uuidType('chatId').notNull(),
    messageId: uuidType('messageId').notNull(),
    isUpvoted: boolType('isUpvoted').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.chatId, tbl.messageId] }),
  })
);

export type VoteDeprecated = InferSelectModel<typeof voteDeprecated>;

export const vote = table(
  'vote_v2',
  {
    chatId: uuidType('chatId').notNull(),
    messageId: uuidType('messageId').notNull(),
    isUpvoted: boolType('isUpvoted').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.chatId, tbl.messageId] }),
  })
);

export type Vote = InferSelectModel<typeof vote>;

export const document = table(
  'document',
  {
    id: uuidType('id').notNull(),
    createdAt: timestampType('createdAt').notNull(),
    title: textType('title').notNull(),
    content: textType('content'),
    kind: varcharType('text', { enum: ['text', 'code', 'image', 'sheet', 'midscene_report'] })
      .notNull()
      .default('text'),
    userId: uuidType('userId').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.id, tbl.createdAt] }),
  })
);

export type Document = InferSelectModel<typeof document>;

export const suggestion = table(
  'suggestion',
  {
    id: uuidType('id').notNull(),
    documentId: uuidType('documentId').notNull(),
    documentCreatedAt: timestampType('documentCreatedAt').notNull(),
    originalText: textType('originalText').notNull(),
    suggestedText: textType('suggestedText').notNull(),
    description: textType('description'),
    isResolved: boolType('isResolved').notNull().default(false),
    userId: uuidType('userId').notNull(),
    createdAt: timestampType('createdAt').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.id] }),
  })
);

export type Suggestion = InferSelectModel<typeof suggestion>;

export const stream = table(
  'stream',
  {
    id: uuidType('id').notNull(),
    chatId: uuidType('chatId').notNull(),
    createdAt: timestampType('createdAt').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.id] }),
  })
);

export type Stream = InferSelectModel<typeof stream>;

// ==================== 测试用例管理系统表结构 ====================

// 文件夹表 - 用于组织测试用例
export const folder = table('folder', {
  id: uuidType('id').primaryKey().notNull(),
  name: varcharType('name', { length: 255 }).notNull(),
  description: textType('description'),
  parentId: uuidType('parentId'), // 支持嵌套文件夹
  path: textType('path').notNull(), // 完整路径，如 "/root/api/auth"
  level: intType('level').notNull().default(0), // 层级深度
  sortOrder: intType('sortOrder').notNull().default(0), // 排序
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
  createdBy: uuidType('createdBy').notNull(),
  updatedBy: uuidType('updatedBy').notNull(),
});

export type Folder = InferSelectModel<typeof folder>;

// 测试用例主表
export const testCase = table('testcase', {
  id: uuidType('id').primaryKey().notNull(),
  folderId: uuidType('folderId'), // 所属文件夹
  name: varcharType('name', { length: 500 }).notNull(),
  description: textType('description').notNull(),
  preconditions: textType('preconditions'),
  priority: varcharType('priority', { enum: ['high', 'medium', 'low'] }).notNull().default('medium'),
  status: varcharType('status', { enum: ['work-in-progress', 'active', 'deprecated', 'draft'] }).notNull().default('draft'),
  weight: varcharType('weight', { enum: ['high', 'medium', 'low'] }).notNull().default('medium'),
  format: varcharType('format', { enum: ['classic', 'bdd', 'exploratory'] }).notNull().default('classic'),
  nature: varcharType('nature', { enum: ['functional', 'performance', 'security', 'usability'] }).notNull().default('functional'),
  type: varcharType('type', { enum: ['regression', 'smoke', 'integration', 'unit'] }).notNull().default('regression'),
  tags: jsonType('tags').notNull().default('[]'), // JSON数组存储标签
  executionTime: intType('executionTime'), // 预估执行时间（分钟）
  lastRunAt: timestampType('lastRunAt'), // 最后运行时间
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
  createdBy: uuidType('createdBy').notNull(),
  updatedBy: uuidType('updatedBy').notNull(),
});

export type TestCase = InferSelectModel<typeof testCase>;

// 测试步骤表
export const testStep = table('teststep', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  stepNumber: intType('stepNumber').notNull(), // 步骤序号
  action: textType('action').notNull(), // 操作描述
  expected: textType('expected').notNull(), // 预期结果
  type: varcharType('type', { enum: ['manual', 'automated', 'optional'] }).notNull().default('manual'),
  notes: textType('notes'), // 备注
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
});

export type TestStep = InferSelectModel<typeof testStep>;

// 自动化配置表
export const automationConfig = table('automationconfig', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  repository: varcharType('repository', { length: 500 }).notNull(),
  branch: varcharType('branch', { length: 100 }).notNull().default('main'),
  commands: jsonType('commands').notNull(), // JSON数组存储命令
  parameters: jsonType('parameters').notNull().default('{}'), // JSON对象存储参数
  framework: varcharType('framework', { enum: ['selenium', 'playwright', 'cypress', 'midscene'] }).notNull().default('midscene'),
  browser: varcharType('browser', { enum: ['chrome', 'firefox', 'safari', 'edge'] }).notNull().default('chrome'),
  environment: varcharType('environment', { enum: ['dev', 'test', 'staging', 'prod'] }).notNull().default('test'),
  isActive: boolType('isActive').notNull().default(true),
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
});

export type AutomationConfig = InferSelectModel<typeof automationConfig>;

// 相关需求表
export const relatedRequirement = table('relatedrequirement', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  requirementId: varcharType('requirementId', { length: 100 }).notNull(), // 外部需求ID
  type: varcharType('type', { enum: ['story', 'epic', 'task', 'document'] }).notNull(),
  title: varcharType('title', { length: 500 }).notNull(),
  status: varcharType('status', { enum: ['open', 'in-progress', 'done', 'blocked'] }).notNull(),
  assignee: varcharType('assignee', { length: 100 }),
  url: varcharType('url', { length: 1000 }), // 需求链接
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
});

export type RelatedRequirement = InferSelectModel<typeof relatedRequirement>;

// 数据集表
export const dataset = table('dataset', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  name: varcharType('name', { length: 255 }).notNull(),
  description: textType('description'),
  columns: jsonType('columns').notNull(), // JSON数组存储列定义
  data: jsonType('data').notNull(), // JSON数组存储数据行
  isActive: boolType('isActive').notNull().default(true),
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
});

export type Dataset = InferSelectModel<typeof dataset>;

// 测试运行表
export const testRun = table('testrun', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  runDate: timestampType('runDate').notNull(),
  status: varcharType('status', { enum: ['passed', 'failed', 'running', 'skipped'] }).notNull(),
  duration: intType('duration'), // 执行时长（秒）
  environment: varcharType('environment', { length: 100 }).notNull(),
  executor: varcharType('executor', { length: 100 }).notNull(), // 执行者
  results: jsonType('results').notNull().default('[]'), // JSON数组存储步骤结果
  errorMessage: textType('errorMessage'), // 错误信息
  logs: textType('logs'), // 执行日志
  screenshots: jsonType('screenshots').notNull().default('[]'), // 截图URL数组
  createdAt: timestampType('createdAt').notNull(),
});

export type TestRun = InferSelectModel<typeof testRun>;

// 已知问题表
export const knownIssue = table('knownissue', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  title: varcharType('title', { length: 500 }).notNull(),
  description: textType('description').notNull(),
  severity: varcharType('severity', { enum: ['critical', 'high', 'medium', 'low'] }).notNull(),
  status: varcharType('status', { enum: ['open', 'investigating', 'resolved', 'wont-fix'] }).notNull().default('open'),
  reporter: varcharType('reporter', { length: 100 }).notNull(),
  assignee: varcharType('assignee', { length: 100 }),
  bugUrl: varcharType('bugUrl', { length: 1000 }), // Bug跟踪系统链接
  workaround: textType('workaround'), // 临时解决方案
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
  resolvedAt: timestampType('resolvedAt'), // 解决时间
});

export type KnownIssue = InferSelectModel<typeof knownIssue>;

// 测试用例标签表（多对多关系）
export const testCaseTag = table('testcasetag', {
  id: uuidType('id').primaryKey().notNull(),
  name: varcharType('name', { length: 100 }).notNull(),
  color: varcharType('color', { length: 7 }).notNull().default('#3B82F6'), // 十六进制颜色
  description: textType('description'),
  createdAt: timestampType('createdAt').notNull(),
  createdBy: uuidType('createdBy').notNull(),
});

export type TestCaseTag = InferSelectModel<typeof testCaseTag>;

// 测试用例标签关联表
export const testCaseTagRelation = table(
  'testcasetagrelation',
  {
    testCaseId: uuidType('testCaseId').notNull(),
    tagId: uuidType('tagId').notNull(),
    createdAt: timestampType('createdAt').notNull(),
  },
  (tbl: any) => ({
    pk: primaryKey({ columns: [tbl.testCaseId, tbl.tagId] }),
  })
);

export type TestCaseTagRelation = InferSelectModel<typeof testCaseTagRelation>;

// 测试用例评论表
export const testCaseComment = table('testcasecomment', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  content: textType('content').notNull(),
  author: uuidType('author').notNull(),
  parentId: uuidType('parentId'), // 支持回复评论
  isResolved: boolType('isResolved').notNull().default(false),
  createdAt: timestampType('createdAt').notNull(),
  updatedAt: timestampType('updatedAt').notNull(),
});

export type TestCaseComment = InferSelectModel<typeof testCaseComment>;

// 测试用例历史版本表
export const testCaseHistory = table('testcasehistory', {
  id: uuidType('id').primaryKey().notNull(),
  testCaseId: uuidType('testCaseId').notNull(),
  version: intType('version').notNull(),
  changeType: varcharType('changeType', { enum: ['created', 'updated', 'deleted', 'restored'] }).notNull(),
  changes: jsonType('changes').notNull(), // 变更内容的JSON
  changeDescription: textType('changeDescription'),
  changedBy: uuidType('changedBy').notNull(),
  createdAt: timestampType('createdAt').notNull(),
});

export type TestCaseHistory = InferSelectModel<typeof testCaseHistory>;
