'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useChat } from '@ai-sdk/react';
// import { Messages } from '@/components/chat/messages'; // 移除以避免URL跳转
import { MultimodalInput } from '@/components/chat/multimodal-input';
// import { DataStreamHandler } from '@/components/chat/data-stream-handler'; // 移除以避免URL跳转
import { generateUUID, fetchWithErrorHandlers, sanitizeText } from '@/lib/utils';
import { toast } from '@/components/chat/toast';
import { ChatSDKError } from '@/lib/errors';
import { TestCase } from '../types';
import { Button } from '@/components/ui/button';
import { Bo<PERSON>, Sparkles, FileText, Play, RefreshCw, User, PanelRightClose } from 'lucide-react';
import type { Attachment, UIMessage } from 'ai';
import { TestCaseMarkdown } from './testcase-markdown';

// 简单的消息显示组件，避免使用会导致URL跳转的Messages组件
function SimpleMessages({ messages }: { messages: UIMessage[] }) {
  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div key={message.id} className="flex gap-3">
          <div className="flex-shrink-0">
            {message.role === 'user' ? (
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
            ) : (
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">
              {message.role === 'user' ? '您' : 'AI助手'}
            </div>
            <div className="prose prose-sm dark:prose-invert max-w-none">
              <TestCaseMarkdown>
                {sanitizeText(typeof message.content === 'string' ? message.content : JSON.stringify(message.content))}
              </TestCaseMarkdown>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

interface TestCaseAssistantProps {
  testCase: TestCase;
  onTestCaseUpdate: (updates: Partial<TestCase>) => void;
  className?: string;
  onCollapse?: () => void;
}

export default function TestCaseAssistant({
  testCase,
  onTestCaseUpdate,
  className = '',
  onCollapse
}: TestCaseAssistantProps) {
  const chatId = `testcase-${testCase.id}`;
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  const {
    messages,
    setMessages,
    handleSubmit: originalHandleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    data,
  } = useChat({
    api: '/api/testcase-chat', // 使用专门的测试用例聊天API
    // id: chatId, // 移除ID以避免与现有聊天会话同步导致的URL跳转
    initialMessages: [],
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    fetch: fetchWithErrorHandlers,
    experimental_prepareRequestBody: (body) => ({
      messages: body.messages,
      testCaseContext: {
        id: testCase.id,
        name: testCase.name,
        description: testCase.description,
        currentModule: 'testcase-assistant'
      }
    }),
    onError: (error) => {
      if (error instanceof ChatSDKError) {
        toast({
          type: 'error',
          description: error.message,
        });
      }
    },
  });

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const [processedMessageIds, setProcessedMessageIds] = useState<Set<string>>(new Set());
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);

  // 自动滚动到底部的函数
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      // 使用requestAnimationFrame确保DOM更新后再滚动
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, []);

  // 包装handleSubmit以添加自动滚动
  const handleSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
    originalHandleSubmit(e);
    // 标记用户已发送消息，隐藏快捷操作
    setHasUserSentMessage(true);
    // 用户发送消息后立即滚动到底部
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  }, [originalHandleSubmit, scrollToBottom]);

  // 处理AI生成的数据流 - 监听消息变化而不是data
  useEffect(() => {
    console.log('Messages updated:', messages.length);

    // 检查所有助手消息中是否包含工具调用结果
    messages.forEach((message) => {
      if (message.role === 'assistant' && message.parts && !processedMessageIds.has(message.id)) {
        console.log('Processing new assistant message:', message.id, message.parts);

        message.parts.forEach((part: any) => {
          if (part.type === 'tool-invocation' && part.toolInvocation?.state === 'result') {
            const { toolName, result } = part.toolInvocation;
            console.log('Found tool result:', toolName, result);

            // 检查是否是测试步骤生成结果
            if (toolName === 'generateTestSteps' && typeof result === 'string' && result.includes('TESTCASE_STEPS:')) {
              console.log('Processing TESTCASE_STEPS result');
              console.log('Full result string:', result);
              try {
                // 更强的正则表达式来匹配JSON数组
                const jsonMatch = result.match(/TESTCASE_STEPS:\s*(\[[\s\S]*?\])/);
                console.log('JSON match:', jsonMatch);
                if (jsonMatch) {
                  const stepsData = JSON.parse(jsonMatch[1]);
                  console.log('Parsed steps data:', stepsData);
                  if (Array.isArray(stepsData)) {
                    const formattedSteps = stepsData.map((step: any, index: number) => ({
                      id: `step-${Date.now()}-${index}`,
                      step: step.step || index + 1,
                      action: step.action || '',
                      expected: step.expected || '',
                      type: step.type || 'manual',
                      notes: step.notes || ''
                    }));

                    console.log('Formatted steps:', formattedSteps);
                    console.log('Calling onTestCaseUpdate with steps:', { steps: formattedSteps });
                    onTestCaseUpdate({ steps: formattedSteps });
                    toast({
                      type: 'success',
                      description: `已生成 ${formattedSteps.length} 个测试步骤并更新到测试用例`
                    });
                  }
                }
              } catch (error) {
                console.error('解析测试步骤数据失败:', error);
                console.error('Error details:', error);
              }
            }

            // 检查是否是测试用例更新结果
            if (toolName === 'updateTestCase' && typeof result === 'string' && result.includes('TESTCASE_UPDATE:')) {
              console.log('Processing TESTCASE_UPDATE result');
              try {
                const jsonMatch = result.match(/TESTCASE_UPDATE:\s*(\{.*?\})/s);
                if (jsonMatch) {
                  const updateData = JSON.parse(jsonMatch[1]);
                  console.log('Update data:', updateData);
                  console.log('Calling onTestCaseUpdate with:', updateData);
                  onTestCaseUpdate(updateData);
                  toast({
                    type: 'success',
                    description: '测试用例信息已更新'
                  });
                }
              } catch (error) {
                console.error('解析测试用例更新数据失败:', error);
              }
            }
          }
        });

        // 标记这条消息已处理
        setProcessedMessageIds(prev => new Set([...prev, message.id]));
      }
    });
  }, [messages, onTestCaseUpdate, processedMessageIds]);



  // 过滤消息内容，移除JSON数据显示
  const filteredMessages = messages.map(message => {
    if (message.role === 'assistant' && typeof message.content === 'string') {
      let content = message.content;

      // 移除TESTCASE_STEPS JSON数据 - 更强的正则表达式
      content = content.replace(/TESTCASE_STEPS:\s*\[[\s\S]*?\]\s*\n*/g, '');

      // 移除TESTCASE_UPDATE JSON数据
      content = content.replace(/TESTCASE_UPDATE:\s*\{[\s\S]*?\}\s*\n*/g, '');

      // 移除AUTOMATION_CONFIG JSON数据
      content = content.replace(/AUTOMATION_CONFIG:\s*\{[\s\S]*?\}\s*\n*/g, '');

      // 移除COVERAGE_ANALYSIS JSON数据
      content = content.replace(/COVERAGE_ANALYSIS:\s*\{[\s\S]*?\}\s*\n*/g, '');

      // 移除MODULE_CONTENT JSON数据
      content = content.replace(/MODULE_CONTENT:\s*\{[\s\S]*?\}\s*\n*/g, '');

      // 清理多余的空行
      content = content.replace(/\n{3,}/g, '\n\n');

      return {
        ...message,
        content: content.trim()
      };
    }
    return message;
  });

  // 滚动检测逻辑已移除

  // 当消息更新时自动滚动
  useEffect(() => {
    // 使用多个延迟确保内容完全渲染后再滚动
    const timers = [50, 150, 300].map(delay =>
      setTimeout(() => scrollToBottom(), delay)
    );

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [messages.length, scrollToBottom]); // 只监听消息数量变化

  // 当AI正在回复时也要滚动
  useEffect(() => {
    if (status === 'loading') {
      const timer = setTimeout(() => scrollToBottom(), 100);
      return () => clearTimeout(timer);
    }
  }, [status, scrollToBottom]);

  // 滚动监听已移除

  // 监听AI工具响应并更新测试用例
  useEffect(() => {
    if (data && data.length > 0) {
      const latestData = data[data.length - 1];

      // 处理测试用例更新
      if (latestData.type === 'tool-result') {
        const content = latestData.result;

        if (typeof content === 'string') {
          // 解析TESTCASE_UPDATE指令
          if (content.includes('TESTCASE_UPDATE:')) {
            try {
              const updateMatch = content.match(/TESTCASE_UPDATE:\s*({.*})/);
              if (updateMatch) {
                const updates = JSON.parse(updateMatch[1]);
                onTestCaseUpdate(updates);
                toast({
                  type: 'success',
                  description: '测试用例已更新',
                });
              }
            } catch (error) {
              console.error('解析测试用例更新失败:', error);
            }
          }

          // 解析TESTCASE_STEPS指令
          if (content.includes('TESTCASE_STEPS:')) {
            try {
              const stepsMatch = content.match(/TESTCASE_STEPS:\s*(\[.*\])/);
              if (stepsMatch) {
                const steps = JSON.parse(stepsMatch[1]);
                onTestCaseUpdate({ steps });
                toast({
                  type: 'success',
                  description: '测试步骤已更新',
                });
              }
            } catch (error) {
              console.error('解析测试步骤失败:', error);
            }
          }

          // 解析AUTOMATION_CONFIG指令
          if (content.includes('AUTOMATION_CONFIG:')) {
            try {
              const configMatch = content.match(/AUTOMATION_CONFIG:\s*({.*})/);
              if (configMatch) {
                const automation = JSON.parse(configMatch[1]);
                onTestCaseUpdate({ automation });
                toast({
                  type: 'success',
                  description: '自动化配置已更新',
                });
              }
            } catch (error) {
              console.error('解析自动化配置失败:', error);
            }
          }
        }
      }
    }
  }, [data, onTestCaseUpdate]);

  // 预设的快速操作
  const quickActions = [
    {
      id: 'generate-steps',
      label: '生成测试步骤',
      icon: FileText,
      prompt: `请基于当前测试用例"${testCase.name}"的描述，生成详细的测试步骤。当前描述：${testCase.description}`
    },
    {
      id: 'improve-description',
      label: '优化描述',
      icon: Sparkles,
      prompt: `请帮我优化这个测试用例的描述，使其更加清晰和专业。当前描述：${testCase.description}`
    },
    {
      id: 'generate-automation',
      label: '生成自动化脚本',
      icon: Play,
      prompt: `请为测试用例"${testCase.name}"生成自动化测试脚本，包括必要的配置和参数。`
    },
    {
      id: 'generate-midscene',
      label: '生成Midscene配置',
      icon: Bot,
      prompt: `请为测试用例"${testCase.name}"生成Midscene自动化配置。

测试用例信息：
- 名称：${testCase.name}
- 描述：${testCase.description}
- ID：${testCase.id}

请提供要测试的网站URL，我将为您生成完整的Midscene YAML配置并保存到数据库中。`
    },
    {
      id: 'suggest-improvements',
      label: '改进建议',
      icon: RefreshCw,
      prompt: `请分析这个测试用例并提供改进建议，包括测试覆盖度、步骤优化等方面。测试用例信息：${JSON.stringify(testCase, null, 2)}`
    }
  ];

  const handleQuickAction = useCallback((action: typeof quickActions[0]) => {
    append({
      role: 'user',
      content: action.prompt,
    });
    // 标记用户已发送消息，隐藏快捷操作
    setHasUserSentMessage(true);
  }, [append]);

  // 检查是否已有用户消息
  useEffect(() => {
    const hasUserMessage = messages.some(message => message.role === 'user');
    if (hasUserMessage && !hasUserSentMessage) {
      setHasUserSentMessage(true);
    }
  }, [messages, hasUserSentMessage]);

  // 欢迎消息
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: UIMessage = {
        id: generateUUID(),
        role: 'assistant',
        content: `👋 您好！我是专业的测试用例助手，支持多模块内容生成！

**当前测试用例**：${testCase.name}

## 🎯 支持的模块

### 📝 测试用例模块
- 生成精确数量的测试步骤（严格按要求）
- 优化测试用例描述和前置条件
- 分析测试覆盖度和风险点

### 🤖 自动化测试模块
- 生成自动化脚本配置
- 支持多框架：Selenium、Playwright、Cypress、Midscene
- CI/CD集成配置

### 📊 数据管理模块
- 生成测试数据集
- 创建数据驱动测试配置
- 设计边界值和异常数据

### 🔗 需求关联模块
- 分析需求覆盖度
- 创建需求-测试用例映射
- 生成可追溯性矩阵

### 📈 报告分析模块
- 生成测试执行报告
- 创建缺陷分析报告
- 提供测试指标分析

## 💡 使用示例
- **精确生成**："请生成5个测试步骤"（将严格生成5个）
- **模块化**："为登录功能生成7个测试步骤"
- **自动化**："创建Midscene自动化配置"
- **数据驱动**："生成3组测试数据"

我会根据不同模块使用专业的提示词，确保生成高质量的内容。有什么可以帮您的吗？`,
        createdAt: new Date(),
      };
      setMessages([welcomeMessage]);
    }
  }, [testCase.name, testCase.id, messages.length, setMessages]);

  return (
    <div className={`testcase-assistant flex flex-col h-full bg-white dark:bg-zinc-900 ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-slate-200 dark:border-zinc-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h3 className="font-semibold text-slate-800 dark:text-slate-200">
              测试用例助手
            </h3>
          </div>
          {onCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCollapse}
              className="h-10 w-10 p-0 hover:bg-slate-100 dark:hover:bg-zinc-800"
              title="收起AI助手"
            >
              <PanelRightClose className="w-6 h-6 text-slate-500 dark:text-slate-400" />
            </Button>
          )}
        </div>
        
        {/* Quick Actions - 只在用户未发送消息时显示 */}
        {!hasUserSentMessage && (
          <div className="grid grid-cols-2 gap-2">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction(action)}
                  className="justify-start text-xs h-8"
                  disabled={status === 'loading'}
                >
                  <Icon className="w-3 h-3 mr-1" />
                  {action.label}
                </Button>
              );
            })}
          </div>
        )}
      </div>

      {/* Messages Area */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 pb-8 break-words relative"
        style={{
          scrollBehavior: 'smooth'
        }}
      >
        <SimpleMessages messages={filteredMessages} />
        {/* 滚动锚点 - 增加底部间距 */}
        <div ref={messagesEndRef} className="h-6" />

        {/* 滚动到底部按钮已移除 */}
      </div>

      {/* Input Area - 固定在底部 */}
      <div className="flex-shrink-0 p-4 border-t border-slate-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 sticky bottom-0">
        <MultimodalInput
          chatId={chatId}
          input={input}
          setInput={setInput}
          handleSubmit={handleSubmit}
          status={status}
          stop={stop}
          attachments={attachments}
          setAttachments={setAttachments}
          messages={filteredMessages}
          setMessages={setMessages}
          append={append}
          selectedVisibilityType="private"
          hideSuggestedActions={true}
        />
      </div>

      {/* Data Stream Handler - 移除以避免URL跳转 */}
      {/* <DataStreamHandler id={chatId} /> */}
    </div>
  );
}
