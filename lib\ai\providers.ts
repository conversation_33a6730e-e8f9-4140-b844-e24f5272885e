import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { xai } from '@ai-sdk/xai';
import { isTestEnvironment } from '../constants';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { qwenModel, qwenMaxModel } from './models.qwen';

// 强制使用测试环境的模型，不使用xAI
const useTestModels = true;

export const myProvider = useTestModels || isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
        'qwen3': qwenModel, // 使用专门的qwenModel
        'qwen-max': qwenMaxModel, // 保留向后兼容性
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': xai('grok-2-vision-1212'),
        'chat-model-reasoning': wrapLanguageModel({
          model: xai('grok-3-mini-beta'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': xai('grok-2-1212'),
        'artifact-model': xai('grok-2-1212'),
        'qwen3': qwenModel, // 使用qwen3模型
        'qwen-max': qwenMaxModel, // 保留向后兼容性
      },
      imageModels: {
        'small-model': xai.image('grok-2-image'),
      },
    });
