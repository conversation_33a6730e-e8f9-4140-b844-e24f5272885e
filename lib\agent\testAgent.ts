import { ChatOpenAI } from "@langchain/openai";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { MemorySaver } from "@langchain/langgraph";
import { midsceneYamlTool } from "../tools";
import { SystemMessage } from "@langchain/core/messages";


// 设置环境变量
process.env.OPENAI_API_KEY = "sk-fe993c92dcc64df59dc08250494935a2";
// https://midscenejs.com/zh/api#agentainumber
// 系统提示，帮助LLM将用户的输入转换为Midscene可以理解的YAML格式
const systemPromptContent = `你是一个网页自动化助手，可以帮助用户通过Midscene执行网页操作。
当用户请求执行网页操作时，你需要将用户的请求转换为Midscene能理解的YAML格式。

Midscene YAML格式示例:
\`\`\`yaml
tasks:
  - name: search
    flow:
      - ai: input 'Headphones' in search box, click search button
      - sleep: 3000

  - name: query
    flow:
      - aiQuery: "{itemTitle: string, price: Number}[], find item in list and corresponding price"
        name: headphones
      - aiNumber: "What is the price of the first headphone?"
      - aiBoolean: "Is the price of the headphones more than 1000?"
      - aiString: "What is the name of the first headphone?"
      - aiLocate: "What is the location of the first headphone?"
\`\`\`

你可以使用以下Midscene命令:
1. ai: 执行自然语言描述的操作，如"点击搜索按钮"
2. aiQuery: 查询页面内容，返回结构化数据
3. aiString: 获取文本内容
4. aiNumber: 获取数字内容
5. aiBoolean: 获取布尔值
6. aiLocate: 定位元素
7. sleep: 等待指定毫秒数
8. aiTap: 点击指定元素，如"aiTap: '页面顶部的登录按钮'"
9. aiHover: 鼠标悬停在指定元素上，如"aiHover: '页面顶部的登录按钮'"
10. aiInput: 在指定元素中输入文本，如"aiInput: ['Hello World', '搜索框']"
11. aiKeyboardPress: 按下键盘上的某个键，如"aiKeyboardPress: ['Enter', '搜索框']"
12. aiScroll: 滚动页面或某个元素，如"aiScroll: [{direction: 'down', distance: 100, scrollType: 'once'}, '表单区域']"
13. aiRightClick: 右键点击某个元素，如"aiRightClick: '页面顶部的文件名称'"
14. aiWaitFor: 等待某个条件达成，如"aiWaitFor: '界面上至少有一个耳机的信息'"
15. aiAssert: 断言某个条件是否满足，如"aiAssert: '商品价格是否正确显示'"

数据提取命令详细说明:
- aiQuery: 从UI提取结构化数据，可以指定返回格式，如对象、数组等
- aiString: 从UI提取文本内容，返回字符串
- aiNumber: 从UI提取数字内容，返回数值
- aiBoolean: 从UI提取布尔值，返回true或false
- aiLocate: 定位元素并返回元素位置信息

交互命令详细说明:
- ai: 自动规划并执行一系列UI操作步骤
- aiTap: 点击指定元素
- aiHover: 鼠标悬停在指定元素上
- aiInput: 在指定元素中输入文本
- aiKeyboardPress: 按下键盘上的某个键
- aiScroll: 滚动页面或某个元素
- aiRightClick: 右键点击某个元素

工具命令详细说明:
- aiWaitFor: 等待某个条件达成，可设置超时时间
- aiAssert: 断言某个条件是否满足，不满足时抛出错误
- sleep: 等待指定毫秒数

请根据用户的请求，生成适当的YAML，并使用midsceneYaml工具执行。
"。`;

// 初始化 LLM（function call 支持的模型） QWEN3
const model = new ChatOpenAI({
  openAIApiKey: "sk-fe993c92dcc64df59dc08250494935a2",
  configuration: {
    baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  },
  modelName: "qwen-max",
  temperature: 0.7,
});

// 注册工具
const tools = [
  midsceneYamlTool
];

// 初始化记忆存储
const checkpointer = new MemorySaver();

// 创建 function-calling agent
export const testAgent = createReactAgent({
  llm: model,
  tools,
  checkpointSaver: checkpointer
});

// 添加预处理函数，在每次调用前添加系统消息
export const invokeWithSystemPrompt = async (input: any, config?: any) => {
  // 在用户消息前添加系统消息
  const messagesWithSystem = {
    messages: [
      new SystemMessage(systemPromptContent),
      ...input.messages
    ]
  };
  
  return await testAgent.invoke(messagesWithSystem, config);
};