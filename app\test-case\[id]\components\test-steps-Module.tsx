'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  Target, 
  CheckCircle, 
  Plus,
  Edit3,
  Save,
  X,
  Trash2,
  FileText,
  Bot,
  User
} from 'lucide-react';
import { ModuleProps, TestStep } from '../types';

export default function TestStepsModule({ 
  testCase, 
  isEditing, 
  onUpdate,
  onAIGenerate 
}: ModuleProps) {
  const [editingStepId, setEditingStepId] = useState<string | null>(null);
  const [newStep, setNewStep] = useState({ action: '', expected: '' });

  const getStepTypeIcon = (type?: string) => {
    switch (type) {
      case 'automated':
        return <Bot className="w-4 h-4 text-purple-600" title="Automated Step" />;
      case 'optional':
        return <Target className="w-4 h-4 text-yellow-600" title="Optional Step" />;
      case 'manual':
      default:
        return <User className="w-4 h-4 text-blue-600" title="Manual Step" />;
    }
  };

  const getStepTypeColor = (type?: string) => {
    switch (type) {
      case 'automated':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'optional':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'manual':
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  const handleAddStep = () => {
    if (!newStep.action.trim() || !newStep.expected.trim()) return;
    
    const newStepObj: TestStep = {
      id: `step-${Date.now()}`,
      step: testCase.steps.length + 1,
      action: newStep.action.trim(),
      expected: newStep.expected.trim(),
      type: 'manual'
    };
    
    onUpdate({
      steps: [...testCase.steps, newStepObj]
    });
    
    setNewStep({ action: '', expected: '' });
  };

  const handleDeleteStep = (stepId: string) => {
    const updatedSteps = testCase.steps
      .filter(step => step.id !== stepId)
      .map((step, index) => ({
        ...step,
        step: index + 1
      }));
    
    onUpdate({ steps: updatedSteps });
  };

  const handleEditStep = (stepId: string, field: keyof TestStep, value: string) => {
    const updatedSteps = testCase.steps.map(step => 
      step.id === stepId ? { ...step, [field]: value } : step
    );
    
    onUpdate({ steps: updatedSteps });
  };

  const handleStepTypeChange = (stepId: string, type: TestStep['type']) => {
    const updatedSteps = testCase.steps.map(step => 
      step.id === stepId ? { ...step, type } : step
    );
    
    onUpdate({ steps: updatedSteps });
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5 text-indigo-600" />
            <h2 className="text-xl font-semibold">Test Steps</h2>
            <Badge variant="outline" className="ml-2">
              {testCase.steps.length} steps
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={onAIGenerate}
              className="border-purple-200 hover:bg-purple-50"
            >
              <Bot className="w-4 h-4 mr-2" />
              AI Generate Steps
            </Button>
          </div>
        </div>

        {/* Add New Step Form */}
        <div className="mb-6 p-4 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg">
          <h4 className="font-medium text-indigo-800 dark:text-indigo-200 mb-3 flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add New Step
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Action
              </label>
              <Textarea
                value={newStep.action}
                onChange={(e) => setNewStep(prev => ({ ...prev, action: e.target.value }))}
                placeholder="Describe what action to perform..."
                className="min-h-[80px] border-slate-200 dark:border-slate-700"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Expected Result
              </label>
              <Textarea
                value={newStep.expected}
                onChange={(e) => setNewStep(prev => ({ ...prev, expected: e.target.value }))}
                placeholder="Describe the expected outcome..."
                className="min-h-[80px] border-slate-200 dark:border-slate-700"
              />
            </div>
          </div>
          <Button 
            onClick={handleAddStep}
            disabled={!newStep.action.trim() || !newStep.expected.trim()}
            className="bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Step
          </Button>
        </div>

        {/* Steps List */}
        <div className="space-y-4">
          {testCase.steps.length === 0 ? (
            <div className="text-center py-12">
              <Target className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
                No test steps yet
              </h3>
              <p className="text-slate-500 dark:text-slate-500 mb-4">
                Add your first test step to get started
              </p>
              <Button 
                onClick={onAIGenerate}
                variant="outline"
                className="border-purple-200 hover:bg-purple-50"
              >
                <Bot className="w-4 h-4 mr-2" />
                Generate Steps with AI
              </Button>
            </div>
          ) : (
            testCase.steps.map((step) => (
              <div
                key={step.id}
                className="flex items-start gap-4 p-5 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 hover:bg-slate-100 dark:hover:bg-slate-800/70 transition-all duration-200 hover:shadow-sm"
              >
                <div className="flex items-center gap-3 min-w-0">
                  <span className="w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/30 dark:to-indigo-800/30 rounded-full flex items-center justify-center text-sm font-semibold text-indigo-600 dark:text-indigo-400 shadow-sm">
                    {step.step}
                  </span>
                  
                  <div className="flex items-center">
                    {getStepTypeIcon(step.type)}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 flex items-center gap-2">
                        <Target className="w-4 h-4 text-indigo-600" />
                        Action
                      </h4>
                      {editingStepId === step.id ? (
                        <Textarea
                          value={step.action}
                          onChange={(e) => handleEditStep(step.id, 'action', e.target.value)}
                          className="min-h-[60px] border-slate-200 dark:border-slate-700"
                        />
                      ) : (
                        <p 
                          className="text-slate-600 dark:text-slate-400 leading-relaxed cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 p-2 rounded transition-colors"
                          onClick={() => setEditingStepId(step.id)}
                        >
                          {step.action}
                        </p>
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        Expected Result
                      </h4>
                      {editingStepId === step.id ? (
                        <Textarea
                          value={step.expected}
                          onChange={(e) => handleEditStep(step.id, 'expected', e.target.value)}
                          className="min-h-[60px] border-slate-200 dark:border-slate-700"
                        />
                      ) : (
                        <p 
                          className="text-slate-600 dark:text-slate-400 leading-relaxed cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 p-2 rounded transition-colors"
                          onClick={() => setEditingStepId(step.id)}
                        >
                          {step.expected}
                        </p>
                      )}
                    </div>
                    
                    {/* Step Notes */}
                    {(step.notes || editingStepId === step.id) && (
                      <div>
                        <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 flex items-center gap-2">
                          <FileText className="w-4 h-4 text-gray-600" />
                          Notes
                        </h4>
                        {editingStepId === step.id ? (
                          <Textarea
                            value={step.notes || ''}
                            onChange={(e) => handleEditStep(step.id, 'notes', e.target.value)}
                            className="min-h-[40px] border-slate-200 dark:border-slate-700"
                            placeholder="Optional notes for this step..."
                          />
                        ) : step.notes ? (
                          <p 
                            className="text-slate-500 dark:text-slate-500 text-sm leading-relaxed cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 p-2 rounded transition-colors italic"
                            onClick={() => setEditingStepId(step.id)}
                          >
                            {step.notes}
                          </p>
                        ) : null}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  {/* Step Type Badge and Selector */}
                  <div className="flex flex-col items-end gap-2">
                    {editingStepId === step.id ? (
                      <select
                        value={step.type || 'manual'}
                        onChange={(e) => handleStepTypeChange(step.id, e.target.value as TestStep['type'])}
                        className="text-xs border border-slate-200 dark:border-slate-700 rounded px-2 py-1 bg-white dark:bg-slate-800"
                      >
                        <option value="manual">Manual</option>
                        <option value="automated">Automated</option>
                        <option value="optional">Optional</option>
                      </select>
                    ) : (
                      <Badge className={getStepTypeColor(step.type)}>
                        {step.type || 'manual'}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex gap-1">
                    {editingStepId === step.id ? (
                      <>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => setEditingStepId(null)}
                          className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          title="Save changes"
                        >
                          <Save className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => setEditingStepId(null)}
                          className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                          title="Cancel editing"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </>
                    ) : (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => setEditingStepId(step.id)}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        title="Edit step"
                      >
                        <Edit3 className="w-4 h-4" />
                      </Button>
                    )}
                    
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleDeleteStep(step.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="Delete step"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
