'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Bot, 
  GitBranch,
  Terminal,
  Settings,
  ExternalLink
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function AutomationModule({ 
  testCase, 
  isEditing, 
  onUpdate,
  onAIGenerate 
}: ModuleProps) {
  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-purple-200 dark:border-purple-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-purple-600" />
            <h2 className="text-xl font-semibold">Automation Configuration</h2>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={onAIGenerate}
              className="border-purple-200 hover:bg-purple-50"
            >
              <Bot className="w-4 h-4 mr-2" />
              AI Configure
            </Button>
          </div>
        </div>

        {testCase.automation ? (
          <div className="space-y-6">
            {/* Repository Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <GitBranch className="w-4 h-4 text-slate-600" />
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Repository</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-slate-800 dark:text-slate-200 font-mono text-sm">
                    {testCase.automation.repository}
                  </span>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <GitBranch className="w-4 h-4 text-slate-600" />
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Branch</span>
                </div>
                <Badge variant="outline" className="font-mono">
                  {testCase.automation.branch}
                </Badge>
              </div>
            </div>

            {/* Commands */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Terminal className="w-4 h-4 text-slate-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Commands</span>
              </div>
              <div className="bg-slate-900 dark:bg-slate-800 rounded-lg p-4">
                {testCase.automation.commands.map((command, index) => (
                  <div key={index} className="text-green-400 font-mono text-sm">
                    $ {command}
                  </div>
                ))}
              </div>
            </div>

            {/* Parameters */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Settings className="w-4 h-4 text-slate-600" />
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Parameters</span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(testCase.automation.parameters).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                    <span className="font-mono text-sm text-slate-600 dark:text-slate-400">{key}</span>
                    <span className="font-mono text-sm text-slate-800 dark:text-slate-200">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <Bot className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
              No automation configured
            </h3>
            <p className="text-slate-500 dark:text-slate-500 mb-4">
              Set up automation configuration to run this test automatically
            </p>
            <Button 
              onClick={onAIGenerate}
              variant="outline"
              className="border-purple-200 hover:bg-purple-50"
            >
              <Bot className="w-4 h-4 mr-2" />
              Configure with AI
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
