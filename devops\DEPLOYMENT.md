# Docker 镜像导出和服务器部署指南

## 步骤 1: 在开发机器上导出镜像

### 方法一：使用 Dockerfile 直接构建
```bash
# 进入项目根目录
cd /path/to/ai-run-nextjs

# 给脚本执行权限
chmod +x devops/export-image.sh

# 运行导出脚本
./devops/export-image.sh
```

### 方法二：使用 docker-compose 构建
```bash
# 进入项目根目录
cd /path/to/ai-run-nextjs

# 给脚本执行权限
chmod +x devops/export-compose.sh

# 运行导出脚本
./devops/export-compose.sh
```

这将生成 `ai-run-nextjs.tar` 文件。

## 步骤 2: 传输镜像文件到服务器

### 使用 scp 传输
```bash
scp ai-run-nextjs.tar user@your-server:/tmp/
```

### 使用 rsync 传输
```bash
rsync -avz ai-run-nextjs.tar user@your-server:/tmp/
```

## 步骤 3: 在服务器上部署

### 方法一：完整部署（包含数据库）
```bash
# 在服务器上
cd /tmp
chmod +x devops/deploy-server.sh
./devops/deploy-server.sh
```

### 方法二：简化部署（仅应用）
```bash
# 在服务器上
cd /tmp
chmod +x devops/deploy-simple.sh
./devops/deploy-simple.sh
```

## 步骤 4: 验证部署

### 检查服务状态
```bash
cd /opt/ai-run
docker-compose ps
```

### 查看日志
```bash
# 查看应用日志
docker-compose logs -f app

# 查看所有服务日志
docker-compose logs -f
```

### 访问应用
打开浏览器访问：`http://your-server-ip:3000`

## 环境变量配置

### 生产环境配置
编辑 `/opt/ai-run/docker-compose.yml` 文件，修改以下环境变量：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  - DB_PROVIDER=sqlite
  - DATABASE_URL=file:./data.db
  - NEXTAUTH_URL=http://your-domain.com  # 修改为你的域名
  - NEXTAUTH_SECRET=your-secret-key-here  # 修改为强密码
```

### 重要安全配置
1. **修改 NEXTAUTH_SECRET**：使用强密码
2. **修改 NEXTAUTH_URL**：使用你的实际域名
3. **配置防火墙**：只开放必要端口

## 管理命令

### 启动服务
```bash
cd /opt/ai-run
docker-compose up -d
```

### 停止服务
```bash
cd /opt/ai-run
docker-compose down
```

### 重启服务
```bash
cd /opt/ai-run
docker-compose restart
```

### 更新应用
```bash
# 1. 停止服务
docker-compose down

# 2. 加载新镜像
docker load -i /path/to/new-ai-run-nextjs.tar

# 3. 启动服务
docker-compose up -d
```

### 备份数据
```bash
# 备份 SQLite 数据库
cp /opt/ai-run/data/data.db /backup/data.db.$(date +%Y%m%d_%H%M%S)

# 备份日志
tar -czf /backup/logs.$(date +%Y%m%d_%H%M%S).tar.gz /opt/ai-run/logs/
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 3000
   
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "3001:3000"  # 改为其他端口
   ```

2. **权限问题**
   ```bash
   # 修复目录权限
   sudo chown -R 1000:1000 /opt/ai-run/data
   sudo chown -R 1000:1000 /opt/ai-run/logs
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   
   # 限制容器内存
   # 在 docker-compose.yml 中添加：
   deploy:
     resources:
       limits:
         memory: 1G
   ```

4. **磁盘空间不足**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   
   # 清理日志
   docker-compose logs --tail=1000 > /dev/null
   ```

## 性能优化

### 生产环境建议
1. **使用 Nginx 反向代理**
2. **配置 SSL 证书**
3. **设置日志轮转**
4. **配置监控告警**
5. **使用 CDN 加速静态资源**

### 监控命令
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tlnp
``` 