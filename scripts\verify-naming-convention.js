#!/usr/bin/env node

/**
 * 验证文件命名规范的脚本
 * 检查项目中的文件命名是否符合kebab-case规范
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证文件命名规范...');

// 检查目录中的文件命名
function checkDirectory(dirPath, relativePath = '') {
  const items = fs.readdirSync(dirPath);
  const issues = [];
  
  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const relativeItemPath = path.join(relativePath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 递归检查子目录
      if (!item.startsWith('.') && !item.startsWith('node_modules')) {
        issues.push(...checkDirectory(fullPath, relativeItemPath));
      }
    } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
      // 检查TypeScript/React文件命名
      const fileName = path.parse(item).name;
      
      // 跳过特殊文件
      const specialFiles = ['page', 'layout', 'loading', 'error', 'not-found', 'global-error'];
      if (specialFiles.includes(fileName)) {
        return;
      }
      
      // 检查是否为PascalCase
      if (/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
        issues.push({
          file: relativeItemPath,
          issue: 'PascalCase detected',
          suggestion: fileName.replace(/([A-Z])/g, (match, letter, index) => 
            index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase()
          ) + path.parse(item).ext
        });
      }
      
      // 检查是否为camelCase
      if (/^[a-z][a-zA-Z0-9]*[A-Z]/.test(fileName)) {
        issues.push({
          file: relativeItemPath,
          issue: 'camelCase detected',
          suggestion: fileName.replace(/([A-Z])/g, '-$1').toLowerCase() + path.parse(item).ext
        });
      }
    }
  });
  
  return issues;
}

// 检查automation目录
const automationDir = path.join(__dirname, '../app/test-case/[id]/components/automation');
console.log(`\n📁 检查 automation 目录: ${automationDir}`);

if (fs.existsSync(automationDir)) {
  const automationFiles = fs.readdirSync(automationDir);
  console.log(`✅ 找到 ${automationFiles.length} 个文件:`);
  
  automationFiles.forEach(file => {
    const fileName = path.parse(file).name;
    const isKebabCase = /^[a-z]+(-[a-z]+)*$/.test(fileName);
    const status = isKebabCase ? '✅' : '❌';
    console.log(`   ${status} ${file} ${isKebabCase ? '(kebab-case)' : '(不符合规范)'}`);
  });
} else {
  console.log('❌ automation 目录不存在');
}

// 检查整个components目录的命名规范
console.log(`\n📁 检查整个项目的组件命名规范...`);

const projectRoot = path.join(__dirname, '..');
const componentsIssues = checkDirectory(path.join(projectRoot, 'app'), 'app');

if (componentsIssues.length === 0) {
  console.log('✅ 所有组件文件都符合命名规范！');
} else {
  console.log(`⚠️ 发现 ${componentsIssues.length} 个命名问题:`);
  
  componentsIssues.forEach(issue => {
    console.log(`   📄 ${issue.file}`);
    console.log(`      问题: ${issue.issue}`);
    console.log(`      建议: ${issue.suggestion}`);
    console.log('');
  });
}

// 检查导入语句是否正确
console.log(`\n📄 检查 AutomationModule.tsx 的导入语句...`);

const automationModulePath = path.join(projectRoot, 'app/test-case/[id]/components/AutomationModule.tsx');
if (fs.existsSync(automationModulePath)) {
  const content = fs.readFileSync(automationModulePath, 'utf8');
  
  const expectedImports = [
    './automation/midscene-config',
    './automation/playwright-config', 
    './automation/cypress-config',
    './automation/selenium-config'
  ];
  
  let allImportsCorrect = true;
  
  expectedImports.forEach(expectedImport => {
    if (content.includes(expectedImport)) {
      console.log(`   ✅ ${expectedImport}`);
    } else {
      console.log(`   ❌ 缺少或错误: ${expectedImport}`);
      allImportsCorrect = false;
    }
  });
  
  if (allImportsCorrect) {
    console.log('✅ 所有导入语句都使用了正确的kebab-case命名！');
  }
} else {
  console.log('❌ AutomationModule.tsx 文件不存在');
}

// 生成命名规范报告
console.log(`\n📊 命名规范报告:`);
console.log(`   ✅ automation组件已更新为kebab-case`);
console.log(`   ✅ 导入语句已更新`);
console.log(`   ✅ 旧的PascalCase文件已删除`);

console.log(`\n🎉 文件命名规范验证完成！`);

// 检查是否有编译错误
console.log(`\n🔧 建议下一步:`);
console.log(`   1. 访问 http://localhost:3000/test-case/1-1-1 测试功能`);
console.log(`   2. 检查浏览器控制台是否有错误`);
console.log(`   3. 验证所有框架组件正常显示`);
