import type { Metada<PERSON> } from "next";
import "./globals.css";
import { NextAuthSessionProvider } from "@/components/session-provider";
import { LayoutProvider } from "@/components/layout-provider";
import { ThemeProvider } from "@/components/theme-provider";

// 使用CSS变量定义系统字体
const fontVariables = {
  variable: "font-sans font-mono",
};

export const metadata: Metadata = {
  title: "AI Run - Next.js",
  description: "AI-powered application built with Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="antialiased">
        <NextAuthSessionProvider>
          <ThemeProvider>
            <LayoutProvider>
              {children}
            </LayoutProvider>
          </ThemeProvider>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
}
