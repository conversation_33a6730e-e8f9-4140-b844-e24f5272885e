/**
 * 工具加载器
 * 根据意图动态加载不同的工具
 */

import { PromptType } from './prompts';
import { midsceneYamlTool } from '../tools';
import { generateUUID } from '@/lib/utils';
import { tool } from "@langchain/core/tools";
import { z } from "zod";

// 定义createDocument工具的schema
const createDocumentSchema = z.object({
  title: z.string().describe("文档标题"),
  kind: z.enum(["text", "code", "sheet", "image"]).describe("文档类型"),
  content: z.string().optional().describe("文档内容"),
});

// 定义updateDocument工具的schema
const updateDocumentSchema = z.object({
  id: z.string().describe("要更新的文档ID"),
  content: z.string().describe("更新的内容"),
});

// 使用tool函数创建createDocument工具
const createDocumentTool = tool(
  async ({ title, kind, content }) => {
    try {
      console.log(`执行createDocument工具: 标题=${title}, 类型=${kind}`);
      const id = generateUUID();
      
      // 根据内容类型设置默认内容
      let defaultContent = '';
      if (!content) {
        if (kind === 'text') {
          defaultContent = '正在生成文章内容...';
        } else if (kind === 'code') {
          defaultContent = '// 正在生成代码...';
        } else if (kind === 'sheet') {
          defaultContent = '正在准备表格数据...';
        }
      }
      
      // 返回结果
      return {
        id,
        title,
        kind: kind || 'text', // 确保有默认类型
        content: content || defaultContent
      };
    } catch (error: any) {
      console.error('createDocument工具执行失败:', error);
      return {
        error: `创建文档失败: ${error.message || '未知错误'}`
      };
    }
  },
  {
    name: 'createDocument',
    description: '创建一个新的文档',
    schema: createDocumentSchema
  }
);

// 使用tool函数创建updateDocument工具
const updateDocumentTool = tool(
  async ({ id, content }) => {
    try {
      console.log(`执行updateDocument工具: ID=${id}`);
      return {
        id,
        content,
        message: "文档已更新",
        status: "success"
      };
    } catch (error: any) {
      console.error("updateDocument工具执行失败:", error);
      return {
        error: `文档更新失败: ${error?.message || '未知错误'}`,
        status: "error"
      };
    }
  },
  {
    name: "updateDocument",
    description: "更新现有文档的内容。提供文档ID和新的内容。",
    schema: updateDocumentSchema,
  }
);

// 定义工具类型
type ToolType = typeof createDocumentTool | typeof updateDocumentTool | typeof midsceneYamlTool;

// 基础工具集
const baseTools: ToolType[] = [];

// 自动化测试工具
const automationTools: ToolType[] = [
  midsceneYamlTool
];

// Artifacts工具
const artifactsTools: ToolType[] = [
  createDocumentTool,
  updateDocumentTool
];

// 混合模式工具
const mixedTools: ToolType[] = [
  ...automationTools,
  ...artifactsTools
];

// 工具映射
const toolsByIntent: Record<PromptType, ToolType[]> = {
  automation: automationTools,
  artifacts: artifactsTools,
  mixed: mixedTools
};

/**
 * 根据意图加载工具
 * @param intent 意图类型
 * @returns 工具数组
 */
export function loadToolsByIntent(intent: PromptType): ToolType[] {
  console.log(`为意图 ${intent} 加载工具`);
  const tools = [...baseTools, ...(toolsByIntent[intent] || [])];
  console.log(`已加载工具: ${tools.map(t => t.name).join(', ')}`);
  return tools;
}

// 导出所有工具
export {
  createDocumentTool,
  updateDocumentTool,
  midsceneYamlTool
}; 