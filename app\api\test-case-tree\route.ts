import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';
import { db } from '@/lib/db';
import { folder, testCase } from '@/lib/db/schema';
import { eq, asc, isNull } from 'drizzle-orm';

interface TreeNode {
  id: string;
  name: string;
  children: TreeNode[];
  isFolder: boolean;
  path?: string;
  level?: number;
}

async function buildFolderTree(parentId: string | null = null): Promise<TreeNode[]> {
  // 获取文件夹
  const folders = await db
    .select()
    .from(folder)
    .where(parentId ? eq(folder.parentId, parentId) : isNull(folder.parentId))
    .orderBy(asc(folder.sortOrder), asc(folder.name));

  // 获取测试用例
  const testCases = await db
    .select()
    .from(testCase)
    .where(parentId ? eq(testCase.folderId, parentId) : isNull(testCase.folderId))
    .orderBy(asc(testCase.name));

  const result: TreeNode[] = [];

  // 添加文件夹节点
  for (const folderItem of folders) {
    const children = await buildFolderTree(folderItem.id);
    result.push({
      id: folderItem.id,
      name: folderItem.name,
      children,
      isFolder: true,
      path: folderItem.path,
      level: folderItem.level
    });
  }

  // 添加测试用例节点
  for (const testCaseItem of testCases) {
    result.push({
      id: testCaseItem.id,
      name: testCaseItem.name,
      children: [],
      isFolder: false
    });
  }

  return result;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tree = await buildFolderTree();
    return NextResponse.json(tree);
  } catch (error) {
    console.error('Get test case tree error:', error);
    return NextResponse.json(
      { error: 'Failed to get test case tree' },
      { status: 500 }
    );
  }
}
