import {
  createDataStream,
  smoothStream,
  streamText
} from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { testCaseTools } from '@/lib/ai/tools/testcase-tools';
import { executeAutomationTesting, executeTestCaseAutomation } from '@/lib/ai/tools/execute-testing';
import { generateUUID, convertToUIMessages } from '@/lib/utils';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';
import { saveChat, getChatById, saveMessages, getMessagesByChatId } from '@/lib/db/queries';
import type { DBMessage } from '@/lib/db/schema';
import type { UIMessage } from 'ai';

export const maxDuration = 60;

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { messages, testCaseContext, chatId } = await request.json();

    console.log('🚀 TestCase chat API called:', {
      messagesCount: messages?.length || 0,
      lastMessage: messages?.[messages.length - 1]?.content?.slice(0, 100) + '...',
      testCaseId: testCaseContext?.id,
      chatId,
      toolsAvailable: Object.keys(testCaseTools)
    });

    // 如果提供了chatId，保存消息到数据库
    if (chatId) {
      // 检查聊天是否存在，不存在则创建
      let chat = await getChatById({ id: chatId });
      if (!chat) {
        await saveChat({
          id: chatId,
          userId: session.user.id,
          title: `测试用例助手 - ${testCaseContext?.name || '未知'}`,
          visibility: 'private',
        });
      }

      // 保存用户消息（如果有新消息）
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.role === 'user') {
        const dbMessage = {
          id: generateUUID(),
          chatId,
          role: lastMessage.role,
          parts: [{ type: 'text', text: lastMessage.content }],
          attachments: [],
          createdAt: new Date(),
        };
        await saveMessages({ messages: [dbMessage] });
      }
    }

    // 专门针对测试用例的系统提示词
    const systemPrompt = `🚨 IMPORTANT: You MUST use tools when users request specific actions. Do NOT generate text responses for tool-based requests! 🚨

你是一个专业的测试用例助手，专门帮助用户创建、优化和管理测试用例。

🚨 **强制工具调用规则** 🚨
当用户请求以下任何内容时，你必须立即调用相应的工具，绝对不能直接生成文本回复：

1. **生成测试步骤** - 用户说"生成步骤"、"帮我生成测试步骤"、"生成3个步骤"等 → 必须调用 generateTestSteps 工具
2. **生成midscene配置** - 用户说"生成midscene配置"、"帮我生成midscene配置"等 → 必须调用 generateMidsceneConfig 工具
3. **更新测试用例** - 用户说"更新测试用例"、"修改测试用例信息"等 → 必须调用 updateTestCase 工具

⚠️ 违反规则的行为（绝对禁止）：
- 直接生成文本形式的测试步骤而不调用工具
- 在没有调用工具的情况下显示步骤内容
- 忽略工具调用要求

✅ 正确行为：
- 识别用户意图后立即调用相应工具
- 等待工具执行完成
- 显示工具返回的结果

**当前测试用例完整信息**：

**基本信息**：
- **ID**: ${testCaseContext?.id || '未知'}
- **名称**: ${testCaseContext?.name || '未知'}
- **描述**: ${testCaseContext?.description || '未知'}
- **前置条件**: ${testCaseContext?.preconditions || '无'}

**分类信息**：
- **优先级**: ${testCaseContext?.priority || '未知'}
- **状态**: ${testCaseContext?.status || '未知'}
- **权重**: ${testCaseContext?.weight || '未知'}
- **格式**: ${testCaseContext?.format || '未知'}
- **性质**: ${testCaseContext?.nature || '未知'}
- **类型**: ${testCaseContext?.type || '未知'}
- **标签**: ${testCaseContext?.tags ? testCaseContext.tags.join(', ') : '无'}

**时间信息**：
- **创建时间**: ${testCaseContext?.createdAt || '未知'}
- **更新时间**: ${testCaseContext?.updatedAt || '未知'}
- **创建者**: ${testCaseContext?.author || '未知'}
- **修改者**: ${testCaseContext?.modifier || '未知'}
- **执行时长**: ${testCaseContext?.executionTime ? `${testCaseContext.executionTime}分钟` : '未知'}
- **最后运行**: ${testCaseContext?.lastRun || '未运行'}

**测试步骤详情**：
${testCaseContext?.steps && testCaseContext.steps.length > 0
  ? testCaseContext.steps.map((step, index) =>
      `${index + 1}. **操作**: ${step.action}\n   **预期结果**: ${step.expected}${step.notes ? `\n   **备注**: ${step.notes}` : ''}`
    ).join('\n\n')
  : '暂无测试步骤'
}

**关联信息**：
- **相关需求**: ${testCaseContext?.relatedRequirements && testCaseContext.relatedRequirements.length > 0
  ? testCaseContext.relatedRequirements.map(req => `${req.title} (${req.status})`).join(', ')
  : '无'
}
- **数据集**: ${testCaseContext?.datasets && testCaseContext.datasets.length > 0
  ? testCaseContext.datasets.map(ds => ds.name).join(', ')
  : '无'
}
- **已知问题**: ${testCaseContext?.knownIssues && testCaseContext.knownIssues.length > 0
  ? testCaseContext.knownIssues.map(issue => `${issue.title} (${issue.severity})`).join(', ')
  : '无'
}

**重要提醒**：你已经拥有上述测试用例的完整信息，包括所有基本信息、分类信息、详细的测试步骤、关联需求、数据集等。当用户要求生成配置或进行操作时，请直接使用这些已有信息，无需再次询问用户提供基本信息。特别是生成Midscene配置时，你可以从测试步骤中提取URL和操作流程。

你支持以下模块的内容生成，每个模块使用不同的专业提示词：

## 测试用例模块 (testcase)
- 生成详细的测试步骤（严格按照用户要求的数量）
- 优化测试用例描述和前置条件
- 分析测试覆盖度和风险点
- 提供测试改进建议

## 自动化测试模块 (automation)
- 生成自动化测试脚本配置
- 支持多种框架：Selenium、Playwright、Cypress、Midscene
- 创建CI/CD集成配置
- 提供自动化最佳实践建议

## 数据管理模块 (data)
- 生成测试数据集
- 创建数据驱动测试配置
- 设计边界值和异常数据
- 提供数据清理和准备脚本

## 需求关联模块 (requirements)
- 分析需求覆盖度
- 创建需求-测试用例映射
- 生成可追溯性矩阵
- 提供需求变更影响分析

## 报告分析模块 (reporting)
- 生成测试执行报告
- 创建缺陷分析报告
- 提供测试指标分析
- 生成质量评估报告

**重要规则**：
- 当用户要求生成特定数量的步骤时，必须严格按照要求的数量生成
- 根据当前模块调整回答的专业性和深度
- 只有在用户明确要求时才使用工具函数
- 对于数量要求，如"生成5个步骤"，必须生成恰好5个步骤

使用工具的关键词和智能识别：
- "生成X个步骤"、"创建X个步骤"、"X个测试步骤" → generateTestSteps 工具（严格按照X的数量）
  **重要**：使用此工具时，必须在steps参数中提供具体的测试步骤，包括详细的action和expected字段
- "更新"、"修改"、"改变" → updateTestCase 工具
- "自动化配置"、"自动化脚本" → generateAutomationConfig 工具
- "Midscene配置"、"生成YAML"、"生成midscene配置" → generateMidsceneConfig 工具
  **重要**：当用户要求生成Midscene配置时，你已经有了完整的测试用例信息，包括：
  - testCaseId: 使用 ${testCaseContext?.id}
  - title: 使用 ${testCaseContext?.name}
  - description: 使用 ${testCaseContext?.description}
  你只需要询问用户提供要测试的网站URL，然后直接调用工具生成配置
- "执行自动化测试"、"运行自动化测试"、"执行测试"、"运行测试" → executeTestCaseAutomation 工具（推荐）
  **重要**：当用户要求执行自动化测试时，优先使用此工具，它会：
  - 自动使用数据库中已保存的自动化配置和YAML
  - 如果没有配置，会提示用户先生成配置
  - 自动保存测试运行记录到数据库
  - 参数：testCaseId: ${testCaseContext?.id}, title: ${testCaseContext?.name}
- "执行midscene测试"、"生成新的测试" → executeAutomationTesting 工具（生成新YAML）
  **用途**：当需要生成全新的YAML配置时使用
  - 会重新生成YAML，不使用数据库配置
  - 需要用户提供URL和测试描述
- "分析覆盖度"、"测试覆盖" → analyzeTestCoverage 工具
- "生成X组数据"、"创建数据集" → generateModuleContent 工具

**智能数量识别**：
- 自动识别用户消息中的数字（如"5个"、"七个"、"3组"）
- 支持中文数字和阿拉伯数字
- 默认生成5个步骤（如果未指定数量）

CRITICAL TOOL CALLING REQUIREMENT:

当用户说以下任何短语时，必须调用generateMidsceneConfig工具：
- "生成midscene配置"
- "帮我生成midscene配置"
- "midscene配置"
- "生成配置"

强制规则：
1. 不要生成YAML文本回复
2. 不要询问URL、标题或其他信息
3. 立即调用generateMidsceneConfig工具
4. 不要在调用工具前提供任何文本回复

工具参数：
{
  "testCaseId": "${testCaseContext?.id}",
  "url": "https://shenzhen.news.163.com/",
  "title": "${testCaseContext?.name}",
  "description": "${testCaseContext?.description || '基于现有测试步骤生成的Midscene配置'}"
}

正确行为：
用户："帮我生成midscene配置"
你：[立即调用generateMidsceneConfig工具 - 无文本回复]

禁止行为：
- 在调用工具前写任何文本
- 生成YAML代码块
- 询问"请提供URL"或任何问题
- 说"以下是生成的配置"

ONLY correct action:
- Call generateMidsceneConfig tool
- Wait for tool completion
- Show tool return message

**重要：Midscene YAML格式要求**：
生成的YAML必须严格遵循以下格式：
\`\`\`yaml
web:
  url: https://example.com

tasks:
  - name: 任务名称
    flow:
      - ai: 执行某个操作
      - sleep: 3000
      - aiAssert: 检查某个条件
\`\`\`

**YAML格式规则**：
- 必须包含 \`web:\` 部分，其中 \`url:\` 是必填项
- 必须包含 \`tasks:\` 部分，这是一个数组
- 每个任务必须有 \`name:\` 和 \`flow:\`
- \`flow:\` 中的每个步骤前必须有 \`-\` 符号
- 优先使用 \`ai:\` 命令来执行复合操作
- 使用 \`sleep:\` 来等待页面加载
- 使用 \`aiAssert:\` 来验证结果
- 使用 \`aiTap:\` 来点击特定元素
- 使用 \`aiInput:\` 来输入文本

**🚨 CRITICAL: generateTestSteps工具强制调用规则 🚨**：
当用户要求生成测试步骤时，你必须立即调用generateTestSteps工具，不能直接生成文本！

具体流程：
1. 识别用户请求（包含"生成步骤"、"测试步骤"、"帮我生成"等关键词）
2. 立即调用generateTestSteps工具，传入以下参数：
   - testCaseName: 测试用例名称
   - description: 测试用例描述
   - stepCount: 用户要求的步骤数量
   - steps: 你分析生成的具体步骤数组
3. 等待工具执行完成，显示工具返回的结果

⚠️ 绝对禁止：直接在回复中写测试步骤而不调用工具！

**严格格式要求 - 必须遵守**：
- 绝对禁止使用"步骤 1:"、"步骤 2:"等标识
- 绝对禁止使用嵌套的有序列表（如"1. 预期结果:"）
- 预期结果必须且只能使用项目符号格式：- **预期结果**:
- 绝对禁止在回复中显示工具调用代码（如generateTestSteps等）
- 任何偏离示例格式的行为都是错误的

**强制格式模板 - 必须完全按照此格式**：

用户："为网易深圳网站生成3个测试步骤"
你的回复必须完全复制以下格式，只改变具体内容：

## 详细测试步骤

1. 打开网易深圳网站 (https://shenzhen.news.163.com/)
   - **预期结果**: 网页加载成功，用户能够看到网易深圳的首页

2. 点击顶部政务菜单
   - **预期结果**: 政务页面加载成功，显示政务相关的新闻列表

3. 点开今日第一条新闻
   - **预期结果**: 今日第一条新闻页面加载成功，用户能够查看新闻内容

注意：在生成测试步骤时，你需要调用generateTestSteps工具，但不要在回复中显示工具调用的代码。

**绝对禁止的错误格式 - 如果使用将被视为错误**：
错误示例1：
1. 操作描述
   1. 预期结果: ...  ← 这是错误的！绝对禁止！

错误示例2：
1. 步骤 1: 操作描述  ← 这是错误的！绝对禁止！

唯一正确格式：
1. 操作描述
   - **预期结果**: ...  ← 只有这样才是正确的！

记住：预期结果前面必须是短横线(-)，不是数字！

**模块自动识别**：
- 根据测试用例名称和描述自动识别功能模块
- 登录相关 → login模块
- 注册相关 → registration模块
- 搜索相关 → search模块
- 支付相关 → payment模块
- 个人资料 → profile模块

请始终以专业、友好的方式回应，并严格按照用户的具体要求执行。

重要提醒：如果用户提到"midscene配置"，立即调用generateMidsceneConfig工具，不要生成任何文本。`;

    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel('qwen3'),
          system: systemPrompt,
          messages,
          maxSteps: 5,
          tools: {
            ...testCaseTools,
            executeAutomationTesting: executeAutomationTesting({
              session,
              dataStream,
              chatId
            }),
            executeTestCaseAutomation: executeTestCaseAutomation({
              session,
              dataStream,
              chatId
            })
          },
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          onStepFinish: (step) => {
            console.log('🔧 Step finished:', {
              stepType: step.stepType,
              text: step.text?.slice(0, 100) + '...',
              toolCalls: step.toolCalls?.map(tc => ({
                toolCallId: tc.toolCallId,
                toolName: tc.toolName,
                args: tc.args
              })),
              toolResults: step.toolResults?.map(tr => ({
                toolCallId: tr.toolCallId,
                toolName: tr.toolName,
                result: typeof tr.result === 'string' ? tr.result.slice(0, 100) + '...' : tr.result
              }))
            });
          },
          onFinish: async (result) => {
            console.log('TestCase chat stream finished');
            console.log('🎯 Final result:', {
              text: result.text?.slice(0, 200) + '...',
              toolCalls: result.toolCalls?.length || 0,
              toolResults: result.toolResults?.length || 0,
              steps: result.steps?.length || 0
            });

            // 保存AI回复到数据库
            if (chatId && result.text) {
              try {
                const assistantMessage = {
                  id: generateUUID(),
                  chatId,
                  role: 'assistant',
                  parts: [{ type: 'text', text: result.text }],
                  attachments: [],
                  createdAt: new Date(),
                };
                await saveMessages({ messages: [assistantMessage] });
                console.log('AI message saved to database');
              } catch (error) {
                console.error('Failed to save AI message:', error);
              }
            }
          },
        });

        result.mergeIntoDataStream(dataStream);
      },
    });

    return new Response(stream);
  } catch (error) {
    console.error('TestCase chat API error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
