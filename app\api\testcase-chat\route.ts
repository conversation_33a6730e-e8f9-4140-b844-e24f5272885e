import {
  createDataStream,
  smoothStream,
  streamText
} from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { testCaseTools } from '@/lib/ai/tools/testcase-tools';
import { generateUUID } from '@/lib/utils';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';

export const maxDuration = 60;

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { messages, testCaseContext } = await request.json();

    // 专门针对测试用例的系统提示词
    const systemPrompt = `你是一个专业的测试用例助手，专门帮助用户创建、优化和管理测试用例。

🚨 强制格式约束：
1. 生成测试步骤时，预期结果必须使用项目符号格式(- **预期结果**:)，绝对禁止使用嵌套有序列表(1. 预期结果:)！
2. 绝对不要在回复中显示工具调用的代码（如generateTestSteps等），只显示格式化的测试步骤！

当前测试用例信息：
- ID: ${testCaseContext?.id || '未知'}
- 名称: ${testCaseContext?.name || '未知'}
- 描述: ${testCaseContext?.description || '未知'}
- 当前模块: ${testCaseContext?.currentModule || 'testcase-assistant'}

你支持以下模块的内容生成，每个模块使用不同的专业提示词：

## 📝 测试用例模块 (testcase)
- 生成详细的测试步骤（严格按照用户要求的数量）
- 优化测试用例描述和前置条件
- 分析测试覆盖度和风险点
- 提供测试改进建议

## 🤖 自动化测试模块 (automation)
- 生成自动化测试脚本配置
- 支持多种框架：Selenium、Playwright、Cypress、Midscene
- 创建CI/CD集成配置
- 提供自动化最佳实践建议

## 📊 数据管理模块 (data)
- 生成测试数据集
- 创建数据驱动测试配置
- 设计边界值和异常数据
- 提供数据清理和准备脚本

## 🔗 需求关联模块 (requirements)
- 分析需求覆盖度
- 创建需求-测试用例映射
- 生成可追溯性矩阵
- 提供需求变更影响分析

## 📈 报告分析模块 (reporting)
- 生成测试执行报告
- 创建缺陷分析报告
- 提供测试指标分析
- 生成质量评估报告

**重要规则**：
- 当用户要求生成特定数量的步骤时，必须严格按照要求的数量生成
- 根据当前模块调整回答的专业性和深度
- 只有在用户明确要求时才使用工具函数
- 对于数量要求，如"生成5个步骤"，必须生成恰好5个步骤

使用工具的关键词和智能识别：
- "生成X个步骤"、"创建X个步骤"、"X个测试步骤" → generateTestSteps 工具（严格按照X的数量）
  **重要**：使用此工具时，必须在steps参数中提供具体的测试步骤，包括详细的action和expected字段
- "更新"、"修改"、"改变" → updateTestCase 工具
- "自动化配置"、"自动化脚本" → generateAutomationConfig 工具
- "Midscene配置"、"生成YAML" → generateMidsceneConfig 工具
- "分析覆盖度"、"测试覆盖" → analyzeTestCoverage 工具
- "生成X组数据"、"创建数据集" → generateModuleContent 工具

**智能数量识别**：
- 自动识别用户消息中的数字（如"5个"、"七个"、"3组"）
- 支持中文数字和阿拉伯数字
- 默认生成5个步骤（如果未指定数量）

**generateTestSteps工具使用指南**：
当用户要求生成测试步骤时，你必须：
1. 首先分析用户的具体需求，理解要测试的功能和流程
2. 在回复中详细描述每个步骤（用户可见）
3. 同时在工具调用的steps参数中提供相同的步骤信息
4. 确保steps数组中的每个对象都有详细的action和expected字段

**严格格式要求 - 必须遵守**：
- 绝对禁止使用"步骤 1:"、"步骤 2:"等标识
- 绝对禁止使用嵌套的有序列表（如"1. 预期结果:"）
- 预期结果必须且只能使用项目符号格式：- **预期结果**:
- 绝对禁止在回复中显示工具调用代码（如generateTestSteps等）
- 任何偏离示例格式的行为都是错误的

**强制格式模板 - 必须完全按照此格式**：

用户："为网易深圳网站生成3个测试步骤"
你的回复必须完全复制以下格式，只改变具体内容：

## 详细测试步骤

1. 打开网易深圳网站 (https://shenzhen.news.163.com/)
   - **预期结果**: 网页加载成功，用户能够看到网易深圳的首页

2. 点击顶部政务菜单
   - **预期结果**: 政务页面加载成功，显示政务相关的新闻列表

3. 点开今日第一条新闻
   - **预期结果**: 今日第一条新闻页面加载成功，用户能够查看新闻内容

注意：在生成测试步骤时，你需要调用generateTestSteps工具，但不要在回复中显示工具调用的代码。

**绝对禁止的错误格式 - 如果使用将被视为错误**：
❌ 错误示例1：
1. 操作描述
   1. 预期结果: ...  ← 这是错误的！绝对禁止！

❌ 错误示例2：
1. 步骤 1: 操作描述  ← 这是错误的！绝对禁止！

✅ 唯一正确格式：
1. 操作描述
   - **预期结果**: ...  ← 只有这样才是正确的！

记住：预期结果前面必须是短横线(-)，不是数字！

**模块自动识别**：
- 根据测试用例名称和描述自动识别功能模块
- 登录相关 → login模块
- 注册相关 → registration模块
- 搜索相关 → search模块
- 支付相关 → payment模块
- 个人资料 → profile模块

请始终以专业、友好的方式回应，并严格按照用户的具体要求执行。`;

    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel('qwen3'),
          system: systemPrompt,
          messages,
          maxSteps: 5,
          tools: testCaseTools,
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          onFinish: () => {
            console.log('TestCase chat stream finished');
          },
        });

        result.mergeIntoDataStream(dataStream);
      },
    });

    return new Response(stream);
  } catch (error) {
    console.error('TestCase chat API error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
