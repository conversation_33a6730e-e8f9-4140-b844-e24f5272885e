'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  Info, 
  CheckCircle, 
  Plus,
  Calendar,
  User,
  Weight,
  Tag,
  Activity
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function InformationModule({ 
  testCase, 
  isEditing, 
  onUpdate 
}: ModuleProps) {
  
  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'work-in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'deprecated': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'draft': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return colors[status as keyof typeof colors] || colors.draft;
  };

  const getWeightColor = (weight: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    };
    return colors[weight as keyof typeof colors] || colors.medium;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-blue-200 dark:border-blue-700">
        <div className="flex items-center gap-2 mb-6">
          <Info className="w-5 h-5 text-blue-600" />
          <h2 className="text-xl font-semibold">Basic Information</h2>
        </div>

        {/* Information Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Status</span>
            </div>
            <Badge className={getStatusColor(testCase.status)}>
              {testCase.status.replace('-', ' ').toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Tag className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">ID</span>
            </div>
            <span className="text-slate-800 dark:text-slate-200 font-mono">{testCase.id}</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Weight className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Weight</span>
            </div>
            <Badge className={getWeightColor(testCase.weight)}>
              {testCase.weight.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-2">
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Auto</span>
            <Badge variant="outline">
              {testCase.automation ? 'YES' : 'NO'}
            </Badge>
          </div>

          <div className="space-y-2">
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Format</span>
            <Badge variant="secondary">
              {testCase.format.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-2">
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Nature</span>
            <Badge variant="secondary">
              {testCase.nature.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-2">
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Type</span>
            <Badge variant="secondary">
              {testCase.type.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Author</span>
            </div>
            <span className="text-slate-800 dark:text-slate-200">{testCase.author}</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Created</span>
            </div>
            <span className="text-slate-800 dark:text-slate-200">
              {new Date(testCase.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Tags</span>
          <div className="flex flex-wrap gap-2">
            {testCase.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-200 dark:border-slate-700">
        <h3 className="text-lg font-semibold mb-4">Description</h3>
        {isEditing ? (
          <Textarea
            value={testCase.description}
            onChange={(e) => onUpdate({ description: e.target.value })}
            className="min-h-[120px] border-slate-200 dark:border-slate-700"
            placeholder="Enter test case description..."
          />
        ) : (
          <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
            {testCase.description}
          </p>
        )}
      </div>

      {/* Preconditions */}
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-200 dark:border-slate-700">
        <div className="flex items-center gap-2 mb-4">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold">Preconditions</h3>
        </div>
        {isEditing ? (
          <Textarea
            value={testCase.preconditions || ''}
            onChange={(e) => onUpdate({ preconditions: e.target.value })}
            className="min-h-[100px] border-slate-200 dark:border-slate-700"
            placeholder="Enter preconditions that must be met before executing this test case..."
          />
        ) : testCase.preconditions ? (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
              {testCase.preconditions}
            </p>
          </div>
        ) : (
          <div className="text-center py-6">
            <CheckCircle className="w-12 h-12 text-slate-400 mx-auto mb-3" />
            <p className="text-slate-500 dark:text-slate-400 mb-3">No preconditions specified</p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onUpdate({ preconditions: '' })}
              className="border-green-200 hover:bg-green-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Preconditions
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
