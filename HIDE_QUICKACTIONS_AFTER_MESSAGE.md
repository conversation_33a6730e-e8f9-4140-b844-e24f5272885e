# 用户发送消息后隐藏快捷操作功能

## 🎯 功能概述

实现当用户发送第一条消息后自动隐藏quickActions（快捷操作按钮）的功能，提供更清洁的聊天界面体验。

## 🔧 技术实现

### 1. 状态管理

#### 新增状态
```typescript
const [hasUserSentMessage, setHasUserSentMessage] = useState(false);
```

**作用**: 跟踪用户是否已经发送过消息

### 2. 消息发送监听

#### handleSubmit函数增强
```typescript
const handleSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
  originalHandleSubmit(e);
  // 标记用户已发送消息，隐藏快捷操作
  setHasUserSentMessage(true);
  // 用户发送消息后立即滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 50);
}, [originalHandleSubmit, scrollToBottom]);
```

#### handleQuickAction函数增强
```typescript
const handleQuickAction = useCallback((action: typeof quickActions[0]) => {
  append({
    role: 'user',
    content: action.prompt,
  });
  // 标记用户已发送消息，隐藏快捷操作
  setHasUserSentMessage(true);
}, [append]);
```

### 3. 历史消息检查

#### 检测已有用户消息
```typescript
// 检查是否已有用户消息
useEffect(() => {
  const hasUserMessage = messages.some(message => message.role === 'user');
  if (hasUserMessage && !hasUserSentMessage) {
    setHasUserSentMessage(true);
  }
}, [messages, hasUserSentMessage]);
```

**作用**: 处理页面刷新或重新加载时，如果已有用户消息历史，自动隐藏快捷操作

### 4. 条件渲染

#### 快捷操作的条件显示
```typescript
{/* Quick Actions - 只在用户未发送消息时显示 */}
{!hasUserSentMessage && (
  <div className="grid grid-cols-2 gap-2">
    {quickActions.map((action) => {
      const Icon = action.icon;
      return (
        <Button
          key={action.id}
          variant="outline"
          size="sm"
          onClick={() => handleQuickAction(action)}
          className="justify-start text-xs h-8"
          disabled={status === 'loading'}
        >
          <Icon className="w-3 h-3 mr-1" />
          {action.label}
        </Button>
      );
    })}
  </div>
)}
```

## 📊 用户体验流程

### 初始状态
```
┌─────────────────────────────────┐
│ AI助手                          │
├─────────────────────────────────┤
│ 👋 您好！我是专业的测试用例助手  │
├─────────────────────────────────┤
│ [生成测试步骤] [生成自动化脚本]  │
│ [生成Midscene配置] [分析覆盖度]  │
├─────────────────────────────────┤
│ 输入框...                       │
└─────────────────────────────────┘
```

### 用户发送消息后
```
┌─────────────────────────────────┐
│ AI助手                          │
├─────────────────────────────────┤
│ 👋 您好！我是专业的测试用例助手  │
│                                 │
│ 用户: 请帮我生成测试步骤         │
│                                 │
│ AI: 好的，我来为您生成...        │
├─────────────────────────────────┤
│ 输入框...                       │
└─────────────────────────────────┘
```

## 🔄 触发场景

### 1. 直接输入消息
- 用户在输入框中输入文本并发送
- 触发`handleSubmit` → 设置`hasUserSentMessage = true`

### 2. 点击快捷操作
- 用户点击任意快捷操作按钮
- 触发`handleQuickAction` → 设置`hasUserSentMessage = true`

### 3. 页面重新加载
- 页面刷新或重新访问
- `useEffect`检查消息历史 → 如有用户消息则设置`hasUserSentMessage = true`

## 🎨 界面变化

### 隐藏前
- 显示4个快捷操作按钮
- 占用界面空间
- 提供快速开始选项

### 隐藏后
- 快捷操作区域完全隐藏
- 更多空间用于消息显示
- 界面更加简洁

## 🌟 功能特点

### 1. 自动化
- 无需用户手动操作
- 智能检测用户行为
- 自动调整界面状态

### 2. 持久性
- 状态在会话期间保持
- 页面刷新后正确恢复
- 基于消息历史判断

### 3. 用户友好
- 初次使用时提供引导
- 开始对话后界面简化
- 减少视觉干扰

### 4. 响应式
- 立即响应用户操作
- 平滑的界面过渡
- 保持良好的用户体验

## 🔍 边界情况处理

### 1. 空消息历史
- 显示快捷操作
- 提供使用引导

### 2. 只有AI消息
- 显示快捷操作
- 等待用户互动

### 3. 混合消息历史
- 检测到用户消息即隐藏
- 不区分消息来源

### 4. 页面状态恢复
- 基于消息历史恢复状态
- 确保一致的用户体验

## 📋 相关文件

### 修改的文件
- `app/test-case/[id]/components/testcase-assistant.tsx`

### 涉及的组件
- 状态管理（useState）
- 事件处理（handleSubmit, handleQuickAction）
- 副作用处理（useEffect）
- 条件渲染（JSX条件）

## 🧪 测试场景

### 1. 首次访问
- 验证快捷操作显示
- 验证欢迎消息显示

### 2. 发送消息
- 验证快捷操作隐藏
- 验证消息正常发送

### 3. 点击快捷操作
- 验证消息自动发送
- 验证快捷操作隐藏

### 4. 页面刷新
- 验证状态正确恢复
- 验证界面一致性

## ✨ 总结

这个功能通过智能的状态管理：

- **提升用户体验**: 初次使用时提供引导，开始对话后界面简化
- **自动化管理**: 无需用户手动操作，自动响应用户行为
- **状态持久**: 正确处理页面刷新和消息历史
- **界面优化**: 减少视觉干扰，提供更清洁的聊天体验

现在用户在发送第一条消息后，快捷操作将自动隐藏，提供更专注的对话体验！
