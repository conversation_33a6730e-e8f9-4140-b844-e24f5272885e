'use client';

import { Bot } from 'lucide-react';
import BaseAutomationConfig from './base-automation-config';
import { AutomationConfig } from '../../types';

interface MidsceneConfigProps {
  config?: AutomationConfig;
  onEdit?: () => void;
  onCreate?: () => void;
  onRun?: () => void;
}

export default function MidsceneConfig({
  config,
  onEdit,
  onCreate,
  onRun
}: MidsceneConfigProps) {
  return (
    <BaseAutomationConfig
      config={config}
      framework="midscene"
      frameworkIcon={Bot}
      frameworkColor="text-purple-600"
      frameworkBg="bg-purple-100"
      frameworkBorder="border-purple-200"
      onEdit={onEdit}
      onCreate={onCreate}
      onRun={onRun}
    />
  );
}
