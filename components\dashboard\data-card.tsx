"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ChartConfig, ChartContainer } from "@/components/ui/chart"

export interface StatCard {
  title: string
  value: number | string
  percent: string
  chartType: 'line' | 'bar'
  chartKey: string
  chartData: Array<{ day: string; value: number }>
}

// 使用CSS变量定义的主题色透明度变体
const getChartColor = (index: number) => {
  const colors = [
    'var(--primary)',
    'var(--primary-80)',
    'var(--primary-60)',
    'var(--primary-40)',
  ];
  return colors[index % colors.length];
}

export function DataCard({ stats }: { stats: StatCard[] }) {
  return (
    <div className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
      {stats.map((stat, i) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">{stat.title}</CardTitle>
          </CardHeader>
          <CardContent className="pb-0">
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.percent} from last month</p>
            <ChartContainer config={{}} className="mt-2 h-[80px] w-full">
              {stat.chartType === 'line' ? (
                <LineChart data={stat.chartData} margin={{ top: 5, right: 10, left: 10, bottom: 0 }}>
                  <XAxis dataKey="day" hide />
                  <Line
                    type="monotone"
                    strokeWidth={2}
                    dataKey={stat.chartKey}
                    stroke={getChartColor(i)}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              ) : (
                <BarChart data={stat.chartData}>
                  <XAxis dataKey="day" hide />
                  <Bar
                    dataKey={stat.chartKey}
                    fill={getChartColor(i)}
                    radius={4}
                  />
                </BarChart>
              )}
            </ChartContainer>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}