@echo off
setlocal enabledelayedexpansion

REM Docker 管理脚本 (Windows版本)
REM 用于简化 Docker 部署操作

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 打印带颜色的消息
:print_message
echo %GREEN%[INFO]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:print_header
echo %BLUE%=== %~1 ===%NC%
goto :eof

REM 检查 Docker 是否安装
:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker 未安装，请先安装 Docker"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)
goto :eof

REM 生产环境部署
:deploy_production
call :print_header "部署生产环境"
call :check_docker
if errorlevel 1 exit /b 1

call :print_message "构建并启动生产环境..."
docker-compose -f docker-compose.yml up --build -d
call :print_message "生产环境已启动，访问 http://localhost:3000"
goto :eof

REM 开发环境部署
:deploy_development
call :print_header "部署开发环境"
call :check_docker
if errorlevel 1 exit /b 1

call :print_message "构建并启动开发环境..."
docker-compose -f docker-compose.dev.yml up --build -d
call :print_message "开发环境已启动，访问 http://localhost:3000"
goto :eof

REM 停止所有服务
:stop_services
call :print_header "停止服务"
call :check_docker
if errorlevel 1 exit /b 1

call :print_message "停止生产环境..."
docker-compose -f docker-compose.yml down

call :print_message "停止开发环境..."
docker-compose -f docker-compose.dev.yml down

call :print_message "所有服务已停止"
goto :eof

REM 查看日志
:view_logs
set "service=%~1"
if "%service%"=="" set "service=app"
set "env=%~2"
if "%env%"=="" set "env=prod"

call :print_header "查看日志 - %service% (%env%)"
call :check_docker
if errorlevel 1 exit /b 1

if "%env%"=="dev" (
    docker-compose -f docker-compose.dev.yml logs -f %service%
) else (
    docker-compose -f docker-compose.yml logs -f %service%
)
goto :eof

REM 清理 Docker 资源
:cleanup
call :print_header "清理 Docker 资源"
call :check_docker
if errorlevel 1 exit /b 1

call :print_warning "这将删除所有未使用的容器、网络和镜像"
set /p "confirm=确定要继续吗? (y/N): "
if /i "%confirm%"=="y" (
    call :print_message "清理中..."
    docker system prune -a -f
    call :print_message "清理完成"
) else (
    call :print_message "取消清理"
)
goto :eof

REM 重启服务
:restart_services
set "env=%~1"
if "%env%"=="" set "env=prod"

call :print_header "重启服务 (%env%)"
call :check_docker
if errorlevel 1 exit /b 1

if "%env%"=="dev" (
    docker-compose -f docker-compose.dev.yml restart
) else (
    docker-compose -f docker-compose.yml restart
)

call :print_message "服务已重启"
goto :eof

REM 显示状态
:show_status
call :print_header "服务状态"
call :check_docker
if errorlevel 1 exit /b 1

call :print_message "生产环境状态:"
docker-compose -f docker-compose.yml ps

echo.
call :print_message "开发环境状态:"
docker-compose -f docker-compose.dev.yml ps
goto :eof

REM 显示帮助信息
:show_help
echo Docker 管理脚本 (Windows版本)
echo.
echo 用法: %0 [命令] [选项]
echo.
echo 命令:
echo   prod         部署生产环境
echo   dev          部署开发环境
echo   stop         停止所有服务
echo   restart      重启服务 (默认生产环境)
echo   logs         查看日志
echo   status       显示服务状态
echo   cleanup      清理 Docker 资源
echo   help         显示此帮助信息
echo.
echo 选项:
echo   --dev        指定开发环境 (用于 restart 和 logs 命令)
echo   --service    指定服务名称 (用于 logs 命令)
echo.
echo 示例:
echo   %0 prod                    # 部署生产环境
echo   %0 dev                     # 部署开发环境
echo   %0 logs --dev --service db # 查看开发环境数据库日志
echo   %0 restart --dev           # 重启开发环境
echo.
echo 注意: 此脚本需要在项目根目录运行
goto :eof

REM 主函数
set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="prod" goto :deploy_production
if "%command%"=="production" goto :deploy_production
if "%command%"=="dev" goto :deploy_development
if "%command%"=="development" goto :deploy_development
if "%command%"=="stop" goto :stop_services
if "%command%"=="restart" goto :restart_services
if "%command%"=="logs" goto :view_logs
if "%command%"=="status" goto :show_status
if "%command%"=="cleanup" goto :cleanup
if "%command%"=="help" goto :show_help
if "%command%"=="-h" goto :show_help
if "%command%"=="--help" goto :show_help

call :print_error "未知命令: %command%"
echo.
call :show_help
exit /b 1 