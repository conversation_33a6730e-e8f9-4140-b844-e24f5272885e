import { codeDocument<PERSON><PERSON><PERSON> } from '@/artifacts/code/server';
import { imageDocumentHandler } from '@/artifacts/image/server';
import { sheetDocumentHandler } from '@/artifacts/sheet/server';
import { textDocumentHandler } from '@/artifacts/text/server';
import { testingDocumentHandler } from '@/artifacts/midscene/server';
import { ArtifactKind } from '@/components/chat/artifact';
import { DataStreamWriter } from 'ai';
import { Document } from '../db/schema';
import { saveDocument } from '../db/queries';
import { Session } from 'next-auth';
import { appLogger } from '@/lib/logger';
import { MidsceneReportType, MIDSCENE_REPORT } from '@/artifacts/types';

// 创建模块专用的日志记录器
const logger = appLogger.child('artifacts-server');

export interface SaveDocumentProps {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}

export interface CreateDocumentCallbackProps {
  id: string;
  title: string;
  dataStream: DataStreamWriter;
  session: Session;
  chatId?: string; // 添加可选的chatId参数
}

export interface UpdateDocumentCallbackProps {
  document: Document;
  description: string;
  dataStream: DataStreamWriter;
  session: Session;
}

// 定义文档处理结果类型
export interface DocumentHandlerResult {
  content: string;
  report_uri?: string;
  error?: string;
  [key: string]: any; // 允许其他属性
}

export interface DocumentHandler<T = ArtifactKind> {
  kind: T;
  onCreateDocument: (args: CreateDocumentCallbackProps) => Promise<DocumentHandlerResult>;
  onUpdateDocument: (args: UpdateDocumentCallbackProps) => Promise<DocumentHandlerResult>;
}

export function createDocumentHandler<T extends ArtifactKind>(config: {
  kind: T;
  onCreateDocument: (params: CreateDocumentCallbackProps) => Promise<string | DocumentHandlerResult>;
  onUpdateDocument: (params: UpdateDocumentCallbackProps) => Promise<string | DocumentHandlerResult>;
}): DocumentHandler<T> {
  return {
    kind: config.kind,
    onCreateDocument: async (args: CreateDocumentCallbackProps) => {
      const result = await config.onCreateDocument({
        id: args.id,
        title: args.title,
        dataStream: args.dataStream,
        session: args.session,
        chatId: args.chatId,
      });

      // 处理返回值，支持字符串或对象
      const draftContent = typeof result === 'string' ? result : result.content;
      const handlerResult = typeof result === 'string' 
        ? { content: result } 
        : result;

      if (args.session?.user?.id) {
        try {
          logger.info(`尝试保存文档: ID=${args.id}, 标题=${args.title}, 类型=${config.kind}, 用户=${args.session.user.id}`);
          
          // 创建日期对象并处理SQLite日期格式问题
          const now = new Date();
          const createdAt = process.env.DB_PROVIDER === 'sqlite' 
            ? Math.floor(now.getTime() / 1000) // SQLite存储为Unix时间戳(秒)
            : now;
          
          await saveDocument({
            id: args.id,
            title: args.title,
            content: draftContent,
            kind: config.kind,
            userId: args.session.user.id,
            chatId: args.chatId, // 传递chatId
          });
          
          logger.info(`文档保存成功: ID=${args.id}`);
        } catch (error) {
          logger.error(`保存文档失败: ID=${args.id}, 错误: ${error instanceof Error ? error.message : String(error)}`);
          // 不抛出错误，让流程继续，前端仍能显示文档
        }
      } else {
        logger.warn(`无法保存文档，用户未认证: ID=${args.id}`);
      }

      return handlerResult;
    },
    onUpdateDocument: async (args: UpdateDocumentCallbackProps) => {
      const result = await config.onUpdateDocument({
        document: args.document,
        description: args.description,
        dataStream: args.dataStream,
        session: args.session,
      });

      // 处理返回值，支持字符串或对象
      const draftContent = typeof result === 'string' ? result : result.content;
      const handlerResult = typeof result === 'string' 
        ? { content: result } 
        : result;

      if (args.session?.user?.id) {
        try {
          logger.info(`尝试更新文档: ID=${args.document.id}, 标题=${args.document.title}, 类型=${config.kind}, 用户=${args.session.user.id}`);
          
          // 创建日期对象并处理SQLite日期格式问题
          const now = new Date();
          const createdAt = process.env.DB_PROVIDER === 'sqlite' 
            ? Math.floor(now.getTime() / 1000) // SQLite存储为Unix时间戳(秒)
            : now;
          
          await saveDocument({
            id: args.document.id,
            title: args.document.title,
            content: draftContent,
            kind: config.kind,
            userId: args.session.user.id,
          });
          
          logger.info(`文档更新成功: ID=${args.document.id}`);
        } catch (error) {
          logger.error(`更新文档失败: ID=${args.document.id}, 错误: ${error instanceof Error ? error.message : String(error)}`);
          // 不抛出错误，让流程继续，前端仍能显示文档
        }
      } else {
        logger.warn(`无法更新文档，用户未认证: ID=${args.document.id}`);
      }

      return handlerResult;
    },
  };
}

/*
 * Use this array to define the document handlers for each artifact kind.
 */
export const documentHandlersByArtifactKind: Array<DocumentHandler> = [
  textDocumentHandler,
  codeDocumentHandler,
  imageDocumentHandler,
  sheetDocumentHandler,
  testingDocumentHandler,
];

export const artifactKinds = ['text', 'code', 'image', 'sheet', MIDSCENE_REPORT] as const;
