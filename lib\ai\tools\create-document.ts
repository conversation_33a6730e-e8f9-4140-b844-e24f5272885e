import { generateUUID } from '@/lib/utils';
import { DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import { Session } from 'next-auth';
import {
  artifactKinds,
  documentHandlersByArtifactKind,
} from '@/lib/artifacts/server';
import { aiLogger } from '@/lib/logger';

// 创建模块专用的日志记录器
const logger = aiLogger.child('create-document');

interface CreateDocumentProps {
  session: Session;
  dataStream: DataStreamWriter;
  chatId?: string; // 添加可选的chatId参数
}

export const createDocument = ({ session, dataStream, chatId }: CreateDocumentProps) =>
  tool({
    description:
      'Create a document for a writing or content creation activities. This tool will call other functions that will generate the contents of the document based on the title and kind.',
    parameters: z.object({
      title: z.string(),
      kind: z.enum(artifactKinds),
    }),
    execute: async ({ title, kind }) => {
      const id = generateUUID();

      dataStream.writeData({
        type: 'kind',
        content: kind,
      });

        dataStream.writeData({
          type: 'id',
          content: id,
        });

        dataStream.writeData({
          type: 'title',
          content: title,
        });

        dataStream.writeData({
          type: 'clear',
          content: '',
        });

        const documentHandler = documentHandlersByArtifactKind.find(
          (documentHandlerByArtifactKind) =>
            documentHandlerByArtifactKind.kind === kind,
        );

      if (!documentHandler) {
        const errorMsg = `No document handler found for kind: ${kind}`;
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // 调用文档处理器生成内容
      logger.info(`开始创建文档: ID=${id}, 标题=${title}, 类型=${kind}`);
      const result = await documentHandler.onCreateDocument({
        id,
        title,
        dataStream,
        session,
        chatId, // 传递chatId
      });
      logger.info(`文档创建完成: ID=${id}`);

        dataStream.writeData({ type: 'finish', content: '' });

      // 提取报告URI，支持多种可能的属性名
      let reportUri = null;
      if (result && typeof result === 'object') {
        reportUri = result.report_uri || null;
        if (reportUri) {
          logger.info(`文档创建后获取到报告URI: ${reportUri}`);
        }
      }
      
      // 构造消息内容，包含文档引用
      const messageContent = JSON.stringify([
        { type: "text", text: `我已经创建了一个文档："${title}"` },
        { type: "document-reference", title: title, document_id: id },
        {
          type: "tool-invocation",
          toolInvocation: {
            toolName: "createDocument",
            toolCallId: generateUUID(),
            state: "result",
            result: {
              id: id,
              title: title,
              kind: kind,
              report_uri: reportUri,
              isVisible: true
            }
          }
        }
      ]);

      // 返回简洁的消息，避免AI生成重复的内容描述
      return {
        id,
        title,
        kind,
        content: messageContent, // 返回完整的消息内容
        isVisible: true,
      };
    },
  });
