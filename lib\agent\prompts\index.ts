/**
 * 提示词索引文件
 * 从各个提示词文件导入并统一导出
 */

import { automationPrompt } from './automation';
import { artifactsPrompt } from './artifacts';
import { mixedPrompt } from './mixed';

// 提示词类型
export type PromptType = 'automation' | 'artifacts' | 'mixed';

// 提示词映射
export const prompts: Record<PromptType, string> = {
  automation: automationPrompt,
  artifacts: artifactsPrompt,
  mixed: mixedPrompt
};

// 获取提示词函数
export function getPrompt(type: PromptType): string {
  return prompts[type];
}

// 导出所有提示词
export {
  automationPrompt,
  artifactsPrompt,
  mixedPrompt
}; 