# DevOps 配置

本文件夹包含项目的所有 DevOps 相关配置文件，包括 Docker 配置、部署脚本等。

## 📁 文件结构

```
devops/
├── Dockerfile              # 生产环境 Dockerfile
├── Dockerfile.dev          # 开发环境 Dockerfile
├── docker-compose.yml      # 生产环境 Docker Compose 配置
├── docker-compose.dev.yml  # 开发环境 Docker Compose 配置
├── .dockerignore          # Docker 构建忽略文件
├── DOCKER_README.md       # Docker 部署详细指南
├── docker-helper.bat      # Windows Docker 管理脚本
├── docker-helper.sh       # Linux/macOS Docker 管理脚本
└── README.md              # 本文件
```

## 🚀 快速使用

### 生产环境构建镜像

```bash
# 从项目根目录运行
# 构建生产环境镜像（使用 devops/Dockerfile）
docker build -f devops/Dockerfile -t ai-run-nextjs:latest .
```

### 生产环境部署

```bash
# 从项目根目录运行
docker-compose -f devops/docker-compose.yml up --build -d
```

### 开发环境运行

```bash
docker run --env-file .env  -v ./sqlite.db:/app/data/sqlite.db -d -p 3000:3000 --name ai-run-nextjs ai-run-nextjs:latest
```


### 开发环境构建镜像

```bash
# 从项目根目录运行
# 构建开发环境镜像（使用 devops/Dockerfile.dev）
docker build -f devops/Dockerfile.dev -t ai-run-nextjs:dev .
```

### 开发环境部署

```bash
# 从项目根目录运行
docker-compose -f devops/docker-compose.dev.yml up --build -d
```

### 开发环境运行

在windows下请把数据库文件复制到容器里面
```bash
docker run --env-file .env -d -p 3000:3000 --name ai-run-nextjs ai-run-nextjs:latest
```

### 使用管理脚本

```bash
# 从项目根目录运行
# Windows
./devops/docker-helper.bat prod    # 生产环境
./devops/docker-helper.bat dev     # 开发环境

# Linux/macOS
./devops/docker-helper.sh prod     # 生产环境
./devops/docker-helper.sh dev      # 开发环境

# 或者从 devops 目录运行
cd devops
./docker-helper.bat prod           # Windows 生产环境
./docker-helper.sh dev             # Linux/macOS 开发环境
```

## 📋 文件说明

### Dockerfile
- **用途**: 生产环境容器构建
- **特点**: 多阶段构建，优化镜像大小
- **基础镜像**: node:18-alpine

### Dockerfile.dev
- **用途**: 开发环境容器构建
- **特点**: 简化构建，支持热重载
- **基础镜像**: node:18-alpine

### docker-compose.yml
- **用途**: 生产环境服务编排
- **服务**: app, db (PostgreSQL), redis
- **特点**: 完整的生产环境配置

### docker-compose.dev.yml
- **用途**: 开发环境服务编排
- **服务**: app, db (PostgreSQL), redis
- **特点**: 开发友好的配置，支持源代码挂载

### .dockerignore
- **用途**: 排除不必要的文件
- **目标**: 优化构建速度和镜像大小

## 🔧 配置说明

### 环境变量

所有环境变量都在 docker-compose 文件中配置：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  - DB_PROVIDER=sqlite
  - DATABASE_URL=file:./data.db
  - NEXTAUTH_URL=http://localhost:3000
  - NEXTAUTH_SECRET=your-secret-key-here
```

### 数据持久化

```yaml
volumes:
  - ../data:/app/data      # 应用数据
  - ../logs:/app/logs      # 日志文件
  - postgres_data:/var/lib/postgresql/data  # 数据库数据
  - redis_data:/data       # Redis 数据
```

### 网络配置

- **应用端口**: 3000
- **数据库端口**: 5432
- **Redis 端口**: 6379

## 🛠️ 自定义配置

### 修改数据库配置

编辑 `docker-compose.yml` 中的数据库服务：

```yaml
db:
  image: postgres:15-alpine
  environment:
    - POSTGRES_DB=your_db_name
    - POSTGRES_USER=your_user
    - POSTGRES_PASSWORD=your_password
```

### 添加新的服务

在 docker-compose 文件中添加新服务：

```yaml
new-service:
  image: your-image
  ports:
    - "8080:8080"
  environment:
    - ENV_VAR=value
```

### 修改构建上下文

如果需要修改构建上下文，更新 docker-compose 文件：

```yaml
build:
  context: ..              # 项目根目录
  dockerfile: devops/Dockerfile
```

## 🔍 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   ```

### 日志查看

```bash
# 查看应用日志
docker-compose -f devops/docker-compose.yml logs app

# 查看所有服务日志
docker-compose -f devops/docker-compose.yml logs

# 实时查看日志
docker-compose -f devops/docker-compose.yml logs -f app
```

## 📚 相关文档

- [Docker 部署指南](DOCKER_README.md) - 详细的部署说明
- [项目主文档](../README.md) - 项目完整文档
- [快速开始指南](../QUICKSTART.md) - 快速启动指南

## 🤝 贡献

如需修改 DevOps 配置，请：

1. 在 `devops/` 目录下进行修改
2. 更新相关文档
3. 测试配置是否正常工作
4. 提交 Pull Request

---

**注意**: 所有 Docker 命令都需要从项目根目录运行，因为构建上下文设置为 `..`。 