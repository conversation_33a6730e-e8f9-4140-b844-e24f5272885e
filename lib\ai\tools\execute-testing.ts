import { DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import { Session } from 'next-auth';
import { testingService } from '@/lib/services/testing-service';
import fs from 'fs';
import path from 'path';
import { generateUUID as generateDocumentUUID } from '@/lib/utils';
import { documentHandlersByArtifactKind } from '@/lib/artifacts/server';
import { MidsceneReportType, MIDSCENE_REPORT } from '@/artifacts/types';
import { createTestRun, updateTestRun, getAutomationConfig, saveDocument } from '@/lib/db/queries';

interface ExecuteTestingProps {
  session: Session;
  dataStream: DataStreamWriter;
  chatId?: string; // 添加可选的chatId参数
}

// 确保报告目录存在
function ensureReportDirExists() {
  const reportDir = path.join(process.cwd(), 'public', 'report');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  return reportDir;
}

// 简单的日志函数
function log(message: string) {
  const logDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  const logFile = path.join(logDir, 'testing-tool.log');
  fs.appendFileSync(logFile, `[${new Date().toISOString()}] ${message}\n`);
  console.log(`[Testing Tool] ${message}`);
}

// 移除 getReportUrlWithRetry

export const executeAutomationTesting = ({ session, dataStream, chatId }: ExecuteTestingProps) =>
  tool({
    description: '执行网页测试并生成测试报告。可以通过YAML描述测试步骤，自动执行网页交互并返回结果。',
    parameters: z.object({
      title: z.string().describe('测试的标题'),
      url: z.string().describe('要测试的网页URL'),
      testCaseId: z.string().optional().describe('关联的测试用例ID，用于保存测试运行记录'),
      yaml: z.string().optional().describe('Midscene YAML格式的测试步骤，如果不提供则会自动生成'),
      additionalInfo: z.string().optional().describe('用户提供的额外测试信息，用于生成更精确的测试步骤'),
      width: z.number().optional().default(1280).describe('浏览器视窗宽度'),
      height: z.number().optional().default(800).describe('浏览器视窗高度'),
    }),
    execute: async ({ title, url, testCaseId, yaml, additionalInfo, width = 1280, height = 800 }) => {
      log(`开始执行测试工具: ${title}, URL: ${url}, 测试用例ID: ${testCaseId || '无'}`);
      ensureReportDirExists();

      let testRunId: string | null = null;
      const startTime = Date.now();

      // 如果提供了testCaseId，创建测试运行记录
      if (testCaseId) {
        try {
          testRunId = await createTestRun({
            testCaseId,
            status: 'running',
            environment: 'automation',
            executor: session.user?.email || 'automation-bot',
            logs: `开始执行自动化测试: ${title}`
          });
          log(`创建测试运行记录: ${testRunId}`);
        } catch (error) {
          log(`创建测试运行记录失败: ${error}`);
        }
      }

      try {
        // 生成文档ID和工具调用ID
        const id = generateDocumentUUID();
        const toolCallId = generateDocumentUUID();
        log(`创建文档，ID: ${id}, 工具调用ID: ${toolCallId}`);
        // 查找测试文档处理器
        const documentHandler = documentHandlersByArtifactKind.find(
          (handler) => handler.kind === MIDSCENE_REPORT,
        );
        if (!documentHandler) {
          throw new Error('未找到测试文档处理器');
        }
        // 调用文档处理器的onCreateDocument方法
        log(`调用documentHandler.onCreateDocument，ID: ${id}`);
        const testResult = await documentHandler.onCreateDocument({
          id,
          title: `${title} ${url}`,
          dataStream,
          session,
          chatId,
        });
        log(`documentHandler.onCreateDocument执行完成`);
        // 等待5秒，确保测试报告已生成
        log(`等待5秒，确保测试报告已生成`);
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 从testResult中获取报告URL
        let reportUrl = '';
        if (testResult && typeof testResult === 'object') {
          // 尝试从不同的属性名中获取报告URL
          reportUrl = testResult.publicReportUrl || testResult.report_url || testResult.report_uri || '';
          log(`从testResult中获取到报告URL: ${reportUrl || '未找到'}`);
        }
        
        // 如果没有找到报告URL，尝试从testResult.testResult中获取
        if (!reportUrl && testResult?.testResult?.publicReportUrl) {
          reportUrl = testResult.testResult.publicReportUrl;
          log(`从testResult.testResult中获取到报告URL: ${reportUrl}`);
        }
        
        // 计算测试持续时间
        const duration = Math.round((Date.now() - startTime) / 1000);

        // 更新测试运行记录
        if (testRunId) {
          try {
            const testStatus = reportUrl ? 'passed' : 'failed';
            const logs = reportUrl
              ? `测试执行成功，报告URL: ${reportUrl}`
              : '测试执行失败，未生成报告';

            await updateTestRun(testRunId, {
              status: testStatus,
              duration,
              logs
            });
            log(`更新测试运行记录: ${testRunId}, 状态: ${testStatus}, 持续时间: ${duration}s`);
          } catch (error) {
            log(`更新测试运行记录失败: ${error}`);
          }
        }

        // 构建结果对象
        const resultObject = {
          id: id,
          title: title,
          kind: MIDSCENE_REPORT,
          report_uri: reportUrl,
          isVisible: true
        };
        log(`返回结果对象: ${JSON.stringify(resultObject)}`);
        let content = `Your test has been completed, and the report has been generated. Please check the test report on the right.`
        if(!reportUrl) {
          content = `测试失败，请检查URL或yaml是否正确，或者尝试提供更具体的测试步骤。`
        }
        // 返回正确格式的消息，确保显示document-preview
        return {
          content: content,
          parts: [
            {
              type: "tool-invocation",
              toolInvocation: {
                toolName: "executeAutomationTesting",
                toolCallId: toolCallId,
                state: "result",
                result: resultObject
              }
            }
          ],
          tool_calls: [
            {
              id: toolCallId,
              type: "function",
              function: {
                name: "executeAutomationTesting",
                arguments: JSON.stringify(resultObject)
              }
            }
          ]
        };
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        log(`执行测试时出错: ${errorMsg}`);
        console.error("执行测试时出错:", error);

        // 更新测试运行记录为失败状态
        if (testRunId) {
          try {
            const duration = Math.round((Date.now() - startTime) / 1000);
            await updateTestRun(testRunId, {
              status: 'failed',
              duration,
              logs: `测试执行失败: ${errorMsg}`
            });
            log(`更新测试运行记录为失败状态: ${testRunId}`);
          } catch (updateError) {
            log(`更新测试运行记录失败: ${updateError}`);
          }
        }

        // 返回格式化的错误消息
        return {
          content: `## 测试执行失败\n- 测试URL: ${url}\n- 错误信息: ${errorMsg}\n\n请检查URL是否正确，或者尝试提供更具体的测试步骤。`
        };
      }
    }
  });

// 专门为测试用例执行自动化测试的工具，使用数据库中的配置
export const executeTestCaseAutomation = ({ session, dataStream, chatId }: ExecuteTestingProps) =>
  tool({
    description: '执行测试用例的自动化测试，使用数据库中已保存的自动化配置和YAML。如果没有配置，会提示用户先生成配置。',
    parameters: z.object({
      testCaseId: z.string().describe('测试用例ID'),
      title: z.string().describe('测试标题'),
      framework: z.string().optional().describe('指定使用的自动化框架，如果不指定则使用默认配置'),
    }),
    execute: async ({ testCaseId, title, framework }) => {
      log(`开始执行测试用例自动化测试: ${title}, 测试用例ID: ${testCaseId}, 框架: ${framework || '默认'}`);
      ensureReportDirExists();

      let testRunId: string | null = null;
      const startTime = Date.now();

      try {
        // 1. 获取数据库中的自动化配置
        const automationConfig = await getAutomationConfig(testCaseId, framework);

        if (!automationConfig) {
          return {
            content: `## ❌ 未找到自动化配置\n\n测试用例 ${testCaseId} 没有配置自动化测试。\n\n请先：\n1. 生成Midscene配置\n2. 或者使用"生成自动化配置"功能\n\n然后再执行自动化测试。`
          };
        }

        // 2. 检查配置中是否有YAML内容
        let yamlContent = '';
        if (automationConfig.parameters && typeof automationConfig.parameters === 'object') {
          const params = automationConfig.parameters as any;
          yamlContent = params.yaml_content || '';
        }

        if (!yamlContent) {
          return {
            content: `## ❌ 配置中缺少YAML内容\n\n找到了自动化配置，但缺少YAML测试脚本。\n\n请重新生成Midscene配置以包含完整的YAML内容。`
          };
        }

        log(`找到自动化配置: 框架=${automationConfig.framework}, 环境=${automationConfig.environment}`);
        log(`YAML内容长度: ${yamlContent.length} 字符`);

        // 3. 创建测试运行记录
        testRunId = await createTestRun({
          testCaseId,
          status: 'running',
          environment: automationConfig.environment || 'test',
          executor: session.user?.email || 'automation-bot',
          logs: `开始执行自动化测试: ${title} (使用数据库配置)`
        });
        log(`创建测试运行记录: ${testRunId}`);

        // 4. 生成文档ID和工具调用ID
        const id = generateDocumentUUID();
        const toolCallId = generateDocumentUUID();
        log(`创建文档，ID: ${id}, 工具调用ID: ${toolCallId}`);

        // 5. 查找测试文档处理器
        const documentHandler = documentHandlersByArtifactKind.find(
          (handler) => handler.kind === MIDSCENE_REPORT,
        );
        if (!documentHandler) {
          throw new Error('未找到测试文档处理器');
        }

        // 6. 创建临时文档存储YAML，这样midscene处理器就能找到它
        if (chatId) {
          try {
            const tempDocId = generateDocumentUUID();
            await saveDocument({
              id: tempDocId,
              chatId: chatId,
              title: `YAML配置 - ${title}`,
              content: yamlContent,
              kind: 'text' as any, // 临时使用text类型
              userId: session.user?.id || 'system'
            });
            log(`创建临时YAML文档: ${tempDocId}`);
          } catch (error) {
            log(`创建临时YAML文档失败: ${error}`);
          }
        }

        // 7. 调用文档处理器，在title中包含URL以便处理器提取
        const testUrl = extractUrlFromYaml(yamlContent);
        const titleWithUrl = testUrl ? `${title} ${testUrl}` : title;

        log(`调用documentHandler.onCreateDocument，使用数据库YAML，URL: ${testUrl}`);
        const testResult = await documentHandler.onCreateDocument({
          id,
          title: titleWithUrl,
          dataStream,
          session,
          chatId
        });
        log(`documentHandler.onCreateDocument执行完成`);

        // 8. 等待测试报告生成
        log(`等待5秒，确保测试报告已生成`);
        await new Promise(resolve => setTimeout(resolve, 5000));

        // 9. 获取报告URL
        let reportUrl = '';
        if (testResult && typeof testResult === 'object') {
          reportUrl = testResult.publicReportUrl || testResult.report_url || testResult.report_uri || '';
          log(`从testResult中获取到报告URL: ${reportUrl || '未找到'}`);
        }

        if (!reportUrl && testResult?.testResult?.publicReportUrl) {
          reportUrl = testResult.testResult.publicReportUrl;
          log(`从testResult.testResult中获取到报告URL: ${reportUrl}`);
        }

        // 10. 计算测试持续时间并更新记录
        const duration = Math.round((Date.now() - startTime) / 1000);

        if (testRunId) {
          const testStatus = reportUrl ? 'passed' : 'failed';
          const logs = reportUrl
            ? `测试执行成功，使用${automationConfig.framework}框架，报告URL: ${reportUrl}`
            : `测试执行失败，使用${automationConfig.framework}框架，未生成报告`;

          await updateTestRun(testRunId, {
            status: testStatus,
            duration,
            logs
          });
          log(`更新测试运行记录: ${testRunId}, 状态: ${testStatus}, 持续时间: ${duration}s`);
        }

        // 11. 构建结果对象
        const resultObject = {
          id: id,
          title: `${title} (${automationConfig.framework})`,
          kind: MIDSCENE_REPORT,
          report_uri: reportUrl,
          isVisible: true
        };
        log(`返回结果对象: ${JSON.stringify(resultObject)}`);

        let content = `✅ 自动化测试执行完成！\n\n**使用配置**: ${automationConfig.framework} (${automationConfig.environment})\n**测试用例**: ${title}\n\n测试报告已生成，请查看右侧报告。`
        if(!reportUrl) {
          content = `❌ 测试执行失败\n\n**使用配置**: ${automationConfig.framework} (${automationConfig.environment})\n**测试用例**: ${title}\n\n请检查YAML配置或网络连接。`
        }

        return {
          content: content,
          parts: [
            {
              type: "tool-invocation",
              toolInvocation: {
                toolName: "executeTestCaseAutomation",
                toolCallId: toolCallId,
                state: "result",
                result: resultObject
              }
            }
          ],
          tool_calls: [
            {
              id: toolCallId,
              type: "function",
              function: {
                name: "executeTestCaseAutomation",
                arguments: JSON.stringify(resultObject)
              }
            }
          ]
        };

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        log(`执行测试用例自动化测试时出错: ${errorMsg}`);
        console.error("执行测试用例自动化测试时出错:", error);

        // 更新测试运行记录为失败状态
        if (testRunId) {
          try {
            const duration = Math.round((Date.now() - startTime) / 1000);
            await updateTestRun(testRunId, {
              status: 'failed',
              duration,
              logs: `测试执行失败: ${errorMsg}`
            });
            log(`更新测试运行记录为失败状态: ${testRunId}`);
          } catch (updateError) {
            log(`更新测试运行记录失败: ${updateError}`);
          }
        }

        return {
          content: `## ❌ 自动化测试执行失败\n\n**测试用例**: ${title}\n**错误信息**: ${errorMsg}\n\n请检查自动化配置或联系管理员。`
        };
      }
    }
  });

// 从YAML中提取URL的辅助函数
function extractUrlFromYaml(yamlContent: string): string {
  try {
    const urlMatch = yamlContent.match(/url:\s*(.+)/);
    return urlMatch ? urlMatch[1].trim() : '';
  } catch (error) {
    log(`提取URL失败: ${error}`);
    return '';
  }
}