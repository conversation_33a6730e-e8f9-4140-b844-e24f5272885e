"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

import { MidsceneReportType, MIDSCENE_REPORT } from '@/artifacts/types';
import { getChartColor } from '@/lib/theme-colors';

export interface DocumentTypeTrend {
  date: string;
  sheet: number;
  [MIDSCENE_REPORT]: number;
  text: number;
  code: number;
  all: number;
}

interface TrendAreaChartProps {
  documentTypeCounts: DocumentTypeTrend[];
}

export function TrendAreaChart({ documentTypeCounts }: TrendAreaChartProps) {
  const [activeChart, setActiveChart] =
    React.useState<'all' | 'sheet' | MidsceneReportType | 'text' | 'code'>('all')

  // 统计总数
  const total = React.useMemo(
    () => {
      const sum = { all: 0, sheet: 0, [MIDSCENE_REPORT]: 0, text: 0, code: 0 };
      documentTypeCounts?.forEach(item => {
        sum.all += item.all || 0;
        sum.sheet += item.sheet || 0;
        sum[MIDSCENE_REPORT] += item[MIDSCENE_REPORT] || 0;
        sum.text += item.text || 0;
        sum.code += item.code || 0;
      });
      return sum;
    },
    [documentTypeCounts]
  );

  // 英文label映射
  const typeLabels: Record<string, string> = {
    all: 'ALL',
    sheet: 'Sheet',
    [MIDSCENE_REPORT]: 'Midscene Report',
    text: 'Text',
    code: 'Code',
  };

  // 颜色映射 - 使用CSS变量定义的主题色透明度变体
  const barColors: Record<string, string> = {
    all: 'var(--primary)',
    sheet: 'var(--primary-80)',
    [MIDSCENE_REPORT]: 'var(--primary-60)',
    text: 'var(--primary-40)',
    code: 'var(--primary-20)',
  };

  return (
    <Card>
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
          <CardTitle>Type Trends</CardTitle>
          <CardDescription>
            Recent trends in the quantity of various types of documents
          </CardDescription>
        </div>
        <div className="flex">
          {['all', 'sheet', MIDSCENE_REPORT, 'text', 'code'].map((key) => (
            <button
              key={key}
              data-active={activeChart === key}
              className="relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:border-primary/20 hover:bg-primary/5 transition-colors sm:border-l sm:border-t-0 sm:px-8 sm:py-6"
              onClick={() => setActiveChart(key as any)}
            >
              <span className="text-xs text-muted-foreground">
                {key === 'all' ? 'ALL' : key.toUpperCase() }
              </span>
              <span className="text-lg font-bold leading-none sm:text-3xl">
                {(total[key as keyof typeof total] ?? 0).toLocaleString()}
              </span>
            </button>
          ))}
        </div>
      </CardHeader>
      <CardContent className="px-2 sm:p-6">
        <ChartContainer config={{}} className="aspect-auto h-[250px] w-full">
          <BarChart
            data={documentTypeCounts}
            margin={{ left: 12, right: 12 }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className="w-[150px]"
                  nameKey={activeChart}
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })
                  }}
                />
              }
            />
            <Bar dataKey={activeChart} fill={barColors[activeChart]} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
