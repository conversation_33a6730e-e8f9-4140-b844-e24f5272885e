'use client';

import { Activity } from 'lucide-react';
import BaseAutomationConfig from './base-automation-config';
import { AutomationConfig } from '../../types';

interface PlaywrightConfigProps {
  config?: AutomationConfig;
  onEdit?: () => void;
  onDelete?: () => void;
  onCreate?: () => void;
  onRun?: () => void;
}

export default function PlaywrightConfig({
  config,
  onEdit,
  onDelete,
  onCreate,
  onRun
}: PlaywrightConfigProps) {
  return (
    <BaseAutomationConfig
      config={config}
      framework="playwright"
      frameworkIcon={Activity}
      frameworkColor="text-green-600"
      frameworkBg="bg-green-100"
      frameworkBorder="border-green-200"
      onEdit={onEdit}
      onDelete={onDelete}
      onCreate={onCreate}
      onRun={onRun}
    />
  );
}
