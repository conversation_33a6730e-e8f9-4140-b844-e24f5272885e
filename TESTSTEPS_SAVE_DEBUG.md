# 测试步骤保存问题调试

## 🐛 问题描述

**现象**: AI生成的测试步骤在聊天框中显示正确，但没有保存到数据库中。

**测试用例**: `http://localhost:3000/test-case/ff281180-160f-4b67-8248-6abecabb7d8d/`

## 🔍 调试步骤

### 1. 添加调试日志

我已经在关键位置添加了调试日志来跟踪数据流：

#### A. testcase-assistant.tsx
```typescript
// 在步骤解析处添加日志
console.log('Formatted steps:', formattedSteps);
console.log('Calling onTestCaseUpdate with steps:', { steps: formattedSteps });
onTestCaseUpdate({ steps: formattedSteps });
```

#### B. page.tsx (handleUpdate函数)
```typescript
// 在handleUpdate函数中添加日志
console.log('TestCase page received update:', updates);
console.log('Update keys:', Object.keys(updates));
console.log('Update values:', Object.values(updates));

// 特别检查steps数据
if (updates.steps) {
  console.log('Steps data received:', updates.steps);
  console.log('Steps count:', updates.steps.length);
  console.log('First step:', updates.steps[0]);
}
```

#### C. queries.ts (updateTestCase函数)
```typescript
// 在数据库操作处添加日志
console.log('Processing steps in updateTestCase:', data.steps);
console.log('Steps count:', data.steps.length);
console.log('TestCase ID:', id);

// 删除现有步骤
console.log('Deleting existing steps for testCaseId:', id);
await db.delete(testStep).where(eq(testStep.testCaseId, id));

// 插入新步骤
console.log('Steps to insert:', stepsToInsert);
console.log('Inserting steps into database...');
await db.insert(testStep).values(stepsToInsert);
console.log('Steps inserted successfully');

// 验证插入结果
const insertedSteps = await db
  .select()
  .from(testStep)
  .where(eq(testStep.testCaseId, id))
  .orderBy(asc(testStep.stepNumber));
console.log('Verification - inserted steps count:', insertedSteps.length);
console.log('Verification - first inserted step:', insertedSteps[0]);
```

### 2. 测试流程

请按以下步骤进行测试：

1. **重启服务器**
2. **打开浏览器开发者工具** (F12)
3. **访问测试页面**: `http://localhost:3000/test-case/ff281180-160f-4b67-8248-6abecabb7d8d/`
4. **在AI聊天框中输入**: "请帮我更新测试步骤"
5. **观察控制台日志**

### 3. 预期的日志输出

如果一切正常，您应该看到以下日志序列：

```
1. AI工具响应解析:
   - Processing TESTCASE_STEPS result
   - Full result string: TESTCASE_STEPS: [...]
   - JSON match: [...]
   - Parsed steps data: [...]
   - Formatted steps: [...]
   - Calling onTestCaseUpdate with steps: {...}

2. 页面更新处理:
   - TestCase page received update: {...}
   - Update keys: ['steps']
   - Steps data received: [...]
   - Steps count: X
   - First step: {...}

3. 数据库操作:
   - Processing steps in updateTestCase: [...]
   - Steps count: X
   - TestCase ID: ff281180-160f-4b67-8248-6abecabb7d8d
   - Deleting existing steps for testCaseId: ff281180-160f-4b67-8248-6abecabb7d8d
   - Steps to insert: [...]
   - Inserting steps into database...
   - Steps inserted successfully
   - Verification - inserted steps count: X
   - Verification - first inserted step: {...}

4. API响应:
   - Test case saved to database successfully
```

## 🔍 可能的问题点

### 1. 数据解析问题
- AI工具返回的JSON格式不正确
- 正则表达式匹配失败
- JSON.parse()抛出异常

### 2. 数据传递问题
- onTestCaseUpdate函数没有被正确调用
- steps数据在传递过程中丢失
- API请求没有包含steps数据

### 3. 数据库操作问题
- testCaseId不匹配
- 数据库连接问题
- SQL执行失败
- 事务回滚

### 4. 权限问题
- 用户认证失败
- 数据库写入权限不足

## 🛠️ 故障排除

### 如果没有看到步骤解析日志
```
问题: AI工具没有返回TESTCASE_STEPS格式
解决: 检查AI工具的参数传递和返回格式
```

### 如果没有看到页面更新日志
```
问题: onTestCaseUpdate函数没有被调用
解决: 检查testcase-assistant.tsx中的数据处理逻辑
```

### 如果没有看到数据库操作日志
```
问题: API请求没有包含steps数据
解决: 检查handleUpdate函数和API请求体
```

### 如果数据库操作失败
```
问题: 数据库写入错误
解决: 检查数据库连接、表结构、数据格式
```

## 📊 数据格式验证

### AI工具返回格式
```
TESTCASE_STEPS: [
  {
    "step": 1,
    "action": "在浏览器中输入网址 https://shenzhen.news.163.com/ 并访问。",
    "expected": "网页加载成功，用户能够看到网易深圳的首页。",
    "type": "manual"
  },
  ...
]
```

### 前端处理后格式
```javascript
{
  steps: [
    {
      id: "step-1640995200000-0",
      step: 1,
      action: "在浏览器中输入网址 https://shenzhen.news.163.com/ 并访问。",
      expected: "网页加载成功，用户能够看到网易深圳的首页。",
      type: "manual",
      notes: ""
    },
    ...
  ]
}
```

### 数据库插入格式
```javascript
{
  id: "uuid-generated",
  testCaseId: "ff281180-160f-4b67-8248-6abecabb7d8d",
  stepNumber: 1,
  action: "在浏览器中输入网址 https://shenzhen.news.163.com/ 并访问。",
  expected: "网页加载成功，用户能够看到网易深圳的首页。",
  type: "manual",
  notes: "",
  createdAt: 1640995200000,
  updatedAt: 1640995200000
}
```

## 🎯 下一步

1. **运行测试** - 按照上述步骤进行测试
2. **收集日志** - 记录控制台输出
3. **定位问题** - 根据日志确定问题所在
4. **修复问题** - 针对性地修复发现的问题

请运行测试并分享控制台日志，这样我就能准确定位问题所在！
