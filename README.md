# AI Run - Next.js 智能聊天应用

<div align="center">

![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-19.0.0-blue?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC?style=for-the-badge&logo=tailwind-css)
![Docker](https://img.shields.io/badge/Docker-支持-blue?style=for-the-badge&logo=docker)

一个基于 Next.js 15 和 Qwen AI 模型的现代化智能聊天应用，支持多模态交互、代码生成、文档处理等功能。

[在线演示](https://your-demo-url.com) • [问题反馈](https://github.com/your-repo/issues) • [贡献指南](CONTRIBUTING.md)

</div>

## ✨ 功能特性

### 🤖 AI 智能交互
- **多模型支持**: 集成 Qwen AI、OpenAI 等多种 AI 模型
- **多模态输入**: 支持文本、图片、文件等多种输入方式
- **智能对话**: 上下文感知的连续对话体验
- **代码生成**: 智能代码生成和编辑功能

### 📝 文档处理
- **文档上传**: 支持多种格式文档上传和处理
- **智能分析**: AI 驱动的文档内容分析和总结
- **版本控制**: 文档版本管理和历史记录

### 🎨 现代化界面
- **响应式设计**: 完美适配桌面端和移动端
- **暗色主题**: 支持明暗主题切换
- **实时预览**: 代码和文档的实时预览功能
- **拖拽交互**: 直观的拖拽上传和排序

### 🔧 开发工具
- **代码编辑器**: 集成 Monaco Editor 代码编辑器
- **调试控制台**: 内置调试和日志查看功能
- **API 测试**: 内置 API 测试工具
- **性能监控**: 实时性能监控和优化

## 🚀 快速开始

### 环境要求

- Node.js 18+ 
- npm, yarn 或 pnpm
- Docker (可选，用于容器化部署)

### 本地开发

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/ai-run-nextjs.git
   cd ai-run-nextjs
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   # 或
   pnpm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env.local
   ```
   
   编辑 `.env.local` 文件：
   ```env
   # AI 模型配置
   QWEN_API_KEY=your_qwen_api_key_here
   OPENAI_API_KEY=your_openai_api_key_here
   
   # 数据库配置
   DB_PROVIDER=sqlite
   DATABASE_URL=file:./data.db
   
   # NextAuth 配置
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here
   
   # 其他配置
   NEXT_TELEMETRY_DISABLED=1
   ```

4. **初始化数据库**
   ```bash
   npm run db:migrate
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

6. **访问应用**
   打开 [http://localhost:3000](http://localhost:3000) 查看应用

### Docker 部署

#### 生产环境
```bash
# 使用管理脚本
./devops/docker-helper.bat prod

# 或直接使用 docker-compose
docker-compose -f devops/docker-compose.yml up --build -d
```

#### 开发环境
```bash
# 使用管理脚本
./devops/docker-helper.bat dev

# 或直接使用 docker-compose
docker-compose -f devops/docker-compose.dev.yml up --build -d
```

详细部署说明请参考 [Docker 部署指南](devops/DOCKER_README.md)

## 生产环境部署说明

### 1. 挂载 sqlite.db 和 .env 文件（docker run 方式）

确保你已经在本地初始化了数据库文件：

```sh
node lib/db/init-db.js
```

然后将生成的 `sqlite.db` 文件和 `.env` 文件上传到服务器项目目录下。

运行容器时挂载这两个文件：

```sh
docker run --env-file .env \
  -v /path/to/sqlite.db:/app/sqlite.db \
  -d -p 3000:3000 \
  --name ai-run-nextjs \
  ai-run-nextjs:latest
```

- `/path/to/sqlite.db` 替换为服务器上实际的 sqlite.db 路径。
- `.env` 文件需包含数据库等相关环境变量。

---

### 2. 使用 Docker Compose 挂载

`docker-compose.prod.yml` 示例：

```yaml
version: '3'
services:
  app:
    image: ai-run-nextjs:latest
    env_file:
      - .env
    ports:
      - "3000:3000"
    volumes:
      - ./sqlite.db:/app/sqlite.db
```

- `./sqlite.db:/app/sqlite.db` 表示将当前目录下的 sqlite.db 挂载到容器内。
- `.env` 文件需与 compose 文件同目录或指定绝对路径。

---

### 3. 注意事项

- 首次部署前请确保已初始化 sqlite.db 文件。
- 挂载路径需与应用读取路径一致（通常为 `/app/sqlite.db`）。
- 不要将 sqlite.db 和 .env 文件 COPY 进镜像，仅用挂载方式提供。

## 📁 项目结构

```
ai-run-nextjs/
├── app/                    # Next.js App Router
│   ├── auth/            # 认证相关页面
│   ├── (chat)/            # 聊天功能页面
│   ├── api/               # API 路由
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── chat/             # 聊天相关组件
│   ├── ui/               # UI 基础组件
│   └── navigation/       # 导航组件
├── devops/               # DevOps 配置
│   ├── Dockerfile        # 生产环境 Dockerfile
│   ├── Dockerfile.dev    # 开发环境 Dockerfile
│   ├── docker-compose.yml # 生产环境编排
│   ├── docker-compose.dev.yml # 开发环境编排
│   ├── docker-helper.bat # Windows Docker 管理脚本
│   └── docker-helper.sh  # Linux/macOS Docker 管理脚本
├── lib/                  # 工具库
│   ├── ai/              # AI 模型配置
│   ├── db/              # 数据库配置
│   └── utils/           # 工具函数
├── hooks/               # 自定义 Hooks
├── artifacts/           # 生成的文件
├── public/              # 静态资源
└── scripts/             # 脚本文件
```

## 🔧 配置说明

### 数据库配置

项目支持 SQLite 和 PostgreSQL 两种数据库：

#### SQLite (推荐开发环境)
```env
DB_PROVIDER=sqlite
DATABASE_URL=file:./data.db
```

#### PostgreSQL (推荐生产环境)
```env
DB_PROVIDER=postgres
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
```

### AI 模型配置

#### Qwen AI
```env
QWEN_API_KEY=your_qwen_api_key_here
```

#### OpenAI
```env
OPENAI_API_KEY=your_openai_api_key_here
```

### 认证配置

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```

## 🛠️ 开发指南

### 添加新功能

1. **创建组件**
   ```bash
   # 在 components/ 目录下创建新组件
   touch components/NewFeature.tsx
   ```

2. **添加 API 路由**
   ```bash
   # 在 app/api/ 目录下创建新路由
   touch app/api/new-feature/route.ts
   ```

3. **更新数据库**
   ```bash
   # 生成迁移文件
   npm run db:generate
   
   # 应用迁移
   npm run db:migrate
   ```

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 代码规范
- 组件使用函数式组件和 Hooks
- 优先使用服务器组件 (RSC)

### 测试

```bash
# 运行单元测试
npm run test

# 运行 E2E 测试
npm run test:e2e

# 检查代码覆盖率
npm run test:coverage
```

## 📊 性能优化

### 已实现的优化

- ✅ Next.js 15 App Router
- ✅ React Server Components
- ✅ 图片优化和懒加载
- ✅ 代码分割和动态导入
- ✅ 数据库连接池
- ✅ Redis 缓存 (可选)

### 监控和分析

- 使用 Next.js Analytics 进行性能监控
- 集成 Sentry 进行错误追踪
- 使用 Lighthouse 进行性能审计

## 🔒 安全特性

- ✅ 用户认证和授权
- ✅ API 请求限流
- ✅ 输入验证和清理
- ✅ SQL 注入防护
- ✅ XSS 防护
- ✅ CSRF 防护

## 🚀 部署

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 自托管部署

参考 [Docker 部署指南](devops/DOCKER_README.md) 进行部署。

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 代码检查
npm run lint
```

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🚀 支持多 AI 模型
- 📱 响应式设计
- 🔐 用户认证系统
- 📊 实时聊天功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Qwen AI](https://qwen.ai/) - AI 模型服务
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [Drizzle ORM](https://orm.drizzle.team/) - 数据库 ORM

## 📞 联系我们

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/your-repo/discussions)

---

<div align="center">

**如果这个项目对你有帮助，请给它一个 ⭐️**

</div>