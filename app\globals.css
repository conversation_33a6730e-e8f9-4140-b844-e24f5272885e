@import "tailwindcss";
@import "tw-animate-css";

/* 确保 CSS 变量在构建时可用 */

/* 文本换行样式 */
.break-words {
  word-break: break-word;
  overflow-wrap: break-word;
}

.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

/* 测试用例助手专用的文本换行样式 */
.testcase-assistant {
  word-break: break-all !important;
  overflow-wrap: anywhere !important;
  white-space: pre-wrap !important;
}

.testcase-assistant * {
  word-break: break-all !important;
  overflow-wrap: anywhere !important;
  white-space: pre-wrap !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 确保容器不会溢出 */
.testcase-assistant {
  overflow-x: hidden !important;
}

/* 覆盖最大宽度限制 */
.testcase-assistant .max-w-3xl {
  max-width: 100% !important;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #1e88e5; /* 蓝色主题色 */
  --primary-foreground: #ffffff; /* 白色文本，在蓝色背景上 */
  --primary-80: rgba(30, 136, 229, 0.8);
  --primary-60: rgba(30, 136, 229, 0.6);
  --primary-40: rgba(30, 136, 229, 0.4);
  --primary-20: rgba(30, 136, 229, 0.2);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --sidebar: #ffffff; /* 白色侧边栏背景 */
  --sidebar-foreground: #333333; /* 深灰色文本 */
  --sidebar-primary: #1e88e5; /* 蓝色主题色 */
  --sidebar-primary-foreground: #ffffff; /* 白色文本 */
  --sidebar-accent: rgba(30, 136, 229, 0.1); /* 与hover:bg-primary/10一致的蓝色半透明 */
  --sidebar-accent-foreground: #1e88e5; /* 蓝色文本 */
  --sidebar-border: transparent; /* 透明边框 */
  --sidebar-ring: oklch(0.708 0 0);
  --chart-1: 221.2 83.2% 53.3%;
  --chart-2: 212 95% 68%;
  --chart-3: 216 92% 60%;
  --chart-4: 210 98% 78%;
  --chart-5: 212 97% 87%;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: #1565c0; /* 深蓝色主题色 */
  --primary-foreground: #ffffff; /* 白色文本 */
  --primary-80: rgba(21, 101, 192, 0.8);
  --primary-60: rgba(21, 101, 192, 0.6);
  --primary-40: rgba(21, 101, 192, 0.4);
  --primary-20: rgba(21, 101, 192, 0.2);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: #1a202c; /* 深色侧边栏背景 */
  --sidebar-foreground: #f7fafc; /* 浅色文本 */
  --sidebar-primary: #2196f3; /* 亮蓝色主题色 */
  --sidebar-primary-foreground: #ffffff; /* 白色文本 */
  --sidebar-accent: rgba(33, 150, 243, 0.1); /* 与hover:bg-primary/10一致的蓝色半透明 */
  --sidebar-accent-foreground: #2196f3; /* 蓝色文本 */
  --sidebar-border: transparent;
  --sidebar-ring: oklch(0.556 0 0);
}

.user-message-content {
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.user-message-content a {
  color: #fff !important;
  text-decoration: underline;
}
.user-message-content a:hover {
  color: #dbeafe !important;
}

/* 蓝色主题 */
.theme-blue {
  --background: #ffffff;
  --foreground: #020817;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --popover: #fff;
  --popover-foreground: #020817;
  --card: #fff;
  --card-foreground: #020817;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;
  --primary: #2563eb;
  --primary-foreground: #f8fafc;
  --primary-80: rgba(37, 99, 235, 0.8);
  --primary-60: rgba(37, 99, 235, 0.6);
  --primary-40: rgba(37, 99, 235, 0.4);
  --primary-20: rgba(37, 99, 235, 0.2);
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --ring: #2563eb;
  --default-50: #f8fafc;
  --default-100: #f1f5f9;
  --default-200: #e2e8f0;
  --default-300: #cbd5e1;
  --default-400: #94a3b8;
  --default-500: #64748b;
  --default-600: #475569;
  --default-700: #334155;
  --default-800: #1e293b;
  --default-900: #0f172a;
  --default-950: #020817;
  --sidebar: #ffffff;
  --sidebar-foreground: #334155;
  --sidebar-primary: #2563eb;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(37, 99, 235, 0.1);
  --sidebar-accent-foreground: #2563eb;
  --sidebar-border: transparent;
  --sidebar-ring: #2563eb;
}

/* 绿色主题 */
.theme-green {
  --background: #ffffff;
  --foreground: #020817;
  --muted: #f0fdf4;
  --muted-foreground: #64748b;
  --popover: #fff;
  --popover-foreground: #020817;
  --card: #fff;
  --card-foreground: #020817;
  --border: #dcfce7;
  --input: #dcfce7;
  --primary: #16a34a;
  --primary-foreground: #f0fdf4;
  --primary-80: rgba(22, 163, 74, 0.8);
  --primary-60: rgba(22, 163, 74, 0.6);
  --primary-40: rgba(22, 163, 74, 0.4);
  --primary-20: rgba(22, 163, 74, 0.2);
  --secondary: #f0fdf4;
  --secondary-foreground: #0f172a;
  --accent: #f0fdf4;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --ring: #16a34a;
  --sidebar: #ffffff;
  --sidebar-foreground: #334155;
  --sidebar-primary: #16a34a;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(22, 163, 74, 0.1);
  --sidebar-accent-foreground: #16a34a;
  --sidebar-border: transparent;
  --sidebar-ring: #16a34a;
}

/* 紫色主题 */
.theme-purple {
  --background: #ffffff;
  --foreground: #020817;
  --muted: #faf5ff;
  --muted-foreground: #64748b;
  --popover: #fff;
  --popover-foreground: #020817;
  --card: #fff;
  --card-foreground: #020817;
  --border: #e9d5ff;
  --input: #e9d5ff;
  --primary: #9333ea;
  --primary-foreground: #faf5ff;
  --primary-80: rgba(147, 51, 234, 0.8);
  --primary-60: rgba(147, 51, 234, 0.6);
  --primary-40: rgba(147, 51, 234, 0.4);
  --primary-20: rgba(147, 51, 234, 0.2);
  --secondary: #faf5ff;
  --secondary-foreground: #0f172a;
  --accent: #faf5ff;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --ring: #9333ea;
  --sidebar: #ffffff;
  --sidebar-foreground: #334155;
  --sidebar-primary: #9333ea;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(147, 51, 234, 0.1);
  --sidebar-accent-foreground: #9333ea;
  --sidebar-border: transparent;
  --sidebar-ring: #9333ea;
}

/* 橙色主题 */
.theme-orange {
  --background: #ffffff;
  --foreground: #020817;
  --muted: #fff7ed;
  --muted-foreground: #64748b;
  --popover: #fff;
  --popover-foreground: #020817;
  --card: #fff;
  --card-foreground: #020817;
  --border: #fed7aa;
  --input: #fed7aa;
  --primary: #ea580c;
  --primary-foreground: #fff7ed;
  --primary-80: rgba(234, 88, 12, 0.8);
  --primary-60: rgba(234, 88, 12, 0.6);
  --primary-40: rgba(234, 88, 12, 0.4);
  --primary-20: rgba(234, 88, 12, 0.2);
  --secondary: #fff7ed;
  --secondary-foreground: #0f172a;
  --accent: #fff7ed;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --ring: #ea580c;
  --sidebar: #ffffff;
  --sidebar-foreground: #334155;
  --sidebar-primary: #ea580c;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(234, 88, 12, 0.1);
  --sidebar-accent-foreground: #ea580c;
  --sidebar-border: transparent;
  --sidebar-ring: #ea580c;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 强制移除项目选择器的hover效果 */
@layer utilities {
  .no-hover-effect:hover {
    background-color: transparent !important;
    color: currentColor !important;
  }

  /* 确保图标使用主题色 */
  .force-primary-color {
    color: var(--primary) !important;
  }

  /* 左侧垂直菜单强烈阴影效果 - 像header一样突出 */
  .sidebar-shadow {
    box-shadow:
      0 0 0 1px rgba(0, 0, 0, 0.05),
      2px 0 10px rgba(0, 0, 0, 0.1),
      4px 0 20px rgba(0, 0, 0, 0.08),
      8px 0 40px rgba(0, 0, 0, 0.05) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }

  /* 深色模式下的强烈阴影效果 */
  .dark .sidebar-shadow {
    box-shadow:
      0 0 0 1px rgba(255, 255, 255, 0.1),
      2px 0 10px rgba(0, 0, 0, 0.4),
      4px 0 20px rgba(0, 0, 0, 0.3),
      8px 0 40px rgba(0, 0, 0, 0.2) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
}
