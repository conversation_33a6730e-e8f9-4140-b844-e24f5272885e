# Docker 部署指南

本项目提供了完整的Docker配置，支持开发和生产环境部署。

## 文件说明

- `Dockerfile` - 生产环境构建文件
- `Dockerfile.dev` - 开发环境构建文件
- `docker-compose.yml` - 生产环境编排文件
- `docker-compose.dev.yml` - 开发环境编排文件
- `.dockerignore` - Docker构建忽略文件

## 快速开始

### 生产环境部署

1. **构建并启动服务**
   ```bash
   docker-compose up --build
   ```

2. **后台运行**
   ```bash
   docker-compose up -d --build
   ```

3. **停止服务**
   ```bash
   docker-compose down
   ```

### 开发环境部署

1. **启动开发环境**
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

2. **后台运行**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d --build
   ```

3. **停止服务**
   ```bash
   docker-compose -f docker-compose.dev.yml down
   ```

## 环境变量配置

### 必需的环境变量

- `NEXTAUTH_SECRET` - NextAuth.js 密钥
- `NEXTAUTH_URL` - 应用URL
- `DB_PROVIDER` - 数据库类型 (sqlite 或 postgres)
- `DATABASE_URL` - 数据库连接字符串

### 可选的环境变量

- `NODE_ENV` - 环境类型 (development/production)
- `NEXT_TELEMETRY_DISABLED` - 禁用Next.js遥测

## 数据库配置

### SQLite (默认)

项目默认使用SQLite数据库，数据文件存储在容器内的 `/app/data.db`。

### PostgreSQL

如果需要使用PostgreSQL，请：

1. 修改 `docker-compose.yml` 中的环境变量：
   ```yaml
   environment:
     - DB_PROVIDER=postgres
     - DATABASE_URL=**************************************/ai_run
   ```

2. 确保PostgreSQL服务已启动

## 数据持久化

### 生产环境

- 数据库数据：存储在Docker卷中
- 应用数据：挂载到 `./data` 目录
- 日志文件：挂载到 `./logs` 目录

### 开发环境

- 源代码：实时同步到容器
- 依赖项：使用卷缓存以提高性能

## 服务端口

- **应用服务**: 3000
- **PostgreSQL**: 5432 (可选)
- **Redis**: 6379 (可选)

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   ```

2. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   ```

3. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   ```

### 日志查看

```bash
# 查看应用日志
docker-compose logs app

# 查看所有服务日志
docker-compose logs

# 实时查看日志
docker-compose logs -f app
```

## 性能优化

### 生产环境

- 使用多阶段构建减少镜像大小
- 启用Next.js standalone输出
- 使用Alpine Linux基础镜像

### 开发环境

- 使用卷挂载实现热重载
- 缓存node_modules提高构建速度
- 使用开发模式优化调试体验

## 安全建议

1. **更改默认密码**
   - 修改PostgreSQL密码
   - 设置强密码的NEXTAUTH_SECRET

2. **网络安全**
   - 在生产环境中使用反向代理
   - 配置SSL/TLS证书
   - 限制数据库端口访问

3. **镜像安全**
   - 定期更新基础镜像
   - 扫描安全漏洞
   - 使用非root用户运行应用

## 扩展部署

### 使用Nginx反向代理

```yaml
# 在docker-compose.yml中添加
nginx:
  image: nginx:alpine
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
  depends_on:
    - app
```

### 使用Traefik

```yaml
# 在docker-compose.yml中添加标签
app:
  labels:
    - "traefik.enable=true"
    - "traefik.http.routers.app.rule=Host(`your-domain.com`)"
``` 