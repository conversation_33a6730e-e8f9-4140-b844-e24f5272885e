#!/usr/bin/env node

/**
 * 测试自动化配置功能的脚本
 * 验证数据库中的自动化配置数据是否正确
 */

const path = require('path');
const Database = require('better-sqlite3');

// 数据库路径
const dbPath = process.env.SQLITE_PATH || path.join(__dirname, '../data/sqlite.db');

console.log('🔍 测试自动化配置功能...');
console.log(`📁 数据库路径: ${dbPath}`);

try {
  const db = new Database(dbPath);
  
  // 检查自动化配置表是否存在
  const tableExists = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='automationconfig'
  `).get();
  
  if (!tableExists) {
    console.error('❌ automationconfig 表不存在');
    process.exit(1);
  }
  
  console.log('✅ automationconfig 表存在');
  
  // 查询所有自动化配置
  const configs = db.prepare(`
    SELECT ac.*, tc.name as testCaseName 
    FROM automationconfig ac
    LEFT JOIN testcase tc ON ac.testCaseId = tc.id
    ORDER BY ac.createdAt DESC
  `).all();
  
  console.log(`\n📊 找到 ${configs.length} 个自动化配置:`);
  
  configs.forEach((config, index) => {
    console.log(`\n${index + 1}. ${config.testCaseName || 'Unknown Test Case'}`);
    console.log(`   🔧 框架: ${config.framework}`);
    console.log(`   🌐 浏览器: ${config.browser}`);
    console.log(`   🏗️ 环境: ${config.environment}`);
    console.log(`   📂 仓库: ${config.repository}`);
    console.log(`   🌿 分支: ${config.branch}`);
    console.log(`   ⚡ 状态: ${config.isActive ? '激活' : '未激活'}`);
    
    // 解析命令
    try {
      const commands = JSON.parse(config.commands);
      console.log(`   📝 命令 (${commands.length}个):`);
      commands.forEach((cmd, i) => {
        console.log(`      ${i + 1}. ${cmd}`);
      });
    } catch (e) {
      console.log(`   📝 命令: 解析失败`);
    }
    
    // 解析参数
    try {
      const parameters = JSON.parse(config.parameters);
      const paramCount = Object.keys(parameters).length;
      console.log(`   ⚙️ 参数 (${paramCount}个):`);
      Object.entries(parameters).forEach(([key, value]) => {
        console.log(`      ${key}: ${value}`);
      });
    } catch (e) {
      console.log(`   ⚙️ 参数: 解析失败`);
    }
  });
  
  // 统计不同框架的数量
  const frameworkStats = db.prepare(`
    SELECT framework, COUNT(*) as count
    FROM automationconfig
    GROUP BY framework
    ORDER BY count DESC
  `).all();
  
  console.log(`\n📈 框架统计:`);
  frameworkStats.forEach(stat => {
    console.log(`   ${stat.framework}: ${stat.count} 个配置`);
  });
  
  // 检查测试用例关联
  const testCaseStats = db.prepare(`
    SELECT 
      COUNT(DISTINCT tc.id) as totalTestCases,
      COUNT(DISTINCT ac.testCaseId) as configuredTestCases
    FROM testcase tc
    LEFT JOIN automationconfig ac ON tc.id = ac.testCaseId
  `).get();
  
  console.log(`\n📋 测试用例统计:`);
  console.log(`   总测试用例: ${testCaseStats.totalTestCases}`);
  console.log(`   已配置自动化: ${testCaseStats.configuredTestCases}`);
  console.log(`   配置覆盖率: ${((testCaseStats.configuredTestCases / testCaseStats.totalTestCases) * 100).toFixed(1)}%`);
  
  // 验证数据完整性
  const invalidConfigs = db.prepare(`
    SELECT id, testCaseId, framework
    FROM automationconfig
    WHERE repository IS NULL 
       OR repository = ''
       OR commands IS NULL
       OR commands = ''
       OR framework NOT IN ('midscene', 'playwright', 'cypress', 'selenium')
  `).all();
  
  if (invalidConfigs.length > 0) {
    console.log(`\n⚠️ 发现 ${invalidConfigs.length} 个无效配置:`);
    invalidConfigs.forEach(config => {
      console.log(`   ID: ${config.id}, 测试用例: ${config.testCaseId}, 框架: ${config.framework}`);
    });
  } else {
    console.log(`\n✅ 所有配置数据完整性验证通过`);
  }
  
  db.close();
  console.log(`\n🎉 自动化配置功能测试完成!`);
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
}
