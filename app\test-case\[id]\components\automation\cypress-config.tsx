'use client';

import { CheckCircle } from 'lucide-react';
import BaseAutomationConfig from './base-automation-config';
import { AutomationConfig } from '../../types';

interface CypressConfigProps {
  config?: AutomationConfig;
  onEdit?: () => void;
  onDelete?: () => void;
  onCreate?: () => void;
  onRun?: () => void;
}

export default function CypressConfig({
  config,
  onEdit,
  onDelete,
  onCreate,
  onRun
}: CypressConfigProps) {
  return (
    <BaseAutomationConfig
      config={config}
      framework="cypress"
      frameworkIcon={CheckCircle}
      frameworkColor="text-blue-600"
      frameworkBg="bg-blue-100"
      frameworkBorder="border-blue-200"
      onEdit={onEdit}
      onDelete={onDelete}
      onCreate={onCreate}
      onRun={onRun}
    />
  );
}
