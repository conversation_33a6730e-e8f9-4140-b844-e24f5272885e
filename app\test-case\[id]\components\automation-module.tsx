'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, Settings, RefreshCw } from 'lucide-react';
import { ModuleProps, AutomationConfig } from '../types';
import MidsceneConfig from './automation/midscene-config';
import PlaywrightConfig from './automation/playwright-config';
import CypressConfig from './automation/cypress-config';
import SeleniumConfig from './automation/selenium-config';

export default function AutomationModule({
  testCase,
  isEditing,
  onUpdate,
  onAIGenerate
}: ModuleProps) {
  const [automationConfigs, setAutomationConfigs] = useState<Record<string, AutomationConfig>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载自动化配置
  const loadAutomationConfigs = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/automation-config?testCaseId=${testCase.id}`);
      if (!response.ok) {
        throw new Error('Failed to load automation configs');
      }

      const data = await response.json();

      // 处理不同的返回格式
      if (data && typeof data === 'object') {
        if (Array.isArray(data)) {
          // 如果返回的是数组格式
          const configMap: Record<string, AutomationConfig> = {};
          data.forEach((config: AutomationConfig) => {
            configMap[config.framework] = config;
          });
          setAutomationConfigs(configMap);
        } else if (data.error) {
          // 如果返回的是错误对象
          throw new Error(data.error);
        } else {
          // 如果返回的是以framework为key的对象格式
          setAutomationConfigs(data);
        }
      } else {
        // 其他情况，设置为空对象
        setAutomationConfigs({});
      }
    } catch (err) {
      console.error('Error loading automation configs:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载配置
  useEffect(() => {
    loadAutomationConfigs();
  }, [testCase.id]);

  // 处理各种操作
  const handleEdit = (framework: string) => {
    console.log(`Edit ${framework} config`);
  };



  const handleCreate = (framework: string) => {
    console.log(`Create ${framework} config`);
  };

  const handleRun = (framework: string) => {
    console.log(`Run ${framework} test`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-purple-100 border-purple-200 border">
            <Bot className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">Automation Configurations</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Manage automation configurations for different frameworks
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadAutomationConfigs}
            disabled={loading}
            className="border-slate-200 hover:bg-slate-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onAIGenerate}
            className="border-purple-200 hover:bg-purple-50"
          >
            <Bot className="w-4 h-4 mr-2" />
            AI Configure
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-slate-200 hover:bg-slate-50"
          >
            <Settings className="w-4 h-4 mr-2" />
            Manage All
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-slate-400 mr-2" />
          <span className="text-slate-600 dark:text-slate-400">Loading automation configurations...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">
            Error loading automation configurations: {error}
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={loadAutomationConfigs}
            className="mt-2"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      )}

      {/* Framework Configurations */}
      {!loading && !error && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Midscene Configuration */}
          <MidsceneConfig
            config={automationConfigs.midscene}
            onEdit={() => handleEdit('midscene')}
            onCreate={() => handleCreate('midscene')}
            onRun={() => handleRun('midscene')}
          />

          {/* Playwright Configuration */}
          <PlaywrightConfig
            config={automationConfigs.playwright}
            onEdit={() => handleEdit('playwright')}
            onCreate={() => handleCreate('playwright')}
            onRun={() => handleRun('playwright')}
          />

          {/* Cypress Configuration */}
          <CypressConfig
            config={automationConfigs.cypress}
            onEdit={() => handleEdit('cypress')}
            onCreate={() => handleCreate('cypress')}
            onRun={() => handleRun('cypress')}
          />

          {/* Selenium Configuration */}
          <SeleniumConfig
            config={automationConfigs.selenium}
            onEdit={() => handleEdit('selenium')}
            onCreate={() => handleCreate('selenium')}
            onRun={() => handleRun('selenium')}
          />
        </div>
      )}
    </div>
  );
}
