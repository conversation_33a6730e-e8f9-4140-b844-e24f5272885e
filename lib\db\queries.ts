import 'server-only';

import {
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  inArray,
  lt,
  type SQL,
} from 'drizzle-orm';
import { db } from './index';

import {
  chat,
  document,
  message,
  suggestion,
  user,
  vote,
  stream,
  // 测试用例相关表
  folder,
  testCase,
  testStep,
  automationConfig,
  relatedRequirement,
  dataset,
  testRun,
  knownIssue,
  testCaseTag,
  testCaseTagRelation,
  testCaseComment,
  testCaseHistory,
  type DBMessage,
  type User,
  type Chat,
  type Suggestion,
  type Folder,
  type TestCase,
  type TestStep,
  type AutomationConfig,
  type RelatedRequirement,
  type Dataset,
  type TestRun,
  type KnownIssue,
  type TestCaseTag,
  type TestCaseTagRelation,
  type TestCaseComment,
  type TestCaseHistory,
} from './schema';
import { ArtifactKind } from '@/components/chat/artifact';
import { ChatSDKError } from '@/lib/errors';
import { generateUUID } from '@/lib/utils';
import { generateHashedPassword } from './utils';
import type { VisibilityType } from '@/components/chat/visibility-selector';
import { dbLogger } from '@/lib/logger';
import {MessagePart} from '../types'
import { MidsceneReportType, MIDSCENE_REPORT } from '@/artifacts/types';

// 创建模块专用的日志记录器
const logger = dbLogger.child('queries');

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    logger.error(`获取用户失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get user by email',
    );
  }
}

export async function createUser(email: string, password: string) {
  const hashedPassword = generateHashedPassword(password);

  try {
    logger.info(`创建用户: ${email}`);
    return await db.insert(user).values({ email, password: hashedPassword });
  } catch (error) {
    logger.error(`创建用户失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to create user');
  }
}

export async function createGuestUser() {
  const email = `guest-${Date.now()}`;
  const password = generateHashedPassword(generateUUID());
  const id = generateUUID();

  try {
    logger.info(`尝试创建访客用户: ${email}`);
    
    // 检查数据库连接
    try {
      const testQuery = await db.select({ count: count() }).from(user);
      logger.debug(`数据库连接测试: ${JSON.stringify(testQuery)}`);
    } catch (dbError) {
      logger.error(`数据库连接测试失败: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
      throw new Error(`数据库连接失败: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
    }
    
    // 创建用户
    const result = await db.insert(user).values({ 
      id,
      email, 
      password 
    }).returning({
      id: user.id,
      email: user.email,
    });
    
    logger.info(`访客用户创建成功: ${JSON.stringify(result)}`);
    return result;
  } catch (error) {
    logger.error(`创建访客用户失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      `Failed to create guest user: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function saveChat({
  id,
  userId,
  title,
  visibility,
}: {
  id: string;
  userId: string;
  title: string;
  visibility: VisibilityType;
}) {
  try {
    logger.info(`尝试保存聊天记录: ID=${id}, 用户=${userId}, 标题=${title}, 可见性=${visibility}`);
    // 兼容 sqlite 和 postgresql
    const isSqlite = process.env.DB_PROVIDER === 'sqlite';
    const now = new Date();
    const createdAt = isSqlite ? Math.floor(now.getTime() / 1000) : now;
    return await db.insert(chat).values({
      id,
      createdAt,
      userId,
      title,
      visibility,
    });
  } catch (error) {
    logger.error(`保存聊天记录失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to save chat');
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    logger.info(`删除聊天记录: ID=${id}`);
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));
    await db.delete(stream).where(eq(stream.chatId, id));

    const [chatsDeleted] = await db
      .delete(chat)
      .where(eq(chat.id, id))
      .returning();
    return chatsDeleted;
  } catch (error) {
    logger.error(`删除聊天记录失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete chat by id',
    );
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    logger.debug(`获取用户聊天记录: 用户=${id}, 限制=${limit}, 开始=${startingAfter}, 结束=${endingBefore}`);
    const extendedLimit = limit + 1;

    const query = (whereCondition?: SQL<any>) =>
      db
        .select()
        .from(chat)
        .where(
          whereCondition
            ? and(whereCondition, eq(chat.userId, id))
            : eq(chat.userId, id),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);

    let filteredChats: Array<Chat> = [];

    if (startingAfter) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, startingAfter))
        .limit(1);

      if (!selectedChat) {
        logger.warn(`未找到ID为${startingAfter}的聊天记录`);
        throw new ChatSDKError(
          'not_found:database',
          `Chat with id ${startingAfter} not found`,
        );
      }

      filteredChats = await query(gt(chat.createdAt, selectedChat.createdAt));
    } else if (endingBefore) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, endingBefore))
        .limit(1);

      if (!selectedChat) {
        logger.warn(`未找到ID为${endingBefore}的聊天记录`);
        throw new ChatSDKError(
          'not_found:database',
          `Chat with id ${endingBefore} not found`,
        );
      }

      filteredChats = await query(lt(chat.createdAt, selectedChat.createdAt));
    } else {
      filteredChats = await query();
    }

    const hasMore = filteredChats.length > limit;
    logger.debug(`获取到${filteredChats.length}条聊天记录, 是否有更多: ${hasMore}`);

    return {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };
  } catch (error) {
    logger.error(`获取用户聊天记录失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get chats by user id',
    );
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    logger.debug(`获取聊天记录: ID=${id}`);
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    logger.error(`获取聊天记录失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    const isSqlite = process.env.DB_PROVIDER === 'sqlite';
    // 使用显式类型注解
    const patchedMessages = messages.map((msg) => {
      // 显式指定返回类型
      const now = msg.createdAt instanceof Date ? msg.createdAt : new Date();
      // SQLite: 强制 parts/attachments 为字符串
      return {
        ...msg,
        createdAt: isSqlite ? Math.floor(now.getTime() / 1000) : now,
        parts: isSqlite
          ? (typeof msg.parts === 'string' ? msg.parts : JSON.stringify(msg.parts))
          : msg.parts,
        attachments: isSqlite
          ? (typeof msg.attachments === 'string' ? msg.attachments : JSON.stringify(msg.attachments))
          : msg.attachments,
      };
    });
    // 打印最终插入内容，便于调试
    logger.debug(`准备插入消息: ${messages.map(m => m.id).join(', ')}`);
    return await db.insert(message).values(patchedMessages);
  } catch (error) {
    logger.error(`保存消息失败: ${error instanceof Error ? error.message : String(error)}`);
    logger.debug(`错误堆栈: ${error instanceof Error ? error.stack : '无堆栈信息'}`);
    throw new ChatSDKError('bad_request:database', 'Failed to save messages');
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    logger.debug(`获取聊天消息: chatId=${id}`);
    const messages = await db.select().from(message).where(eq(message.chatId, id));
    // 修正：确保 parts/attachments 为对象
    return messages.map((msg: any) => ({
      ...msg,
      parts: typeof msg.parts === 'string' ? JSON.parse(msg.parts) : msg.parts,
      attachments: typeof msg.attachments === 'string' ? JSON.parse(msg.attachments) : msg.attachments,
    }));
  } catch (error) {
    logger.error(`获取聊天消息失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get messages by chat id');
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    logger.debug(`投票消息: chatId=${chatId}, messageId=${messageId}, 类型=${type}`);
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      logger.debug(`更新现有投票: messageId=${messageId}`);
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    logger.debug(`创建新投票: messageId=${messageId}`);
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    logger.error(`投票消息失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to vote message');
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    logger.debug(`获取聊天投票: chatId=${id}`);
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    logger.error(`获取聊天投票失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get votes by chat id',
    );
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
  chatId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
  chatId?: string;
}) {
  try {
    logger.info(`保存文档: ID=${id}, 标题=${title}, 类型=${kind}, 用户=${userId}`);
    
    // 确保参数有效
    if (!id || !title || !kind || !userId) {
      logger.error('保存文档失败: 参数无效', { id, title, kind, userId });
      throw new Error('无效的文档参数');
    }
    
    // 创建日期对象并处理SQLite日期格式问题
    const now = new Date();
    // 根据数据库类型选择合适的时间格式
    const createdAt = process.env.DB_PROVIDER === 'sqlite' 
      ? Math.floor(now.getTime() / 1000) // SQLite存储为Unix时间戳(秒)
      : now;
    
    // 执行数据库插入
    const result = await db
      .insert(document)
      .values({
        id,
        title,
        kind,
        content,
        userId,
        createdAt,
      })
      .returning();
    
    // 如果提供了chatId，保存包含工具调用的消息
    if (chatId) {
      // 优先使用kind字段判断文档类型，只有在kind不明确时才使用标题判断
      const isTestingDoc = kind === MIDSCENE_REPORT;
      const toolName = isTestingDoc ? 'executeAutomationTesting' : 'createDocument';
      
      // 检查是否包含报告URL
      let reportUrl = null;
      if (isTestingDoc && typeof content === 'string') {
        // 尝试多种方式提取报告URL
        // 1. 从Markdown图片链接中提取
        const imgMatch = content.match(/\!\[.*?\]\((\/report\/[^)]+\.html)\)/);
        if (imgMatch) {
          reportUrl = imgMatch[1];
        } 
        // 2. 从reportUrl字段中提取
        else {
          const reportUrlMatch = content.match(/reportUrl:\s*(\S+report\/[^,\s\n]+\.html)/);
          if (reportUrlMatch) {
            reportUrl = reportUrlMatch[1];
          }
          // 3. 从内容中的任何位置查找report路径
          else {
            const anyReportMatch = content.match(/(\/report\/[^,\s\n"']+\.html)/);
            if (anyReportMatch) {
              reportUrl = anyReportMatch[1];
            }
          }
        }
        
        // 4. 尝试解析JSON内容
        if (!reportUrl) {
          try {
            // 尝试解析JSON
            const contentObj = JSON.parse(content);
            if (contentObj.report_link) {
              reportUrl = contentObj.report_link;
            } else if (contentObj.report_url) {
              reportUrl = contentObj.report_url;
            }
          } catch (e) {
            // 解析失败，继续使用其他方法
          }
        }
      }
      
      logger.info(`提取的报告URL: ${reportUrl || '未找到'}`);
      
      const parts:MessagePart[] = [
        { type: 'text', text: `我已经创建了一个${isTestingDoc ? '测试' : ''}文档："${title}"` },
        { type: 'document-reference', title, document_id: id },
      ];
      if (isTestingDoc) {
        parts.push({
          type: MIDSCENE_REPORT,
          title,
          document_id: id
        });
      }
      parts.push({
        type: 'tool-invocation',
        toolInvocation: {
          toolName,
          toolCallId: generateUUID(),
          state: 'result',
          result: {
            id,
            title,
            kind,
            report_url: reportUrl,
            isVisible: true
          }
        }
      });

      const assistantMessage = {
        id: generateUUID(),
        chatId,
        role: 'assistant',
        parts: JSON.stringify(parts),
        attachments: JSON.stringify([]),
        createdAt: new Date(),
      };
      
      // 调试信息
      logger.debug(`准备保存消息，文档ID=${id}, 报告URL=${reportUrl || '未找到'}`);
      
      try {
        await saveMessages({ messages: [assistantMessage] });
        logger.info(`包含文档的AI消息已保存: messageId=${assistantMessage.id}`);
      } catch (err) {
        logger.error(`保存AI消息失败: ${err instanceof Error ? err.message : String(err)}`);
      }
    }
    
    logger.info(`文档保存成功: ID=${id}`);
    return result;
  } catch (error) {
    logger.error(`保存文档到数据库失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to save document');
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    logger.debug(`获取文档: ID=${id}`);
    const documents = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    // 处理时间戳
    return documents.map((doc: any) => {
      // 如果createdAt是数字（Unix时间戳），则转换为Date对象
      if (doc.createdAt && typeof doc.createdAt === 'number') {
        return {
          ...doc,
          createdAt: new Date(doc.createdAt * 1000) // 将秒转换为毫秒
        };
      }
      return doc;
    });
  } catch (error) {
    logger.error(`获取文档失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get documents by id',
    );
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    logger.debug(`获取单个文档: ID=${id}`);
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    // 处理时间戳
    if (selectedDocument && selectedDocument.createdAt && typeof selectedDocument.createdAt === 'number') {
      return {
        ...selectedDocument,
        createdAt: new Date(selectedDocument.createdAt * 1000) // 将秒转换为毫秒
      };
    }

    return selectedDocument;
  } catch (error) {
    logger.error(`获取单个文档失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get document by id',
    );
  }
}

export async function getDocuments({
  limit = 20,
  offset = 0,
  userId,
  kind,
  sortBy = 'createdAt',
  sortDirection = 'desc'
}: {
  limit?: number;
  offset?: number;
  userId?: string;
  kind?: ArtifactKind;
  sortBy?: 'createdAt' | 'title';
  sortDirection?: 'asc' | 'desc';
}) {
  try {
    logger.debug(`获取文档列表: limit=${limit}, offset=${offset}, userId=${userId}, kind=${kind}, sortBy=${sortBy}, sortDirection=${sortDirection}`);
    
    // 构建查询条件
    let whereConditions = [];
    if (userId) {
      whereConditions.push(eq(document.userId, userId));
    }
    if (kind) {
      whereConditions.push(eq(document.kind, kind));
    }
    
    // 构建排序条件
    let orderByClause;
    if (sortBy === 'title') {
      orderByClause = sortDirection === 'asc' ? asc(document.title) : desc(document.title);
    } else {
      orderByClause = sortDirection === 'asc' ? asc(document.createdAt) : desc(document.createdAt);
    }
    
    // 执行查询
    const documents = await db
      .select()
      .from(document)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);
    
    // 获取总数
    const [countResult] = await db
      .select({ count: count() })
      .from(document)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);
    
    const total = countResult?.count || 0;
    
    logger.info(`获取到${documents.length}个文档，总数：${total}`);
    
    // 处理时间戳
    const processedDocuments = documents.map((doc: any) => {
      // 如果createdAt是数字（Unix时间戳），则转换为Date对象
      if (doc.createdAt && typeof doc.createdAt === 'number') {
        return {
          ...doc,
          createdAt: new Date(doc.createdAt * 1000) // 将秒转换为毫秒
        };
      }
      return doc;
    });
    
    return {
      documents: processedDocuments,
      total,
      limit,
      offset,
      hasMore: offset + documents.length < total
    };
  } catch (error) {
    logger.error(`获取文档列表失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get documents list',
    );
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    logger.info(`删除文档: ID=${id}, 时间戳=${timestamp}`);
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)))
      .returning();
  } catch (error) {
    logger.error(`删除文档失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete documents by id after timestamp',
    );
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    logger.info(`保存建议: 数量=${suggestions.length}`);
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    logger.error(`保存建议失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to save suggestions',
    );
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    logger.debug(`获取文档建议: documentId=${documentId}`);
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    logger.error(`获取文档建议失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get suggestions by document id',
    );
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    logger.debug(`获取消息: ID=${id}`);
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    logger.error(`获取消息失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message by id',
    );
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    logger.info(`删除消息: chatId=${chatId}, 时间戳=${timestamp}`);
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );

    const messageIds = messagesToDelete.map((message: { id: string }) => message.id);

    if (messageIds.length > 0) {
      logger.debug(`找到${messageIds.length}条要删除的消息`);
      await db
        .delete(vote)
        .where(
          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),
        );

      return await db
        .delete(message)
        .where(
          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),
        );
    }
    logger.debug('没有找到要删除的消息');
    return [];
  } catch (error) {
    logger.error(`删除消息失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete messages by chat id after timestamp',
    );
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    logger.info(`更新聊天可见性: chatId=${chatId}, 可见性=${visibility}`);
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    logger.error(`更新聊天可见性失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to update chat visibility by id',
    );
  }
}

export async function getMessageCountByUserId({
  id,
  differenceInHours,
}: { id: string; differenceInHours: number }) {
  try {
    logger.debug(`获取用户消息计数: 用户=${id}, 小时差=${differenceInHours}`);
    const twentyFourHoursAgo = new Date(
      Date.now() - differenceInHours * 60 * 60 * 1000,
    );

    const [stats] = await db
      .select({ count: count(message.id) })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .where(
        and(
          eq(chat.userId, id),
          gte(message.createdAt, twentyFourHoursAgo),
          eq(message.role, 'user'),
        ),
      )
      .execute();

    return stats?.count ?? 0;
  } catch (error) {
    logger.error(`获取用户消息计数失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message count by user id',
    );
  }
}

export async function createStreamId({
  streamId,
  chatId,
}: {
  streamId: string;
  chatId: string;
}) {
  try {
    logger.debug(`创建流ID: streamId=${streamId}, chatId=${chatId}`);
    await db
      .insert(stream)
      .values({ id: streamId, chatId, createdAt: new Date() });
  } catch (error) {
    logger.error(`创建流ID失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to create stream id',
    );
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    logger.debug(`获取聊天流IDs: chatId=${chatId}`);
    const streamIds = await db
      .select({ id: stream.id })
      .from(stream)
      .where(eq(stream.chatId, chatId))
      .orderBy(asc(stream.createdAt))
      .execute();

    return streamIds.map(({ id }: { id: string }) => id);
  } catch (error) {
    logger.error(`获取聊天流IDs失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get stream ids by chat id',
    );
  }
}

/**
 * 获取四种文档类型的数量统计
 * @returns {Promise<{ text: number; code: number; image: number; sheet: number }>}
 */
export async function getDocumentTypeCounts() {
  try {
    // 统计每种类型的数量
    const kinds = ['text', 'code', 'image', 'sheet'] as const;
    const counts: Record<string, number> = {};
    for (const kind of kinds) {
      const [result] = await db
        .select({ count: count() })
        .from(document)
        .where(eq(document.kind, kind));
      counts[kind] = Number(result?.count ?? 0);
    }
    return {
      text: counts['text'] ?? 0,
      code: counts['code'] ?? 0,
      image: counts['image'] ?? 0,
      sheet: counts['sheet'] ?? 0,
    };
  } catch (error) {
    logger.error(`获取文档类型数量失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get document type counts');
  }
}

// ==================== 测试用例管理系统查询函数 ====================

// 文件夹相关查询
export async function getFolders(parentId?: string): Promise<Array<Folder>> {
  try {
    const whereClause = parentId ? eq(folder.parentId, parentId) : eq(folder.parentId, null);
    return await db
      .select()
      .from(folder)
      .where(whereClause)
      .orderBy(asc(folder.sortOrder), asc(folder.name));
  } catch (error) {
    logger.error(`获取文件夹失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get folders');
  }
}

export async function createFolder(data: {
  name: string;
  description?: string;
  parentId?: string;
  createdBy: string;
}): Promise<Folder> {
  try {
    const id = generateUUID();
    const now = new Date();

    // 计算路径和层级
    let path = `/${data.name}`;
    let level = 0;

    if (data.parentId) {
      const parent = await db.select().from(folder).where(eq(folder.id, data.parentId)).limit(1);
      if (parent.length > 0) {
        path = `${parent[0].path}/${data.name}`;
        level = parent[0].level + 1;
      }
    }

    const newFolder: typeof folder.$inferInsert = {
      id,
      name: data.name,
      description: data.description || null,
      parentId: data.parentId || null,
      path,
      level,
      sortOrder: 0,
      createdAt: now.getTime(),
      updatedAt: now.getTime(),
      createdBy: data.createdBy,
      updatedBy: data.createdBy,
    };

    await db.insert(folder).values(newFolder);
    return newFolder as Folder;
  } catch (error) {
    logger.error(`创建文件夹失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to create folder');
  }
}

// 测试用例相关查询
export async function getTestCases(folderId?: string): Promise<Array<TestCase>> {
  try {
    const whereClause = folderId ? eq(testCase.folderId, folderId) : undefined;
    return await db
      .select()
      .from(testCase)
      .where(whereClause)
      .orderBy(desc(testCase.updatedAt));
  } catch (error) {
    logger.error(`获取测试用例失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get test cases');
  }
}

export async function getTestCaseById(id: string): Promise<TestCase | null> {
  try {
    const result = await db.select().from(testCase).where(eq(testCase.id, id)).limit(1);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    logger.error(`获取测试用例详情失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get test case');
  }
}

export async function createTestCase(data: {
  folderId?: string;
  name: string;
  description: string;
  preconditions?: string;
  priority?: 'high' | 'medium' | 'low';
  status?: 'work-in-progress' | 'active' | 'deprecated' | 'draft';
  weight?: 'high' | 'medium' | 'low';
  format?: 'classic' | 'bdd' | 'exploratory';
  nature?: 'functional' | 'performance' | 'security' | 'usability';
  type?: 'regression' | 'smoke' | 'integration' | 'unit';
  tags?: string[];
  createdBy: string;
}): Promise<TestCase> {
  try {
    const id = generateUUID();
    const now = new Date();

    const newTestCase: typeof testCase.$inferInsert = {
      id,
      folderId: data.folderId || null,
      name: data.name,
      description: data.description,
      preconditions: data.preconditions || null,
      priority: data.priority || 'medium',
      status: data.status || 'draft',
      weight: data.weight || 'medium',
      format: data.format || 'classic',
      nature: data.nature || 'functional',
      type: data.type || 'regression',
      tags: JSON.stringify(data.tags || []),
      executionTime: null,
      lastRunAt: null,
      createdAt: now.getTime(),
      updatedAt: now.getTime(),
      createdBy: data.createdBy,
      updatedBy: data.createdBy,
    };

    console.log('Creating test case with data:', newTestCase);
    await db.insert(testCase).values(newTestCase);
    return newTestCase as TestCase;
  } catch (error) {
    console.error('Detailed error:', error);
    console.error('Test case data:', newTestCase);
    logger.error(`创建测试用例失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to create test case');
  }
}

export async function updateTestCase(id: string, data: Partial<TestCase>, updatedBy: string): Promise<void> {
  try {
    const now = new Date();
    console.log('updateTestCase called with:', { id, data, updatedBy });

    // 简化版本：只更新基本字段，暂时不处理版本历史
    const allowedFields = [
      'folderId', 'name', 'description', 'preconditions', 'priority',
      'status', 'weight', 'format', 'nature', 'type', 'tags',
      'executionTime', 'lastRunAt'
    ];

    const filteredData: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (allowedFields.includes(key)) {
        // 特殊处理tags字段，确保它是JSON字符串
        if (key === 'tags' && Array.isArray(value)) {
          filteredData[key] = JSON.stringify(value);
        } else {
          filteredData[key] = value;
        }
      }
    }

    console.log('Filtered data for database update:', filteredData);

    // 只有在有字段需要更新时才执行数据库更新
    if (Object.keys(filteredData).length > 0) {
      const updateData = { ...filteredData, updatedAt: now.getTime(), updatedBy };
      console.log('Final update data:', updateData);

      await db
        .update(testCase)
        .set(updateData)
        .where(eq(testCase.id, id));

      console.log('Database update successful');
    }

    // 如果包含steps，单独处理测试步骤
    if (data.steps && Array.isArray(data.steps)) {
      console.log('Processing steps in updateTestCase:', data.steps);
      console.log('Steps count:', data.steps.length);
      console.log('TestCase ID:', id);

      // 删除现有步骤
      console.log('Deleting existing steps for testCaseId:', id);
      await db.delete(testStep).where(eq(testStep.testCaseId, id));

      // 插入新步骤
      if (data.steps.length > 0) {
        const stepsToInsert = data.steps.map((step: any, index: number) => ({
          id: generateUUID(),
          testCaseId: id,
          stepNumber: index + 1,
          action: step.action || '',
          expected: step.expected || '',
          type: step.type || 'manual',
          notes: step.notes || '',
          createdAt: now.getTime(),
          updatedAt: now.getTime(),
        }));

        console.log('Steps to insert:', stepsToInsert);
        console.log('Inserting steps into database...');
        await db.insert(testStep).values(stepsToInsert);
        console.log('Steps inserted successfully');

        // 验证插入结果
        const insertedSteps = await db
          .select()
          .from(testStep)
          .where(eq(testStep.testCaseId, id))
          .orderBy(asc(testStep.stepNumber));
        console.log('Verification - inserted steps count:', insertedSteps.length);
        console.log('Verification - first inserted step:', insertedSteps[0]);
      }
    }

    logger.info(`测试用例更新成功: id=${id}, updatedBy=${updatedBy}`);
  } catch (error) {
    console.error('updateTestCase error:', error);
    logger.error(`更新测试用例失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to update test case');
  }
}

// 测试步骤相关查询
export async function getTestSteps(testCaseId: string): Promise<Array<TestStep>> {
  try {
    return await db
      .select()
      .from(testStep)
      .where(eq(testStep.testCaseId, testCaseId))
      .orderBy(asc(testStep.stepNumber));
  } catch (error) {
    logger.error(`获取测试步骤失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get test steps');
  }
}

export async function createTestSteps(testCaseId: string, steps: Array<{
  stepNumber: number;
  action: string;
  expected: string;
  type?: 'manual' | 'automated' | 'optional';
  notes?: string;
}>): Promise<void> {
  try {
    const now = new Date();

    // 先删除现有步骤
    await db.delete(testStep).where(eq(testStep.testCaseId, testCaseId));

    // 插入新步骤
    const newSteps = steps.map(step => ({
      id: generateUUID(),
      testCaseId,
      stepNumber: step.stepNumber,
      action: step.action,
      expected: step.expected,
      type: step.type || 'manual',
      notes: step.notes || null,
      createdAt: now.getTime(),
      updatedAt: now.getTime(),
    }));

    if (newSteps.length > 0) {
      await db.insert(testStep).values(newSteps);
    }
  } catch (error) {
    logger.error(`创建测试步骤失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to create test steps');
  }
}

// 自动化配置相关查询
export async function getAutomationConfig(testCaseId: string, framework?: string): Promise<AutomationConfig | null> {
  try {
    let whereConditions = [eq(automationConfig.testCaseId, testCaseId)];

    if (framework) {
      whereConditions.push(eq(automationConfig.framework, framework));
    }

    const result = await db
      .select()
      .from(automationConfig)
      .where(and(...whereConditions))
      .limit(1);

    return result.length > 0 ? result[0] : null;
  } catch (error) {
    logger.error(`获取自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get automation config');
  }
}

export async function getAllAutomationConfigs(testCaseId: string): Promise<AutomationConfig[]> {
  try {
    const result = await db
      .select()
      .from(automationConfig)
      .where(eq(automationConfig.testCaseId, testCaseId));
    return result;
  } catch (error) {
    logger.error(`获取所有自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get all automation configs');
  }
}

export async function createOrUpdateAutomationConfig(testCaseId: string, data: {
  repository: string;
  branch?: string;
  commands: string[];
  parameters?: Record<string, string>;
  framework: 'selenium' | 'playwright' | 'cypress' | 'midscene';
  browser?: 'chrome' | 'firefox' | 'safari' | 'edge';
  environment?: 'dev' | 'test' | 'staging' | 'prod';
  isActive?: boolean;
}): Promise<void> {
  try {

    const now = new Date();
    const existing = await getAutomationConfig(testCaseId, data.framework);

    if (existing) {
      console.log(`🔄 更新现有配置 - ID: ${existing.id}`);

      // 准备更新数据，添加详细调试
      const updateData = {
        repository: data.repository,
        branch: data.branch || 'main',
        commands: JSON.stringify(data.commands),
        parameters: JSON.stringify(data.parameters || {}),
        browser: data.browser || 'chrome',
        environment: data.environment || 'test',
        isActive: data.isActive !== undefined ? (data.isActive ? 1 : 0) : 1, // 转换为整数
        updatedAt: now.getTime(),
      };

      console.log(`🔍 更新数据详情:`);
      console.log(`  repository: ${typeof updateData.repository} = ${updateData.repository}`);
      console.log(`  branch: ${typeof updateData.branch} = ${updateData.branch}`);
      console.log(`  commands: ${typeof updateData.commands} = ${updateData.commands}`);
      console.log(`  parameters: ${typeof updateData.parameters} = ${updateData.parameters.substring(0, 100)}...`);
      console.log(`  browser: ${typeof updateData.browser} = ${updateData.browser}`);
      console.log(`  environment: ${typeof updateData.environment} = ${updateData.environment}`);
      console.log(`  isActive: ${typeof updateData.isActive} = ${updateData.isActive}`);
      console.log(`  updatedAt: ${typeof updateData.updatedAt} = ${updateData.updatedAt}`);

      await db
        .update(automationConfig)
        .set(updateData)
        .where(and(
          eq(automationConfig.testCaseId, testCaseId),
          eq(automationConfig.framework, data.framework)
        ));
    } else {
      const newId = generateUUID();
      await db.insert(automationConfig).values({
        id: newId,
        testCaseId,
        repository: data.repository,
        branch: data.branch || 'main',
        commands: JSON.stringify(data.commands),
        parameters: JSON.stringify(data.parameters || {}),
        framework: data.framework,
        browser: data.browser || 'chrome',
        environment: data.environment || 'test',
        isActive: data.isActive !== undefined ? (data.isActive ? 1 : 0) : 1, // 转换为整数
        createdAt: now.getTime(),
        updatedAt: now.getTime(),
      });
    }
    console.log(`🎉 createOrUpdateAutomationConfig 成功完成`);
  } catch (error) {
    console.error(`❌ createOrUpdateAutomationConfig 失败:`, error);
    logger.error(`创建或更新自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to create or update automation config');
  }
}

export async function deleteAutomationConfig(testCaseId: string, framework: string): Promise<void> {
  try {
    await db
      .delete(automationConfig)
      .where(
        and(
          eq(automationConfig.testCaseId, testCaseId),
          eq(automationConfig.framework, framework)
        )
      );
  } catch (error) {
    logger.error(`删除自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to delete automation config');
  }
}

// 获取完整的测试用例信息（包含所有关联数据）
export async function getCompleteTestCase(id: string): Promise<any | null> {
  try {
    // 获取基本测试用例信息
    const testCaseResult = await getTestCaseById(id);
    if (!testCaseResult) {
      return null;
    }

    // 获取测试步骤
    const steps = await getTestSteps(id);
    console.log('getCompleteTestCase - steps from database:', steps);
    console.log('getCompleteTestCase - steps count:', steps.length);

    // 获取自动化配置
    const automation = await getAutomationConfig(id);

    // 获取相关需求
    const requirements = await db
      .select()
      .from(relatedRequirement)
      .where(eq(relatedRequirement.testCaseId, id));

    // 获取数据集
    const datasets = await db
      .select()
      .from(dataset)
      .where(eq(dataset.testCaseId, id));

    // 获取测试运行记录
    const testRuns = await db
      .select()
      .from(testRun)
      .where(eq(testRun.testCaseId, id))
      .orderBy(desc(testRun.runDate))
      .limit(10);

    // 获取已知问题
    const knownIssues = await db
      .select()
      .from(knownIssue)
      .where(eq(knownIssue.testCaseId, id))
      .orderBy(desc(knownIssue.createdAt));

    // 组装完整的测试用例对象
    const completeTestCase = {
      ...testCaseResult,
      tags: JSON.parse(testCaseResult.tags as string || '[]'),
      steps: steps.map(step => ({
        id: step.id,
        step: step.stepNumber,
        action: step.action,
        expected: step.expected,
        type: step.type,
        notes: step.notes,
      })),
      automation: automation ? {
        repository: automation.repository,
        branch: automation.branch,
        commands: JSON.parse(automation.commands as string),
        parameters: JSON.parse(automation.parameters as string),
      } : undefined,
      relatedRequirements: requirements.map(req => ({
        id: req.requirementId,
        type: req.type,
        title: req.title,
        status: req.status,
        assignee: req.assignee,
        url: req.url,
      })),
      datasets: datasets.map(ds => ({
        id: ds.id,
        name: ds.name,
        columns: JSON.parse(ds.columns as string),
        data: JSON.parse(ds.data as string),
      })),
      testRuns: testRuns.map(run => ({
        id: run.id,
        runDate: new Date(run.runDate as number).toISOString(),
        status: run.status,
        duration: run.duration,
        environment: run.environment,
        executor: run.executor,
        results: JSON.parse(run.results as string),
      })),
      knownIssues: knownIssues.map(issue => ({
        id: issue.id,
        title: issue.title,
        description: issue.description,
        severity: issue.severity,
        status: issue.status,
        reporter: issue.reporter,
        assignee: issue.assignee,
        bugUrl: issue.bugUrl,
        createdAt: new Date(issue.createdAt as number).toISOString(),
      })),
    };

    console.log('getCompleteTestCase - returning data with steps:', completeTestCase.steps);
    console.log('getCompleteTestCase - final steps count:', completeTestCase.steps.length);
    return completeTestCase;
  } catch (error) {
    logger.error(`获取完整测试用例失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to get complete test case');
  }
}

// 删除测试用例
export async function deleteTestCase(id: string): Promise<void> {
  try {
    // 删除相关数据
    await db.delete(testStep).where(eq(testStep.testCaseId, id));
    await db.delete(automationConfig).where(eq(automationConfig.testCaseId, id));
    await db.delete(relatedRequirement).where(eq(relatedRequirement.testCaseId, id));
    await db.delete(dataset).where(eq(dataset.testCaseId, id));
    await db.delete(testRun).where(eq(testRun.testCaseId, id));
    await db.delete(knownIssue).where(eq(knownIssue.testCaseId, id));
    await db.delete(testCaseTagRelation).where(eq(testCaseTagRelation.testCaseId, id));
    await db.delete(testCaseComment).where(eq(testCaseComment.testCaseId, id));
    await db.delete(testCaseHistory).where(eq(testCaseHistory.testCaseId, id));

    // 删除主记录
    await db.delete(testCase).where(eq(testCase.id, id));
  } catch (error) {
    logger.error(`删除测试用例失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to delete test case');
  }
}
