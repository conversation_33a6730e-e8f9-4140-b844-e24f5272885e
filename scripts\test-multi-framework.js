#!/usr/bin/env node

/**
 * 测试多框架自动化配置功能
 * 验证每个测试用例的多框架配置是否正确
 */

const path = require('path');
const Database = require('better-sqlite3');

// 数据库路径
const dbPath = process.env.SQLITE_PATH || path.join(__dirname, '../data/sqlite.db');

console.log('🔍 测试多框架自动化配置功能...');
console.log(`📁 数据库路径: ${dbPath}`);

try {
  const db = new Database(dbPath);
  
  // 查询测试用例及其自动化配置
  const testCasesWithConfigs = db.prepare(`
    SELECT 
      tc.id,
      tc.name,
      ac.framework,
      ac.repository,
      ac.branch,
      ac.browser,
      ac.environment,
      ac.isActive,
      ac.commands,
      ac.parameters
    FROM testcase tc
    LEFT JOIN automationconfig ac ON tc.id = ac.testCaseId
    ORDER BY tc.id, ac.framework
  `).all();
  
  // 按测试用例分组
  const groupedByTestCase = {};
  testCasesWithConfigs.forEach(row => {
    if (!groupedByTestCase[row.id]) {
      groupedByTestCase[row.id] = {
        name: row.name,
        configs: []
      };
    }
    
    if (row.framework) {
      groupedByTestCase[row.id].configs.push({
        framework: row.framework,
        repository: row.repository,
        branch: row.branch,
        browser: row.browser,
        environment: row.environment,
        isActive: row.isActive,
        commands: row.commands,
        parameters: row.parameters
      });
    }
  });
  
  console.log(`\n📊 测试用例自动化配置概览:`);
  
  Object.entries(groupedByTestCase).forEach(([testCaseId, data]) => {
    console.log(`\n🧪 ${testCaseId}: ${data.name}`);
    
    if (data.configs.length === 0) {
      console.log(`   ❌ 无自动化配置`);
      return;
    }
    
    console.log(`   ✅ 配置了 ${data.configs.length} 个框架:`);
    
    data.configs.forEach(config => {
      const statusIcon = config.isActive ? '🟢' : '🔴';
      console.log(`   ${statusIcon} ${config.framework.toUpperCase()}`);
      console.log(`      🌐 浏览器: ${config.browser}`);
      console.log(`      🏗️ 环境: ${config.environment}`);
      console.log(`      🌿 分支: ${config.branch}`);
      console.log(`      📂 仓库: ${config.repository}`);
      
      // 解析命令
      try {
        const commands = JSON.parse(config.commands);
        console.log(`      📝 命令数量: ${commands.length}`);
      } catch (e) {
        console.log(`      📝 命令: 解析失败`);
      }
      
      // 解析参数
      try {
        const parameters = JSON.parse(config.parameters);
        const paramCount = Object.keys(parameters).length;
        console.log(`      ⚙️ 参数数量: ${paramCount}`);
      } catch (e) {
        console.log(`      ⚙️ 参数: 解析失败`);
      }
      
      console.log('');
    });
  });
  
  // 框架使用统计
  const frameworkStats = db.prepare(`
    SELECT 
      framework,
      COUNT(*) as total_configs,
      SUM(CASE WHEN isActive = 1 THEN 1 ELSE 0 END) as active_configs,
      COUNT(DISTINCT testCaseId) as test_cases_count
    FROM automationconfig
    GROUP BY framework
    ORDER BY total_configs DESC
  `).all();
  
  console.log(`\n📈 框架使用统计:`);
  frameworkStats.forEach(stat => {
    const activeRate = ((stat.active_configs / stat.total_configs) * 100).toFixed(1);
    console.log(`   ${stat.framework.toUpperCase()}:`);
    console.log(`      总配置: ${stat.total_configs}`);
    console.log(`      激活配置: ${stat.active_configs} (${activeRate}%)`);
    console.log(`      涉及测试用例: ${stat.test_cases_count}`);
    console.log('');
  });
  
  // 多框架测试用例统计
  const multiFrameworkStats = db.prepare(`
    SELECT 
      testCaseId,
      COUNT(*) as framework_count
    FROM automationconfig
    GROUP BY testCaseId
    HAVING framework_count > 1
    ORDER BY framework_count DESC
  `).all();
  
  console.log(`\n🔄 多框架配置统计:`);
  console.log(`   支持多框架的测试用例: ${multiFrameworkStats.length}`);
  
  multiFrameworkStats.forEach(stat => {
    const testCase = groupedByTestCase[stat.testCaseId];
    console.log(`   ${stat.testCaseId}: ${testCase.name} (${stat.framework_count} 个框架)`);
    
    const frameworks = testCase.configs.map(c => c.framework).join(', ');
    console.log(`      框架: ${frameworks}`);
  });
  
  // 环境分布统计
  const envStats = db.prepare(`
    SELECT 
      environment,
      framework,
      COUNT(*) as count
    FROM automationconfig
    GROUP BY environment, framework
    ORDER BY environment, framework
  `).all();
  
  console.log(`\n🏗️ 环境分布统计:`);
  const envGroups = {};
  envStats.forEach(stat => {
    if (!envGroups[stat.environment]) {
      envGroups[stat.environment] = [];
    }
    envGroups[stat.environment].push(`${stat.framework}(${stat.count})`);
  });
  
  Object.entries(envGroups).forEach(([env, frameworks]) => {
    console.log(`   ${env}: ${frameworks.join(', ')}`);
  });
  
  // 验证数据完整性
  const incompleteConfigs = db.prepare(`
    SELECT id, testCaseId, framework
    FROM automationconfig
    WHERE repository IS NULL 
       OR repository = ''
       OR commands IS NULL
       OR commands = ''
       OR commands = '[]'
  `).all();
  
  if (incompleteConfigs.length > 0) {
    console.log(`\n⚠️ 发现 ${incompleteConfigs.length} 个不完整的配置:`);
    incompleteConfigs.forEach(config => {
      console.log(`   ${config.testCaseId} - ${config.framework}: ${config.id}`);
    });
  } else {
    console.log(`\n✅ 所有配置数据完整性验证通过`);
  }
  
  db.close();
  console.log(`\n🎉 多框架自动化配置测试完成!`);
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
}
