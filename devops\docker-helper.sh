#!/bin/bash

# Docker 管理脚本
# 用于简化 Docker 部署操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 生产环境部署
deploy_production() {
    print_header "部署生产环境"
    check_docker
    
    print_message "构建并启动生产环境..."
    docker-compose -f docker-compose.yml up --build -d
    
    print_message "生产环境已启动，访问 http://localhost:3000"
}

# 开发环境部署
deploy_development() {
    print_header "部署开发环境"
    check_docker
    
    print_message "构建并启动开发环境..."
    docker-compose -f docker-compose.dev.yml up --build -d
    
    print_message "开发环境已启动，访问 http://localhost:3000"
}

# 停止所有服务
stop_services() {
    print_header "停止服务"
    check_docker
    
    print_message "停止生产环境..."
    docker-compose -f docker-compose.yml down
    
    print_message "停止开发环境..."
    docker-compose -f docker-compose.dev.yml down
    
    print_message "所有服务已停止"
}

# 查看日志
view_logs() {
    local service=${1:-app}
    local env=${2:-prod}
    
    print_header "查看日志 - $service ($env)"
    check_docker
    
    if [ "$env" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml logs -f $service
    else
        docker-compose -f docker-compose.yml logs -f $service
    fi
}

# 清理 Docker 资源
cleanup() {
    print_header "清理 Docker 资源"
    check_docker
    
    print_warning "这将删除所有未使用的容器、网络和镜像"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "清理中..."
        docker system prune -a -f
        print_message "清理完成"
    else
        print_message "取消清理"
    fi
}

# 重启服务
restart_services() {
    local env=${1:-prod}
    
    print_header "重启服务 ($env)"
    check_docker
    
    if [ "$env" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml restart
    else
        docker-compose -f docker-compose.yml restart
    fi
    
    print_message "服务已重启"
}

# 显示状态
show_status() {
    print_header "服务状态"
    check_docker
    
    print_message "生产环境状态:"
    docker-compose -f docker-compose.yml ps
    
    echo
    print_message "开发环境状态:"
    docker-compose -f docker-compose.dev.yml ps
}

# 显示帮助信息
show_help() {
    echo "Docker 管理脚本"
    echo
    echo "用法: $0 [命令] [选项]"
    echo
    echo "命令:"
    echo "  prod         部署生产环境"
    echo "  dev          部署开发环境"
    echo "  stop         停止所有服务"
    echo "  restart      重启服务 (默认生产环境)"
    echo "  logs         查看日志"
    echo "  status       显示服务状态"
    echo "  cleanup      清理 Docker 资源"
    echo "  help         显示此帮助信息"
    echo
    echo "选项:"
    echo "  --dev        指定开发环境 (用于 restart 和 logs 命令)"
    echo "  --service    指定服务名称 (用于 logs 命令)"
    echo
    echo "示例:"
    echo "  $0 prod                    # 部署生产环境"
    echo "  $0 dev                     # 部署开发环境"
    echo "  $0 logs --dev --service db # 查看开发环境数据库日志"
    echo "  $0 restart --dev           # 重启开发环境"
    echo
    echo "注意: 此脚本需要在项目根目录运行"
}

# 主函数
main() {
    case "${1:-help}" in
        "prod"|"production")
            deploy_production
            ;;
        "dev"|"development")
            deploy_development
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            local env="prod"
            if [[ "$2" == "--dev" ]]; then
                env="dev"
            fi
            restart_services $env
            ;;
        "logs")
            local service="app"
            local env="prod"
            
            # 解析参数
            while [[ $# -gt 0 ]]; do
                case $1 in
                    --service)
                        service="$2"
                        shift 2
                        ;;
                    --dev)
                        env="dev"
                        shift
                        ;;
                    *)
                        shift
                        ;;
                esac
            done
            
            view_logs $service $env
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 