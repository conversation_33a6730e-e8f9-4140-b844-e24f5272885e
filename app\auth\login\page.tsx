'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';
import { toast } from '@/components/chat/toast';


import { login, type LoginActionState } from '../actions';
import { useSession, signIn } from 'next-auth/react';

export default function Page() {
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);

  const [state, formAction] = useActionState<LoginActionState, FormData>(
    login,
    {
      status: 'idle',
    },
  );

  const { update: updateSession } = useSession();

  useEffect(() => {
    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: 'Invalid credentials!',
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: 'Failed validating your submission!',
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      updateSession();
      router.refresh();
      router.push('/');
    }
  }, [state.status, router, updateSession]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };
  
  const handleGuestLogin = async () => {
    try {
      const result = await signIn('guest', { callbackUrl: '/', redirect: false });
      if (result?.error) {
        console.error('访客登录失败:', result.error);
        toast({
          type: 'error',
          description: '访客登录失败，请稍后再试',
        });
      } else if (result?.url) {
        router.push(result.url);
      }
    } catch (error) {
      console.error('访客登录错误:', error);
      toast({
        type: 'error',
        description: '访客登录出现错误，请稍后再试',
      });
    }
  };

  return (
    <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12 p-8 border border-gray-200 dark:border-gray-800">
      <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
        <h3 className="text-xl font-semibold dark:text-zinc-50">登录</h3>
        <p className="text-sm text-gray-500 dark:text-zinc-400">
          使用您的电子邮件和密码登录
        </p>
      </div>
      
      <form className="flex flex-col gap-4" action={handleSubmit}>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
            电子邮件
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:outline-none focus:ring-blue-500"
          />
        </div>
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
            密码
          </label>
          <input
            id="password"
            name="password"
            type="password"
            required
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:outline-none focus:ring-blue-500"
          />
        </div>
        <button
          type="submit"
          className="mt-4 flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          登录
        </button>
      </form>
      
      <div className="flex items-center justify-center">
        <span className="text-sm text-gray-500 dark:text-gray-400">或者</span>
      </div>
      
      <button
        onClick={handleGuestLogin}
        className="flex w-full justify-center rounded-md bg-gray-200 dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      >
        以访客身份登录
      </button>
      
      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        没有账户？{' '}
        <Link href="/register" className="text-blue-600 hover:text-blue-500">
          注册
        </Link>
      </div>
    </div>
  );
} 