'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bug, 
  Plus,
  ExternalLink,
  User,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { ModuleProps } from '../types';

export default function KnownIssuesModule({ 
  testCase, 
  isEditing, 
  onUpdate 
}: ModuleProps) {
  
  const getSeverityColor = (severity: string) => {
    const colors = {
      'critical': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'high': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
      'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    };
    return colors[severity as keyof typeof colors] || colors.medium;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'open': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'investigating': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'resolved': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'wont-fix': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return colors[status as keyof typeof colors] || colors.open;
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'medium':
        return <Bug className="w-4 h-4 text-yellow-600" />;
      case 'low':
        return <Bug className="w-4 h-4 text-green-600" />;
      default:
        return <Bug className="w-4 h-4 text-slate-600" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white/80 dark:bg-zinc-800/80 backdrop-blur-sm rounded-lg p-6 border border-red-200 dark:border-red-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Bug className="w-5 h-5 text-red-600" />
            <h2 className="text-xl font-semibold">Known Issues</h2>
            <Badge variant="outline" className="ml-2">
              {testCase.knownIssues.length} issues
            </Badge>
          </div>
          {isEditing && (
            <Button 
              variant="outline" 
              size="sm"
              className="border-red-200 hover:bg-red-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Report Issue
            </Button>
          )}
        </div>

        {testCase.knownIssues.length > 0 ? (
          <div className="space-y-4">
            {testCase.knownIssues.map((issue) => (
              <div
                key={issue.id}
                className="flex items-start gap-4 p-4 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 hover:bg-slate-100 dark:hover:bg-slate-800/70 transition-all duration-200"
              >
                <div className="flex items-center gap-2">
                  {getSeverityIcon(issue.severity)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4 mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-1">
                        {issue.title}
                      </h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mb-3 leading-relaxed">
                        {issue.description}
                      </p>
                      
                      <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>Reporter: {issue.reporter}</span>
                        </div>
                        {issue.assignee && (
                          <div className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            <span>Assignee: {issue.assignee}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>{new Date(issue.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col items-end gap-2">
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(issue.severity)}>
                          {issue.severity.toUpperCase()}
                        </Badge>
                        <Badge className={getStatusColor(issue.status)}>
                          {issue.status.replace('-', ' ').toUpperCase()}
                        </Badge>
                      </div>
                      
                      {issue.bugUrl && (
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3 mr-1" />
                          View Bug
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Bug className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">
              No known issues
            </h3>
            <p className="text-slate-500 dark:text-slate-500 mb-4">
              Report any bugs or issues related to this test case
            </p>
            {isEditing && (
              <Button 
                variant="outline"
                className="border-red-200 hover:bg-red-50"
              >
                <Plus className="w-4 h-4 mr-2" />
                Report First Issue
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
