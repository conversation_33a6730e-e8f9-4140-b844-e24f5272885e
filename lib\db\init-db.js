// 初始化数据库
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// 确保数据库文件存在
const dbPath = process.env.SQLITE_PATH || path.join(__dirname, '../../data/sqlite.db');;
console.log(`初始化数据库: ${dbPath}`);

// 如果文件不存在或大小为0，创建新的数据库文件
if (!fs.existsSync(dbPath) || fs.statSync(dbPath).size === 0) {
  console.log('创建新的数据库文件...');
  const db = new Database(dbPath);
  
  // 创建用户表
  db.exec(`
    CREATE TABLE IF NOT EXISTS user (
      id TEXT PRIMARY KEY NOT NULL,
      email TEXT NOT NULL,
      password TEXT
    );
  `);

  // 创建聊天表
  db.exec(`
    CREATE TABLE IF NOT EXISTS chat (
      id TEXT PRIMARY KEY NOT NULL,
      createdAt INTEGER NOT NULL,
      title TEXT NOT NULL,
      userId TEXT NOT NULL,
      visibility TEXT NOT NULL DEFAULT 'private'
    );
  `);

  // 创建消息表
  db.exec(`
    CREATE TABLE IF NOT EXISTS message_v2 (
      id TEXT PRIMARY KEY NOT NULL,
      chatId TEXT NOT NULL,
      role TEXT NOT NULL,
      parts TEXT NOT NULL,
      attachments TEXT NOT NULL,
      createdAt INTEGER NOT NULL
    );
  `);

  // 创建投票表
  db.exec(`
    CREATE TABLE IF NOT EXISTS vote_v2 (
      chatId TEXT NOT NULL,
      messageId TEXT NOT NULL,
      isUpvoted INTEGER NOT NULL,
      PRIMARY KEY (chatId, messageId)
    );
  `);

  // 创建文档表
  db.exec(`
    CREATE TABLE IF NOT EXISTS document (
      id TEXT NOT NULL,
      createdAt INTEGER NOT NULL,
      title TEXT NOT NULL,
      content TEXT,
      kind TEXT NOT NULL DEFAULT 'text',
      userId TEXT NOT NULL,
      PRIMARY KEY (id, createdAt)
    );
  `);

  // 创建建议表
  db.exec(`
    CREATE TABLE IF NOT EXISTS suggestion (
      id TEXT PRIMARY KEY NOT NULL,
      documentId TEXT NOT NULL,
      documentCreatedAt INTEGER NOT NULL,
      originalText TEXT NOT NULL,
      suggestedText TEXT NOT NULL,
      description TEXT,
      isResolved INTEGER NOT NULL DEFAULT 0,
      userId TEXT NOT NULL,
      createdAt INTEGER NOT NULL
    );
  `);

  // 创建流表
  db.exec(`
    CREATE TABLE IF NOT EXISTS stream (
      id TEXT PRIMARY KEY NOT NULL,
      chatId TEXT NOT NULL,
      createdAt INTEGER NOT NULL
    );
  `);

  // ==================== 测试用例管理系统表 ====================

  // 创建文件夹表
  db.exec(`
    CREATE TABLE IF NOT EXISTS folder (
      id TEXT PRIMARY KEY NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      parentId TEXT,
      path TEXT NOT NULL,
      level INTEGER NOT NULL DEFAULT 0,
      sortOrder INTEGER NOT NULL DEFAULT 0,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL,
      createdBy TEXT NOT NULL,
      updatedBy TEXT NOT NULL
    );
  `);

  // 创建测试用例主表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testcase (
      id TEXT PRIMARY KEY NOT NULL,
      folderId TEXT,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      preconditions TEXT,
      priority TEXT NOT NULL DEFAULT 'medium',
      status TEXT NOT NULL DEFAULT 'draft',
      weight TEXT NOT NULL DEFAULT 'medium',
      format TEXT NOT NULL DEFAULT 'classic',
      nature TEXT NOT NULL DEFAULT 'functional',
      type TEXT NOT NULL DEFAULT 'regression',
      tags TEXT NOT NULL DEFAULT '[]',
      executionTime INTEGER,
      lastRunAt INTEGER,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL,
      createdBy TEXT NOT NULL,
      updatedBy TEXT NOT NULL
    );
  `);

  // 创建测试步骤表
  db.exec(`
    CREATE TABLE IF NOT EXISTS teststep (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      stepNumber INTEGER NOT NULL,
      action TEXT NOT NULL,
      expected TEXT NOT NULL,
      type TEXT NOT NULL DEFAULT 'manual',
      notes TEXT,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL
    );
  `);

  // 创建自动化配置表
  db.exec(`
    CREATE TABLE IF NOT EXISTS automationconfig (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      repository TEXT NOT NULL,
      branch TEXT NOT NULL DEFAULT 'main',
      commands TEXT NOT NULL,
      parameters TEXT NOT NULL DEFAULT '{}',
      framework TEXT NOT NULL DEFAULT 'midscene',
      browser TEXT NOT NULL DEFAULT 'chrome',
      environment TEXT NOT NULL DEFAULT 'test',
      isActive INTEGER NOT NULL DEFAULT 1,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL
    );
  `);

  // 创建相关需求表
  db.exec(`
    CREATE TABLE IF NOT EXISTS relatedrequirement (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      requirementId TEXT NOT NULL,
      type TEXT NOT NULL,
      title TEXT NOT NULL,
      status TEXT NOT NULL,
      assignee TEXT,
      url TEXT,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL
    );
  `);

  // 创建数据集表
  db.exec(`
    CREATE TABLE IF NOT EXISTS dataset (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      columns TEXT NOT NULL,
      data TEXT NOT NULL,
      isActive INTEGER NOT NULL DEFAULT 1,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL
    );
  `);

  // 创建测试运行表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testrun (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      runDate INTEGER NOT NULL,
      status TEXT NOT NULL,
      duration INTEGER,
      environment TEXT NOT NULL,
      executor TEXT NOT NULL,
      results TEXT NOT NULL DEFAULT '[]',
      errorMessage TEXT,
      logs TEXT,
      screenshots TEXT NOT NULL DEFAULT '[]',
      createdAt INTEGER NOT NULL
    );
  `);

  // 创建已知问题表
  db.exec(`
    CREATE TABLE IF NOT EXISTS knownissue (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      severity TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'open',
      reporter TEXT NOT NULL,
      assignee TEXT,
      bugUrl TEXT,
      workaround TEXT,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL,
      resolvedAt INTEGER
    );
  `);

  // 创建测试用例标签表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testcasetag (
      id TEXT PRIMARY KEY NOT NULL,
      name TEXT NOT NULL,
      color TEXT NOT NULL DEFAULT '#3B82F6',
      description TEXT,
      createdAt INTEGER NOT NULL,
      createdBy TEXT NOT NULL
    );
  `);

  // 创建测试用例标签关联表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testcasetagrelation (
      testCaseId TEXT NOT NULL,
      tagId TEXT NOT NULL,
      createdAt INTEGER NOT NULL,
      PRIMARY KEY (testCaseId, tagId)
    );
  `);

  // 创建测试用例评论表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testcasecomment (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      content TEXT NOT NULL,
      author TEXT NOT NULL,
      parentId TEXT,
      isResolved INTEGER NOT NULL DEFAULT 0,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL
    );
  `);

  // 创建测试用例历史版本表
  db.exec(`
    CREATE TABLE IF NOT EXISTS testcasehistory (
      id TEXT PRIMARY KEY NOT NULL,
      testCaseId TEXT NOT NULL,
      version INTEGER NOT NULL,
      changeType TEXT NOT NULL,
      changes TEXT NOT NULL,
      changeDescription TEXT,
      changedBy TEXT NOT NULL,
      createdAt INTEGER NOT NULL
    );
  `);

  // 创建索引以提高查询性能
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_folder_parent ON folder(parentId);
    CREATE INDEX IF NOT EXISTS idx_folder_path ON folder(path);
    CREATE INDEX IF NOT EXISTS idx_testcase_folder ON testcase(folderId);
    CREATE INDEX IF NOT EXISTS idx_testcase_status ON testcase(status);
    CREATE INDEX IF NOT EXISTS idx_testcase_priority ON testcase(priority);
    CREATE INDEX IF NOT EXISTS idx_testcase_updated ON testcase(updatedAt);
    CREATE INDEX IF NOT EXISTS idx_teststep_testcase ON teststep(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_teststep_number ON teststep(testCaseId, stepNumber);
    CREATE INDEX IF NOT EXISTS idx_automation_testcase ON automationconfig(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_requirement_testcase ON relatedrequirement(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_dataset_testcase ON dataset(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_testrun_testcase ON testrun(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_testrun_date ON testrun(runDate);
    CREATE INDEX IF NOT EXISTS idx_issue_testcase ON knownissue(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_issue_status ON knownissue(status);
    CREATE INDEX IF NOT EXISTS idx_comment_testcase ON testcasecomment(testCaseId);
    CREATE INDEX IF NOT EXISTS idx_history_testcase ON testcasehistory(testCaseId);
  `);

  // 插入示例数据
  console.log('插入示例数据...');

  // 插入根文件夹
  db.exec(`
    INSERT OR IGNORE INTO folder (id, name, description, parentId, path, level, sortOrder, createdAt, updatedAt, createdBy, updatedBy)
    VALUES
      ('root-folder', 'Root', '根文件夹', NULL, '/Root', 0, 0, ${Date.now()}, ${Date.now()}, 'system', 'system'),
      ('api-folder', 'API Tests', 'API接口测试', 'root-folder', '/Root/API Tests', 1, 1, ${Date.now()}, ${Date.now()}, 'system', 'system'),
      ('ui-folder', 'UI Tests', 'UI界面测试', 'root-folder', '/Root/UI Tests', 1, 2, ${Date.now()}, ${Date.now()}, 'system', 'system'),
      ('auth-folder', 'Authentication', '认证相关测试', 'ui-folder', '/Root/UI Tests/Authentication', 2, 1, ${Date.now()}, ${Date.now()}, 'system', 'system');
  `);

  // 插入示例测试用例
  db.exec(`
    INSERT OR IGNORE INTO testcase (id, folderId, name, description, preconditions, priority, status, weight, format, nature, type, tags, executionTime, createdAt, updatedAt, createdBy, updatedBy)
    VALUES
      ('1-1-1', 'auth-folder', 'Login with valid user', 'Test the login functionality with valid user credentials to ensure proper authentication flow and user experience', 'User account must be created and activated. Application must be running and accessible. Database connection must be established.', 'high', 'work-in-progress', 'low', 'classic', 'functional', 'regression', '["login", "authentication", "smoke"]', 45, ${Date.now()}, ${Date.now()}, 'henix_admin', 'guest_tr'),
      ('1-1-2', 'auth-folder', 'Login with invalid credentials', 'Test login functionality with invalid credentials to ensure proper error handling', 'Application must be running and accessible', 'high', 'active', 'low', 'classic', 'functional', 'regression', '["login", "authentication", "negative"]', 30, ${Date.now()}, ${Date.now()}, 'henix_admin', 'henix_admin'),
      ('1-2-1', 'ui-folder', 'User profile update', 'Test user profile information update functionality', 'User must be logged in', 'medium', 'active', 'medium', 'classic', 'functional', 'regression', '["profile", "update"]', 60, ${Date.now()}, ${Date.now()}, 'henix_admin', 'henix_admin');
  `);

  // 插入示例测试步骤
  db.exec(`
    INSERT OR IGNORE INTO teststep (id, testCaseId, stepNumber, action, expected, type, notes, createdAt, updatedAt)
    VALUES
      ('step-1-1', '1-1-1', 1, 'Navigate to login page', 'Login page should be displayed with username and password fields', 'manual', 'Ensure browser is in incognito mode', ${Date.now()}, ${Date.now()}),
      ('step-1-2', '1-1-1', 2, 'Enter valid username and password', 'Credentials should be accepted without validation errors', 'manual', NULL, ${Date.now()}, ${Date.now()}),
      ('step-1-3', '1-1-1', 3, 'Click login button', 'User should be redirected to dashboard', 'manual', NULL, ${Date.now()}, ${Date.now()}),
      ('step-1-4', '1-1-1', 4, 'Verify user session', 'User session should be active and user info displayed', 'automated', NULL, ${Date.now()}, ${Date.now()});
  `);

  // 插入示例标签
  db.exec(`
    INSERT OR IGNORE INTO testcasetag (id, name, color, description, createdAt, createdBy)
    VALUES
      ('tag-login', 'login', '#10B981', '登录相关测试', ${Date.now()}, 'system'),
      ('tag-auth', 'authentication', '#3B82F6', '认证相关测试', ${Date.now()}, 'system'),
      ('tag-smoke', 'smoke', '#F59E0B', '冒烟测试', ${Date.now()}, 'system'),
      ('tag-regression', 'regression', '#EF4444', '回归测试', ${Date.now()}, 'system'),
      ('tag-api', 'api', '#8B5CF6', 'API测试', ${Date.now()}, 'system'),
      ('tag-ui', 'ui', '#06B6D4', 'UI测试', ${Date.now()}, 'system');
  `);

  // 插入自动化配置示例数据
  db.exec(`
    INSERT OR IGNORE INTO automationconfig (id, testCaseId, repository, branch, commands, parameters, framework, browser, environment, isActive, createdAt, updatedAt)
    VALUES
      ('auto-1-1-1', '1-1-1', 'https://github.com/company/test-automation', 'main',
       '["npm install", "npm run test:login", "npm run test:report"]',
       '{"browser": "chrome", "headless": "true", "timeout": "30000", "viewport": "1920x1080", "retries": "3"}',
       'midscene', 'chrome', 'test', 1, ${Date.now()}, ${Date.now()}),
      ('auto-1-1-2', '1-1-2', 'https://github.com/company/security-tests', 'develop',
       '["npm install", "npx playwright test login-negative", "npx playwright show-report"]',
       '{"browser": "firefox", "headless": "false", "timeout": "45000", "workers": "2", "retries": "1", "video": "retain-on-failure"}',
       'playwright', 'firefox', 'staging', 1, ${Date.now()}, ${Date.now()}),
      ('auto-1-2-1', '1-2-1', 'https://github.com/company/e2e-tests', 'feature/profile-tests',
       '["yarn install", "yarn cypress:run --spec \\"cypress/e2e/profile/**\\"", "yarn cypress:report"]',
       '{"browser": "chrome", "headless": "true", "timeout": "60000", "baseUrl": "https://staging.company.com", "video": "true", "screenshots": "true", "env": "staging"}',
       'cypress', 'chrome', 'staging', 1, ${Date.now()}, ${Date.now()});
  `);

  // 插入测试运行示例数据
  db.exec(`
    INSERT OR IGNORE INTO testrun (id, testCaseId, runDate, status, duration, environment, executor, results, errorMessage, logs, screenshots, createdAt)
    VALUES
      ('run-1', '1-1-1', ${Date.now() - 86400000}, 'passed', 45, 'staging', 'john.doe',
       '[{"stepId": "step-1", "status": "passed", "duration": 15}, {"stepId": "step-2", "status": "passed", "duration": 10}, {"stepId": "step-3", "status": "passed", "duration": 20}]',
       NULL, 'Test completed successfully', '[]', ${Date.now() - 86400000}),
      ('run-2', '1-1-1', ${Date.now() - *********}, 'failed', 30, 'production', 'jane.smith',
       '[{"stepId": "step-1", "status": "passed", "duration": 15}, {"stepId": "step-2", "status": "failed", "duration": 15, "error": "Invalid credentials"}]',
       'Login failed with invalid credentials', 'Error: Element not found', '[]', ${Date.now() - *********}),
      ('run-3', '1-1-2', ${Date.now() - 43200000}, 'passed', 28, 'staging', 'security.bot',
       '[{"stepId": "step-1", "status": "passed", "duration": 8}, {"stepId": "step-2", "status": "passed", "duration": 12}, {"stepId": "step-3", "status": "passed", "duration": 8}]',
       NULL, 'Security test passed', '[]', ${Date.now() - 43200000}),
      ('run-4', '1-2-1', ${Date.now() - 21600000}, 'passed', 58, 'staging', 'cypress.runner',
       '[{"stepId": "step-1", "status": "passed", "duration": 12}, {"stepId": "step-2", "status": "passed", "duration": 18}, {"stepId": "step-3", "status": "passed", "duration": 15}, {"stepId": "step-4", "status": "passed", "duration": 13}]',
       NULL, 'Profile update test completed', '[]', ${Date.now() - 21600000});
  `);

  console.log('自动化配置示例数据插入完成');
  console.log('测试运行示例数据插入完成');
  console.log('示例数据插入完成');
  console.log('测试用例管理系统表创建完成');
  console.log('数据库表创建完成');
  db.close();
} else {
  console.log('数据库文件已存在，大小正常');
}

console.log('数据库初始化完成'); 