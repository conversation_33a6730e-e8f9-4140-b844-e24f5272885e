# Midscene配置生成功能

## 🎯 功能概述

通过AI聊天框智能生成Midscene自动化配置，包括YAML测试脚本和完整的配置参数，并自动保存到数据库中。

## 🔧 技术实现

### 1. AI工具集成

#### 新增工具：generateMidsceneConfigTool
```typescript
export const generateMidsceneConfigTool = tool({
  description: '为测试用例生成Midscene自动化配置，包括YAML测试脚本和配置参数',
  parameters: z.object({
    testCaseId: z.string().describe('测试用例ID'),
    url: z.string().describe('要测试的网站URL'),
    title: z.string().describe('测试标题'),
    description: z.string().optional().describe('测试描述和用户需求'),
    repository: z.string().optional().default('https://github.com/company/midscene-automation'),
    branch: z.string().optional().default('main'),
    browser: z.enum(['chrome', 'firefox', 'safari', 'edge']).optional().default('chrome'),
    environment: z.enum(['dev', 'test', 'staging', 'prod']).optional().default('test'),
  }),
  execute: async ({ testCaseId, url, title, description, repository, branch, browser, environment }) => {
    // 实现逻辑
  }
});
```

### 2. 核心流程

#### 步骤1：AI生成YAML
```typescript
const testingService = new TestingService();
const yamlResult = await testingService.generateTestingYaml(url, title, description);
```

#### 步骤2：准备配置数据
```typescript
const configData = {
  repository: repository || 'https://github.com/company/midscene-automation',
  branch: branch || 'main',
  commands: [
    'npm install',
    'npm run midscene:test',
    'npm run midscene:report'
  ],
  parameters: {
    browser: browser || 'chrome',
    environment: environment || 'test',
    headless: 'true',
    timeout: '30000',
    viewport: '1920x1080',
    retries: '2',
    yaml_content: yamlResult.yaml  // 关键：将生成的YAML存储在parameters中
  },
  framework: 'midscene' as const,
  browser: browser || 'chrome',
  environment: environment || 'test',
  isActive: true
};
```

#### 步骤3：保存到数据库
```typescript
await createOrUpdateAutomationConfig(testCaseId, configData);
```

### 3. 快捷操作集成

#### 新增快捷按钮
```typescript
{
  id: 'generate-midscene',
  label: '生成Midscene配置',
  icon: Bot,
  prompt: `请为测试用例"${testCase.name}"生成Midscene自动化配置。

测试用例信息：
- 名称：${testCase.name}
- 描述：${testCase.description}
- ID：${testCase.id}

请提供要测试的网站URL，我将为您生成完整的Midscene YAML配置并保存到数据库中。`
}
```

## 🎨 用户交互流程

### 1. 启动配置生成
用户可以通过以下方式启动：
- 点击"生成Midscene配置"快捷按钮
- 直接在聊天框中描述需求

### 2. 提供测试信息
用户需要提供：
- **网站URL**（必需）：要测试的网站地址
- **测试描述**（可选）：具体的测试需求和操作流程
- **配置参数**（可选）：浏览器、环境等

### 3. AI智能生成
AI将：
- 分析用户需求
- 调用TestingService生成YAML
- 创建完整的配置参数
- 保存到数据库

### 4. 结果展示
生成成功后显示：
- 生成的YAML配置
- 执行命令列表
- 配置参数详情
- 下一步操作建议

## 📊 数据存储格式

### 数据库存储
```sql
INSERT INTO automationconfig (
  testCaseId,
  framework,
  repository,
  branch,
  commands,
  parameters,
  browser,
  environment,
  isActive
) VALUES (
  '1-1-1',
  'midscene',
  'https://github.com/company/midscene-automation',
  'main',
  '["npm install", "npm run midscene:test", "npm run midscene:report"]',
  '{"browser": "chrome", "environment": "test", "yaml_content": "web:\n  - action: goto\n    url: https://example.com"}',
  'chrome',
  'test',
  1
);
```

### YAML内容存储
YAML配置存储在`parameters.yaml_content`字段中：
```yaml
web:
  - action: goto
    url: https://example.com
  - action: click
    selector: "#login-button"
  - action: type
    selector: "#username"
    text: "testuser"
  - action: assert
    selector: "#welcome-message"
    text: "Welcome"
```

## 🚀 使用示例

### 示例对话1：基础配置生成
```
用户：请为登录功能生成Midscene配置
URL：https://example.com/login

AI：✅ 成功生成并保存Midscene自动化配置！

🎯 测试目标: 登录功能测试
🌐 测试URL: https://example.com/login
🔧 框架: Midscene
🌍 浏览器: chrome
🏗️ 环境: test

📋 生成的YAML配置:
```yaml
web:
  - action: goto
    url: https://example.com/login
  - action: type
    selector: "#username"
    text: "testuser"
  - action: type
    selector: "#password"
    text: "password123"
  - action: click
    selector: "#login-button"
  - action: assert
    selector: "#dashboard"
    text: "Dashboard"
```

🚀 执行命令:
• npm install
• npm run midscene:test
• npm run midscene:report

💾 配置已保存到数据库，您可以在自动化配置模块中查看和管理。
```

### 示例对话2：详细需求配置
```
用户：我需要测试电商网站的购物流程，包括搜索商品、添加到购物车、结账等步骤
URL：https://shop.example.com
浏览器：firefox
环境：staging

AI：[生成包含完整购物流程的YAML配置]
```

## 🔍 错误处理

### 1. YAML生成失败
```typescript
if (!yamlResult.success) {
  return `❌ 生成Midscene配置失败：${yamlResult.message}

请提供更详细的测试需求，例如：
- 需要测试哪些具体功能
- 用户操作流程
- 预期的测试结果`;
}
```

### 2. 数据库保存失败
```typescript
try {
  await createOrUpdateAutomationConfig(testCaseId, configData);
} catch (error) {
  return `❌ 生成Midscene配置时发生错误：${error instanceof Error ? error.message : String(error)}

请稍后重试，或联系技术支持。`;
}
```

## 🌟 功能特点

### 1. 智能化生成
- 基于用户描述自动生成YAML
- 支持复杂的测试流程
- 自动优化测试步骤

### 2. 完整配置
- 包含执行命令
- 配置运行参数
- 支持多环境部署

### 3. 数据持久化
- 自动保存到数据库
- 支持后续编辑和管理
- 与现有配置系统集成

### 4. 用户友好
- 简单的对话式交互
- 清晰的结果展示
- 详细的操作指导

## ✨ 总结

这个功能实现了从用户需求到完整Midscene配置的全自动化流程：

- **输入**：用户描述 + 网站URL
- **处理**：AI生成YAML + 配置参数
- **输出**：完整的数据库配置记录

用户只需要简单描述测试需求，AI就能生成专业的Midscene自动化配置并保存到数据库中，大大简化了自动化测试的配置过程！
