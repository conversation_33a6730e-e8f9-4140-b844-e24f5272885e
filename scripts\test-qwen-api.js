// 测试Qwen API连接的脚本

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

// 检查环境变量
function checkEnvironment() {
  console.log('检查环境变量...');
  const apiKey = process.env.QWEN_API_KEY;
  
  if (!apiKey) {
    console.error('错误: 未找到QWEN_API_KEY环境变量');
    console.log('请确保您已创建.env.local文件并设置了QWEN_API_KEY');
    process.exit(1);
  }
  
  console.log('QWEN_API_KEY环境变量已找到');
  return apiKey;
}

// 测试API连接
async function testApiConnection(apiKey) {
  console.log('测试Qwen API连接...');
  
  try {
    // 创建一个简单的请求
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('API连接成功!');
    console.log('可用模型:', data);
    
  } catch (error) {
    console.error('API连接测试失败:', error);
    process.exit(1);
  }
}

// 主函数
async function main() {
  console.log('=== Qwen API 测试工具 ===');
  const apiKey = checkEnvironment();
  await testApiConnection(apiKey);
  console.log('=== 测试完成 ===');
}

// 运行测试
main().catch(error => {
  console.error('测试过程中发生错误:', error);
  process.exit(1);
}); 