# AI聊天框收起功能测试指南

## 🧪 测试步骤

### 1. 基本功能测试

1. **访问测试页面**
   - 打开浏览器访问: http://localhost:3000/test-case/1-1-1
   - 确认页面正常加载，右侧显示AI助手

2. **测试收起功能**
   - 在AI助手标题栏右侧找到收起按钮（`PanelRightClose` 图标）
   - 点击收起按钮
   - 验证：
     - AI助手面板宽度从480px收缩到48px
     - 内容区域获得更多空间
     - 收起面板显示简化的AI图标和文字
     - 动画过渡流畅（300ms）

3. **测试展开功能**
   - 在收起状态下，找到展开按钮（`PanelRightOpen` 图标）
   - 点击展开按钮
   - 验证：
     - AI助手面板恢复到480px宽度
     - AI助手内容完全显示
     - 动画过渡流畅

### 2. 视觉效果测试

1. **按钮样式**
   - 收起按钮应该与标题栏风格一致
   - 悬停时有适当的背景色变化
   - 图标大小和颜色合适

2. **动画效果**
   - 宽度变化应该平滑
   - 内容透明度变化自然
   - 无闪烁或跳跃现象

3. **收起状态显示**
   - 显示展开按钮
   - 显示AI图标和文字提示
   - 整体布局居中对齐

### 3. 交互测试

1. **工具提示**
   - 收起按钮悬停显示"收起AI助手"
   - 展开按钮悬停显示"展开AI助手"

2. **功能保持**
   - 收起后重新展开，AI助手功能正常
   - 聊天历史保持不变
   - 快捷操作按钮正常工作

### 4. 响应式测试

1. **不同屏幕尺寸**
   - 在不同窗口大小下测试
   - 确保收起功能在各种尺寸下正常工作

2. **暗色模式**
   - 切换到暗色模式
   - 验证按钮和图标在暗色主题下显示正常

## ✅ 预期结果

### 正常状态（展开）
- AI助手宽度：480px
- 显示完整的AI助手界面
- 标题栏右侧有收起按钮

### 收起状态
- AI助手宽度：48px
- 显示简化的AI图标和文字
- 顶部有展开按钮

### 动画效果
- 过渡时间：300ms
- 宽度平滑变化
- 内容淡入淡出

## 🐛 可能的问题

1. **按钮位置**
   - 如果按钮遮挡内容，需要调整位置
   - 确保按钮在各种内容长度下都不会重叠

2. **动画性能**
   - 在低性能设备上可能出现卡顿
   - 可以考虑减少动画复杂度

3. **状态同步**
   - 确保收起状态在页面刷新后不会丢失（如果需要持久化）

## 📱 多设备测试

1. **桌面端**
   - Chrome, Firefox, Safari, Edge
   - 不同分辨率下的表现

2. **移动端**
   - 响应式布局是否正常
   - 触摸操作是否流畅

## 🎯 成功标准

- ✅ 收起按钮位置合适，不遮挡内容
- ✅ 展开按钮易于找到和点击
- ✅ 动画流畅，无视觉故障
- ✅ 功能在收起/展开后保持正常
- ✅ 支持暗色模式
- ✅ 工具提示清晰明确

## 🚀 测试完成后

如果所有测试通过，这个功能就可以投入使用了！用户现在可以根据需要自由控制AI助手的显示状态，获得更灵活的界面布局。
