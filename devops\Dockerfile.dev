# 使用官方Node.js运行时作为基础镜像
FROM node:20-alpine

# 切换到阿里云 apk 源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 设置工作目录
WORKDIR /app

# 安装依赖项（包括 Python3、make、g++）
RUN apk add --no-cache libc6-compat python3 make g++

# 设置国内npm源
RUN npm config set registry https://registry.npmmirror.com/

# 只用npm安装依赖
COPY package.json package-lock.json* ./
RUN npm install

# 复制源代码
COPY . .

# 初始化数据库（如果不存在则自动建表）
RUN node lib/db/init-db.js

# 设置环境变量
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# 暴露端口
EXPOSE 3000

# 启动开发服务器
CMD ["npm", "run", "dev"] 