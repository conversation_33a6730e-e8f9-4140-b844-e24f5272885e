import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';
import { getMessagesByChatId } from '@/lib/db/queries';
import { convertToUIMessages } from '@/lib/utils';
import type { DBMessage } from '@/lib/db/schema';
import type { UIMessage } from 'ai';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    if (!chatId) {
      return new Response('chatId is required', { status: 400 });
    }

    console.log('Loading chat history for chatId:', chatId);

    // 获取聊天历史消息
    const messagesFromDb = await getMessagesByChatId({ id: chatId });
    console.log('Messages from DB:', messagesFromDb.length, messagesFromDb);

    // 详细检查每条消息的parts
    messagesFromDb.forEach((msg, index) => {
      console.log(`Message ${index}:`, {
        id: msg.id,
        role: msg.role,
        parts: msg.parts,
        partsType: typeof msg.parts,
        partsStringified: JSON.stringify(msg.parts)
      });
    });

    const uiMessages = convertToUIMessages(messagesFromDb);
    console.log('UI Messages:', uiMessages.length, uiMessages);

    return Response.json(uiMessages);
  } catch (error) {
    console.error('Get testcase chat history error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
