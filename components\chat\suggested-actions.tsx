'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { memo } from 'react';
import type { UseChatHelpers } from '@ai-sdk/react';
import type { VisibilityType } from './visibility-selector';

interface SuggestedActionsProps {
  chatId: string;
  append: UseChatHelpers['append'];
  selectedVisibilityType: VisibilityType;
}

function PureSuggestedActions({
  chatId,
  append,
  selectedVisibilityType,
}: SuggestedActionsProps) {
  const suggestedActions = [
    {
      title: '创建测试用例',
      label: '打开网易第一条新闻',
      action: '请创建一个测试用例，测试描述：在网易深圳(https://shenzhen.news.163.com/)中，点击政务,点开今日第一条新闻',
    },
    {
      title: '生成测试数据',
      label: `10个用户测试数据`,
      action: `请生成10个用户测试数据`,
    },
    {
      title: '请编写单元测试代码',
      label: `java单元测试`,
      action: `请编写一个java单元测试的示例代码`,
    },
    {
      title: '执行测试',
      label: '自动化测试',
      action: '在网易深圳(https://shenzhen.news.163.com/)中，点击政务,点开今日第一条新闻',
    },
  ];

  return (
    <div
      data-testid="suggested-actions"
      className="grid sm:grid-cols-2 gap-2 w-full"
    >
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className={index > 1 ? 'hidden sm:block' : 'block'}
        >
          <Button
            variant="outline"
            onClick={async () => {
              // 只有在非测试用例页面时才执行URL替换
              if (!window.location.pathname.includes('/test-case/')) {
                window.history.replaceState({}, '', `/chat/${chatId}`);
              }

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="text-left rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-shadow"
          >
            <span className="font-medium">{suggestedAction.title}</span>
            <span className="text-muted-foreground">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(
  PureSuggestedActions,
  (prevProps, nextProps) => {
    if (prevProps.chatId !== nextProps.chatId) return false;
    if (prevProps.selectedVisibilityType !== nextProps.selectedVisibilityType)
      return false;

    return true;
  },
);
