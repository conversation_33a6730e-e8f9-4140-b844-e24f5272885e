{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:api": "node scripts/test-qwen-api.js", "api": "node server.js", "api:dev": "nodemon server.js"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.16", "@assistant-ui/react-markdown": "^0.10.6", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.59", "@langchain/langgraph": "^0.3.4", "@langchain/openai": "^0.5.13", "@midscene/web": "latest", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-hook/media-query": "^1.1.1", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "@vercel/blob": "^1.1.1", "@vercel/functions": "^2.2.2", "ai": "^4.3.16", "bcrypt-ts": "^7.1.0", "better-sqlite3": "^11.10.0", "body-parser": "^1.20.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.44.2", "express": "^4.18.2", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.19.1", "lucide-react": "^0.522.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "orderedmap": "^2.1.1", "papaparse": "^5.5.3", "postgres": "^3.4.7", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "puppeteer": "^24.0.0", "qwen-ai-provider": "^0.1.0", "react": "^19.0.0", "react-data-grid": "7.0.0-beta.47", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-jsx-parser": "^2.4.0", "react-markdown": "^10.1.0", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "resumable-stream": "^2.2.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/next-auth": "^3.15.0", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "nodemon": "^3.0.2", "postcss": "^8.5.6", "swr": "^2.3.3", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "typescript": "^5"}}