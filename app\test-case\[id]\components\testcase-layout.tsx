'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useThemeStore } from '@/stores/theme-store';
import {
  ArrowLeft,
  Play,
  Save,
  Edit3,
  Bot,
  Activity,
  FileText,
  Weight,
  Tag,
  Info,
  Target,
  Link,
  Database,
  PlayCircle,
  Bug,

} from 'lucide-react';
import { TestCase, ModuleButtonConfig } from '../types';
import TestCaseAssistant from './testcase-assistant';

interface TestCaseLayoutProps {
  testCase: TestCase;
  children: React.ReactNode;
  activeTab: string;
  onTabChange: (tab: string) => void;
  isEditing: boolean;
  onEditToggle: () => void;
  onSave: () => void;
  onAIGenerate: () => void;
  onRunTest: () => void;
  isRunning: boolean;
  onTestCaseUpdate: (updates: Partial<TestCase>) => void;
}

export default function TestCaseLayout({
  testCase,
  children,
  activeTab,
  onTabChange,
  isEditing,
  onEditToggle,
  onSave,
  onAIGenerate,
  onRunTest,
  isRunning,
  onTestCaseUpdate
}: TestCaseLayoutProps) {
  const router = useRouter();
  const { theme, isDarkMode, initializeTheme, applyTheme } = useThemeStore();

  // AI聊天框收起状态
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);

  // 确保主题正确初始化
  useEffect(() => {
    // 直接从localStorage读取并应用主题，不依赖Zustand的hydration
    const applyStoredTheme = () => {
      try {
        const savedData = localStorage.getItem('theme-store');
        if (savedData) {
          const parsed = JSON.parse(savedData);
          const savedTheme = parsed.state?.theme || 'blue';
          const savedDarkMode = parsed.state?.isDarkMode || false;

          console.log('TestCaseLayout: 应用存储的主题', { savedTheme, savedDarkMode });

          // 直接应用到DOM
          const html = document.documentElement;
          html.classList.remove('theme-blue', 'theme-green', 'theme-purple', 'theme-orange');

          if (savedTheme !== 'default') {
            html.classList.add(`theme-${savedTheme}`);
          }

          if (savedDarkMode) {
            html.classList.add('dark');
          } else {
            html.classList.remove('dark');
          }

          console.log('TestCaseLayout: DOM类列表', html.className);
        } else {
          // 如果没有存储的主题，应用默认蓝色主题
          const html = document.documentElement;
          html.classList.remove('theme-blue', 'theme-green', 'theme-purple', 'theme-orange');
          html.classList.add('theme-blue');
          console.log('TestCaseLayout: 应用默认蓝色主题');
        }
      } catch (error) {
        console.error('TestCaseLayout: 应用主题失败', error);
        // 失败时应用默认主题
        const html = document.documentElement;
        html.classList.remove('theme-blue', 'theme-green', 'theme-purple', 'theme-orange');
        html.classList.add('theme-blue');
      }
    };

    // 立即应用
    applyStoredTheme();

    // 也调用store的初始化方法以保持状态同步
    initializeTheme();
  }, [initializeTheme]);

  // 获取当前模块应该显示的按钮
  const getModuleButtons = (moduleId: string): ModuleButtonConfig => {
    const buttonConfig: Record<string, ModuleButtonConfig> = {
      information: { aiGenerate: true, edit: true, runTest: false },
      steps: { aiGenerate: true, edit: true, runTest: false },
      automation: { aiGenerate: true, edit: true, runTest: true },
      requirements: { aiGenerate: false, edit: true, runTest: false },
      dataset: { aiGenerate: true, edit: true, runTest: false },
      testruns: { aiGenerate: false, edit: false, runTest: true },
      issues: { aiGenerate: false, edit: true, runTest: false }
    };
    
    return buttonConfig[moduleId] || { aiGenerate: false, edit: false, runTest: false };
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'work-in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'deprecated': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'draft': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return colors[status as keyof typeof colors] || colors.draft;
  };

  const modules = [
    { id: 'information', label: 'Information', icon: Info, color: 'blue' },
    { id: 'steps', label: 'Test Steps', icon: Target, color: 'indigo' },
    { id: 'automation', label: 'Automation', icon: Bot, color: 'purple' },
    { id: 'requirements', label: 'Requirements', icon: Link, color: 'green' },
    { id: 'dataset', label: 'Dataset', icon: Database, color: 'orange' },
    { id: 'testruns', label: 'Test Runs', icon: PlayCircle, color: 'teal' },
    { id: 'issues', label: 'Known Issues', icon: Bug, color: 'red' }
  ];

  const getActiveClasses = (color: string) => {
    // 使用主题色的不同透明度，忽略传入的color参数
    return 'bg-primary/10 dark:bg-primary/20 text-primary';
  };

  const getIconColor = (color: string) => {
    // 使用主题色，忽略传入的color参数
    return 'text-primary';
  };

  const buttons = getModuleButtons(activeTab);

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-zinc-900 dark:to-zinc-800 flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-200 dark:border-zinc-700 bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm p-6 flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/test-case')}
            className="hover:bg-slate-100 dark:hover:bg-slate-800"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Test Cases
          </Button>
          
          <div className="flex items-center gap-2">
            {/* AI Generate 按钮 */}
            {buttons.aiGenerate && (
              <Button
                variant="outline"
                onClick={onAIGenerate}
                className="border-primary/20 dark:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 text-primary"
              >
                <Bot className="w-4 h-4 mr-2" />
                AI Generate
              </Button>
            )}
            
            {/* Edit 按钮 */}
            {buttons.edit && (
              <>
                <Button
                  variant="outline"
                  onClick={onEditToggle}
                  className="border-primary/20 dark:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 text-primary"
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  {isEditing ? 'Cancel' : 'Edit'}
                </Button>

                {isEditing && (
                  <Button onClick={onSave} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                )}
              </>
            )}
            
            {/* Run Test 按钮 */}
            {buttons.runTest && (
              <Button
                onClick={onRunTest}
                disabled={isRunning}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                {isRunning ? (
                  <Activity className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {isRunning ? 'Running...' : 'Run Test'}
              </Button>
            )}
          </div>
        </div>

        {/* Test Case Header */}
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30 rounded-lg flex items-center justify-center shadow-sm">
            <FileText className="w-6 h-6 text-primary" />
          </div>
          
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-2">
              {testCase.name}
            </h1>
            
            <div className="flex items-center gap-3 mb-3">
              <Badge className={getPriorityColor(testCase.priority)}>
                <Weight className="w-3 h-3 mr-1" />
                {testCase.priority.toUpperCase()}
              </Badge>
              <Badge className={getStatusColor(testCase.status)}>
                <Activity className="w-3 h-3 mr-1" />
                {testCase.status.replace('-', ' ').toUpperCase()}
              </Badge>
              <Badge variant="outline">
                <Tag className="w-3 h-3 mr-1" />
                ID: {testCase.id}
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {testCase.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content with Sidebar Navigation */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white/60 dark:bg-zinc-900/60 backdrop-blur-sm border-r border-slate-200 dark:border-zinc-700 flex flex-col h-full">
          <div className="p-4 flex-1 overflow-y-auto">
            <h3 className="text-sm font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wide mb-4">
              Test Case Modules
            </h3>
            <nav className="space-y-2">
              {modules.map((item) => {
                const Icon = item.icon;
                const isActive = activeTab === item.id;

                return (
                  <button
                    key={item.id}
                    onClick={() => onTabChange(item.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 ${
                      isActive
                        ? `${getActiveClasses(item.color)} shadow-sm`
                        : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-200'
                    }`}
                  >
                    <Icon className={`w-4 h-4 ${isActive ? getIconColor(item.color) : ''}`} />
                    <span className="font-medium">{item.label}</span>
                    {item.id === 'steps' && (
                      <span className="ml-auto text-xs bg-slate-200 dark:bg-slate-700 px-2 py-0.5 rounded-full">
                        {testCase.steps.length}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main Content Area with Chat Assistant */}
        <div className="flex-1 flex overflow-hidden relative">
          {/* Content Area */}
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>

          {/* 浮动展开按钮 - 收起状态下显示 */}
          {isChatCollapsed && (
            <Button
              variant="default"
              size="sm"
              onClick={() => setIsChatCollapsed(false)}
              className="fixed bottom-6 right-6 z-50 h-12 w-12 p-0 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-primary hover:bg-primary/90"
              title="展开AI助手"
            >
              <Bot className="w-5 h-5 text-primary-foreground" />
            </Button>
          )}

          {/* AI Assistant Sidebar - 只在展开状态下显示 */}
          {!isChatCollapsed && (
            <div className="w-[480px] border-l border-slate-200 dark:border-zinc-700 flex flex-col h-full">
              <TestCaseAssistant
                testCase={testCase}
                onTestCaseUpdate={onTestCaseUpdate}
                onCollapse={() => setIsChatCollapsed(true)}
                className="h-full"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
