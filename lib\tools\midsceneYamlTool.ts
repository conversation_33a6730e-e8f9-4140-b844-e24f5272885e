import { z } from "zod";
import { tool } from "@langchain/core/tools";
import puppeteer from "puppeteer";
import { PuppeteerAgent } from "@midscene/web/puppeteer";
import os from "node:os";

/**
 * 使用Midscene执行YAML任务的工具
 * 该工具接收YAML格式的任务描述，并使用Midscene执行
 */
const midsceneYamlSchema = z.object({
  url: z.string().describe("要访问的网页URL"),
  yaml: z.string().describe("Midscene YAML任务描述"),
  headless: z.boolean().optional().default(true).describe("是否以无头模式运行浏览器"),
  width: z.number().optional().default(768).describe("浏览器视窗宽度"),
  height: z.number().optional().default(768).describe("浏览器视窗高度"),
});

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

export const midsceneYamlTool = tool(
  async ({ url, yaml, headless = true, width = 768, height = 768 }) => {
    try {
      console.log("准备执行Midscene YAML任务:");
      console.log("URL:", url);
      console.log("YAML内容:", yaml);
      
      // 设置环境变量，使用qwen3模型
      process.env.MIDSCENE_MODEL_NAME = "qwen-vl-max-latest";
      process.env.MIDSCENE_USE_QWEN_VL = "1";
      process.env.OPENAI_API_KEY = "sk-fe993c92dcc64df59dc08250494935a2";
      process.env.OPENAI_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";
      
      // 启动浏览器 - 强制使用无头模式
      console.log("正在启动浏览器...");
      const browser = await puppeteer.launch({
        headless: true, // 确保始终使用无头模式
        args: [
          "--no-sandbox", 
          "--disable-setuid-sandbox",
          "--disable-gpu",
          "--disable-dev-shm-usage"
        ],
      });

      const page = await browser.newPage();
      await page.setViewport({
        width,
        height,
        deviceScaleFactor: os.platform() === "darwin" ? 2 : 1,
      });

      // 访问URL
      console.log(`正在访问URL: ${url}`);
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });
      console.log("页面加载完成，等待3秒...");
      await sleep(3000); // 等待页面加载

      // 初始化Midscene代理
      console.log("初始化Midscene代理...");
      const agent = new PuppeteerAgent(page);

      // 执行YAML任务
      console.log("开始执行YAML任务...");
      const { result } = await agent.runYaml(yaml);
      console.log("YAML任务执行完成");
      
      // 关闭浏览器
      await browser.close();
      console.log("浏览器已关闭");

      return JSON.stringify(result, null, 2);
    } catch (error) {
      console.error("执行Midscene YAML任务时出错:", error);
      
      // 尝试获取更详细的错误信息
      let errorMessage = "未知错误";
      if (error instanceof Error) {
        errorMessage = `${error.name}: ${error.message}\n${error.stack || ''}`;
      } else {
        errorMessage = String(error);
      }
      
      return `执行失败: ${errorMessage}`;
    }
  },
  {
    name: "midsceneYaml",
    description: "使用Midscene执行YAML格式的自动化任务，可以进行网页搜索、点击、查询等操作",
    schema: midsceneYamlSchema,
  }
); 