# AI聊天框收起功能

## 🎯 功能概述

为测试用例详情页面的右侧AI聊天框添加了收起/展开功能，用户可以通过点击按钮来隐藏或显示AI助手，从而获得更大的内容查看空间。

## ✨ 功能特性

### 1. 收起按钮
- **位置**: 集成在AI助手标题栏的右侧
- **图标**: `PanelRightClose` (收起图标)
- **样式**: Ghost按钮，与标题栏风格一致
- **优势**: 不会遮挡任何内容，位置直观

### 2. 展开按钮
- **位置**: 收起状态下显示在收起面板的顶部
- **图标**: `PanelRightOpen` (展开图标)
- **样式**: 与收起按钮保持一致的设计

### 2. 动画效果
- **宽度变化**: 从480px平滑过渡到48px
- **透明度**: AI助手内容淡入淡出
- **持续时间**: 300ms的平滑过渡动画

### 3. 状态管理
- **本地状态**: 使用React useState管理收起状态
- **默认状态**: 默认展开显示AI助手

### 4. 用户体验
- **工具提示**: 按钮有明确的提示文字
- **视觉反馈**: 收起状态下显示简化的AI图标和文字
- **响应式**: 保持良好的视觉层次

## 🔧 技术实现

### 组件修改
**文件**: `app/test-case/[id]/components/testcase-layout.tsx`

#### 1. 导入新图标
```typescript
import {
  // ... 其他图标
  PanelRightClose,
  PanelRightOpen
} from 'lucide-react';
```

#### 2. 状态管理
```typescript
// AI聊天框收起状态
const [isChatCollapsed, setIsChatCollapsed] = useState(false);
```

#### 3. 动态样式
```typescript
// 容器宽度动态变化
className={`border-l border-slate-200 dark:border-zinc-700 flex flex-col h-full transition-all duration-300 relative ${
  isChatCollapsed ? 'w-12' : 'w-[480px]'
}`}
```

#### 4. 收起按钮
```typescript
<Button
  variant="ghost"
  size="sm"
  onClick={() => setIsChatCollapsed(!isChatCollapsed)}
  className={`absolute top-4 z-10 h-8 w-8 p-0 bg-white dark:bg-zinc-800 border border-slate-200 dark:border-zinc-700 shadow-sm hover:shadow-md transition-all duration-200 ${
    isChatCollapsed 
      ? 'left-2' 
      : 'left-4'
  }`}
  title={isChatCollapsed ? '展开AI助手' : '收起AI助手'}
>
  {isChatCollapsed ? (
    <PanelRightOpen className="w-4 h-4" />
  ) : (
    <PanelRightClose className="w-4 h-4" />
  )}
</Button>
```

#### 5. 条件渲染
```typescript
// AI助手内容
<div className={`h-full transition-all duration-300 ${
  isChatCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'
}`}>
  {!isChatCollapsed && (
    <TestCaseAssistant
      testCase={testCase}
      onTestCaseUpdate={onTestCaseUpdate}
      className="h-full"
    />
  )}
</div>

// 收起状态提示
{isChatCollapsed && (
  <div className="flex flex-col items-center justify-center h-full p-2">
    <Bot className="w-6 h-6 text-slate-400 dark:text-slate-500 mb-2" />
    <div className="text-xs text-slate-400 dark:text-slate-500 text-center leading-tight">
      AI<br />助手
    </div>
  </div>
)}
```

## 🎨 视觉设计

### 展开状态 (默认)
- **宽度**: 480px
- **内容**: 完整的AI助手界面
- **按钮**: 显示收起图标，位于左侧

### 收起状态
- **宽度**: 48px (12 * 4px)
- **内容**: 简化的AI图标和文字提示
- **按钮**: 显示展开图标，位于左侧

### 过渡动画
- **CSS类**: `transition-all duration-300`
- **变化属性**: 宽度、透明度、位置
- **缓动函数**: 默认ease

## 🚀 使用方式

1. **访问测试用例页面**: 打开任意测试用例详情页
2. **查看AI助手**: 右侧默认显示完整的AI聊天界面
3. **收起助手**: 点击左上角的收起按钮
4. **展开助手**: 在收起状态下点击展开按钮

## 📱 响应式考虑

- **桌面端**: 完整的收起/展开功能
- **移动端**: 保持原有的响应式布局
- **暗色模式**: 支持暗色主题下的样式

## 🔍 测试建议

1. **功能测试**:
   - 点击收起按钮验证动画效果
   - 点击展开按钮验证恢复功能
   - 验证AI助手功能在展开状态下正常工作

2. **视觉测试**:
   - 检查按钮位置和样式
   - 验证动画流畅性
   - 测试暗色模式下的显示效果

3. **交互测试**:
   - 验证工具提示显示
   - 测试按钮悬停效果
   - 确认收起状态下的视觉提示

## 🎉 总结

这个功能为用户提供了更灵活的界面布局选择，特别是在需要更大内容查看空间时。通过平滑的动画和清晰的视觉反馈，确保了良好的用户体验。

现在用户可以根据需要自由控制AI助手的显示状态，提高了界面的可用性和灵活性！
