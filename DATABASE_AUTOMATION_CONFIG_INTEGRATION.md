# 自动化配置数据库集成

## 🎯 功能概述

将test-case的自动化配置模块从静态数据改为通过数据库动态加载，实现真正的数据持久化和管理功能。

## 🔧 技术实现

### 1. AutomationModule组件改进

#### 状态管理
```typescript
const [automationConfigs, setAutomationConfigs] = useState<Record<string, AutomationConfig>>({});
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);
```

#### 数据加载函数
```typescript
const loadAutomationConfigs = async () => {
  try {
    setLoading(true);
    setError(null);
    
    const response = await fetch(`/api/automation-config?testCaseId=${testCase.id}`);
    if (!response.ok) {
      throw new Error('Failed to load automation configs');
    }
    
    const configs = await response.json();
    
    // 将配置数组转换为以framework为key的对象
    const configMap: Record<string, AutomationConfig> = {};
    configs.forEach((config: AutomationConfig) => {
      configMap[config.framework] = config;
    });
    
    setAutomationConfigs(configMap);
  } catch (err) {
    console.error('Error loading automation configs:', err);
    setError(err instanceof Error ? err.message : 'Unknown error');
  } finally {
    setLoading(false);
  }
};
```

#### 删除操作
```typescript
const handleDelete = async (framework: string) => {
  try {
    const config = automationConfigs[framework];
    if (!config) return;

    const response = await fetch(`/api/automation-config`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        testCaseId: testCase.id,
        framework: framework,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete automation config');
    }

    // 重新加载配置
    await loadAutomationConfigs();
  } catch (err) {
    console.error(`Error deleting ${framework} config:`, err);
  }
};
```

### 2. API路由扩展

#### 新增DELETE方法
```typescript
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { testCaseId, framework } = body;

    if (!testCaseId || !framework) {
      return NextResponse.json(
        { error: 'testCaseId and framework are required' },
        { status: 400 }
      );
    }

    logger.info(`删除自动化配置: testCaseId=${testCaseId}, framework=${framework}`);

    await deleteAutomationConfig(testCaseId, framework);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`删除自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    return NextResponse.json(
      { error: 'Failed to delete automation config' },
      { status: 500 }
    );
  }
}
```

### 3. 数据库查询函数

#### 删除配置函数
```typescript
export async function deleteAutomationConfig(testCaseId: string, framework: string): Promise<void> {
  try {
    await db
      .delete(automationConfig)
      .where(
        and(
          eq(automationConfig.testCaseId, testCaseId),
          eq(automationConfig.framework, framework)
        )
      );
  } catch (error) {
    logger.error(`删除自动化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    throw new ChatSDKError('bad_request:database', 'Failed to delete automation config');
  }
}
```

## 🎨 用户界面改进

### 1. 加载状态
```typescript
{loading && (
  <div className="flex items-center justify-center py-8">
    <RefreshCw className="w-6 h-6 animate-spin text-slate-400 mr-2" />
    <span className="text-slate-600 dark:text-slate-400">Loading automation configurations...</span>
  </div>
)}
```

### 2. 错误状态
```typescript
{error && (
  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
    <p className="text-red-600 text-sm">
      Error loading automation configurations: {error}
    </p>
    <Button
      variant="outline"
      size="sm"
      onClick={loadAutomationConfigs}
      className="mt-2"
    >
      <RefreshCw className="w-4 h-4 mr-2" />
      Retry
    </Button>
  </div>
)}
```

### 3. 刷新按钮
```typescript
<Button
  variant="outline"
  size="sm"
  onClick={loadAutomationConfigs}
  disabled={loading}
  className="border-slate-200 hover:bg-slate-50"
>
  <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
  Refresh
</Button>
```

## 📊 数据流程

### 1. 组件初始化
1. 组件挂载时调用`loadAutomationConfigs()`
2. 发送GET请求到`/api/automation-config?testCaseId=${testCase.id}`
3. 接收配置数组并转换为framework为key的对象
4. 更新组件状态

### 2. 配置删除
1. 用户点击删除按钮
2. 发送DELETE请求到`/api/automation-config`
3. 后端调用`deleteAutomationConfig()`删除数据库记录
4. 前端重新加载配置列表

### 3. 错误处理
1. 网络错误或API错误时显示错误信息
2. 提供重试按钮允许用户重新加载
3. 加载状态期间显示加载动画

## 🔄 API端点

### GET /api/automation-config
- **参数**: `testCaseId` (必需), `framework` (可选)
- **返回**: 自动化配置数组或单个配置
- **用途**: 获取指定测试用例的自动化配置

### POST /api/automation-config
- **请求体**: 自动化配置数据
- **返回**: 操作结果
- **用途**: 创建或更新自动化配置

### DELETE /api/automation-config
- **请求体**: `{ testCaseId, framework }`
- **返回**: 操作结果
- **用途**: 删除指定框架的自动化配置

## 🌟 功能特性

### 1. 实时数据加载
- 组件挂载时自动加载最新配置
- 支持手动刷新获取最新数据
- 操作后自动重新加载

### 2. 错误处理
- 网络错误提示
- 重试机制
- 用户友好的错误信息

### 3. 加载状态
- 加载动画
- 禁用状态管理
- 视觉反馈

### 4. 数据持久化
- 真正的数据库存储
- 支持CRUD操作
- 数据一致性保证

## 🚀 测试建议

### 1. 功能测试
- 测试配置加载功能
- 测试配置删除功能
- 测试刷新功能
- 测试错误处理

### 2. 性能测试
- 测试大量配置的加载性能
- 测试并发操作
- 测试网络延迟情况

### 3. 用户体验测试
- 测试加载状态显示
- 测试错误状态显示
- 测试操作反馈

## ✨ 总结

这次改进实现了自动化配置模块的完整数据库集成：

- **数据驱动**: 从数据库动态加载配置，不再依赖静态数据
- **实时更新**: 支持实时的增删改查操作
- **用户体验**: 提供完整的加载状态和错误处理
- **可扩展性**: 为未来的功能扩展奠定了基础

现在自动化配置模块真正成为了一个功能完整的数据管理界面！
