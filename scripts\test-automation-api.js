#!/usr/bin/env node

/**
 * 测试自动化配置API的返回格式
 */

const testCaseId = '1-1-1'; // 测试用例ID
const baseUrl = 'http://localhost:3000';

async function testAutomationConfigAPI() {
  console.log('🧪 测试自动化配置API...\n');

  try {
    // 测试获取所有配置
    console.log('📋 测试获取所有配置:');
    const allConfigsResponse = await fetch(`${baseUrl}/api/automation-config?testCaseId=${testCaseId}`);
    
    if (!allConfigsResponse.ok) {
      throw new Error(`HTTP ${allConfigsResponse.status}: ${allConfigsResponse.statusText}`);
    }
    
    const allConfigsData = await allConfigsResponse.json();
    console.log('✅ API响应成功');
    console.log('📊 返回数据类型:', typeof allConfigsData);
    console.log('📊 是否为数组:', Array.isArray(allConfigsData));
    console.log('📊 数据结构:', JSON.stringify(allConfigsData, null, 2));

    // 检查数据格式
    if (Array.isArray(allConfigsData)) {
      console.log('⚠️ 返回格式为数组，包含', allConfigsData.length, '个配置');
      allConfigsData.forEach((config, index) => {
        console.log(`   配置 ${index + 1}: ${config.framework}`);
      });
    } else if (typeof allConfigsData === 'object' && allConfigsData !== null) {
      console.log('✅ 返回格式为对象，包含以下框架:');
      Object.keys(allConfigsData).forEach(framework => {
        console.log(`   ${framework}: ${allConfigsData[framework] ? '已配置' : '未配置'}`);
      });
    } else {
      console.log('❌ 未知的返回格式');
    }

    // 测试获取特定框架配置
    console.log('\n📋 测试获取特定框架配置 (midscene):');
    const midsceneResponse = await fetch(`${baseUrl}/api/automation-config?testCaseId=${testCaseId}&framework=midscene`);
    
    if (midsceneResponse.ok) {
      const midsceneData = await midsceneResponse.json();
      console.log('✅ Midscene配置获取成功');
      console.log('📊 配置详情:', JSON.stringify(midsceneData, null, 2));
    } else if (midsceneResponse.status === 404) {
      console.log('ℹ️ Midscene配置不存在 (404)');
    } else {
      console.log('❌ 获取Midscene配置失败:', midsceneResponse.status);
    }

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('💡 提示: 请确保开发服务器正在运行 (npm run dev)');
    }
  }
}

// 运行测试
testAutomationConfigAPI();
