import type { Metadata } from "next";
import "../globals.css";
import { NextAuthSessionProvider } from "@/components/session-provider";

export const metadata: Metadata = {
  title: "认证 - AI Run",
  description: "登录或注册 AI Run 应用",
};

// 这个布局完全替换根布局，不会继承任何组件
export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="antialiased">
        <NextAuthSessionProvider>
          <div className="flex h-dvh w-screen items-center justify-center bg-background">
            {children}
          </div>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
} 