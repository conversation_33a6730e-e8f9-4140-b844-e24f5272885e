import NextAuth from "next-auth";
import { authConfig } from "@/app/auth/auth.config";
import Credentials from "next-auth/providers/credentials";
import { compare } from 'bcrypt-ts';
import { createGuestUser, getUser } from '@/lib/db/queries';
import { DUMMY_PASSWORD } from '@/lib/constants';

// 创建一个完整的NextAuth配置
const handler = NextAuth({
  ...authConfig,
  debug: true, // 启用调试模式
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        try {
          const users = await getUser(email);

          if (users.length === 0) {
            await compare(password, DUMMY_PASSWORD);
            return null;
          }

          const [user] = users;

          if (!user.password) {
            await compare(password, DUMMY_PASSWORD);
            return null;
          }

          const passwordsMatch = await compare(password, user.password);

          if (!passwordsMatch) return null;

          return { ...user, type: 'regular' };
        } catch (error) {
          console.error("常规登录错误:", error);
          throw new Error("登录失败，请稍后再试");
        }
      },
    }),
    Credentials({
      id: 'guest',
      credentials: {},
      async authorize() {
        try {
          console.log("尝试创建访客用户...");
          const [guestUser] = await createGuestUser();
          console.log("访客用户创建成功:", guestUser);
          return { ...guestUser, type: 'guest' };
        } catch (error) {
          console.error("访客登录错误:", error);
          throw new Error("访客登录失败，请稍后再试");
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.type = user.type;
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.type = token.type;
      }

      return session;
    },
  },
});

// 导出处理函数
export { handler as GET, handler as POST };
