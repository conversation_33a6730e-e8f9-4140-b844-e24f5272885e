# Docker 管理脚本使用指南

本文件夹包含用于管理 Docker 部署的脚本文件。

## 📁 脚本文件

- `docker-helper.bat` - Windows 批处理脚本
- `docker-helper.sh` - Linux/macOS Shell 脚本

## 🚀 使用方法

### 从项目根目录运行

```bash
# Windows
./devops/docker-helper.bat prod    # 部署生产环境
./devops/docker-helper.bat dev     # 部署开发环境
./devops/docker-helper.bat stop    # 停止所有服务
./devops/docker-helper.bat logs    # 查看日志

# Linux/macOS
./devops/docker-helper.sh prod     # 部署生产环境
./devops/docker-helper.sh dev      # 部署开发环境
./devops/docker-helper.sh stop     # 停止所有服务
./devops/docker-helper.sh logs     # 查看日志
```

### 从 devops 目录运行

```bash
# 进入 devops 目录
cd devops

# Windows
./docker-helper.bat prod           # 部署生产环境
./docker-helper.bat dev            # 部署开发环境

# Linux/macOS
./docker-helper.sh prod            # 部署生产环境
./docker-helper.sh dev             # 部署开发环境
```

## 📋 可用命令

### 部署命令
- `prod` / `production` - 部署生产环境
- `dev` / `development` - 部署开发环境

### 管理命令
- `stop` - 停止所有服务
- `restart` - 重启服务 (默认生产环境)
- `status` - 显示服务状态
- `cleanup` - 清理 Docker 资源

### 日志命令
- `logs` - 查看应用日志
- `logs --dev` - 查看开发环境日志
- `logs --service db` - 查看数据库日志

### 帮助命令
- `help` / `-h` / `--help` - 显示帮助信息

## 🔧 高级用法

### 查看特定服务日志

```bash
# 查看生产环境数据库日志
./devops/docker-helper.bat logs --service db

# 查看开发环境 Redis 日志
./devops/docker-helper.sh logs --dev --service redis
```

### 重启特定环境

```bash
# 重启开发环境
./devops/docker-helper.bat restart --dev

# 重启生产环境
./devops/docker-helper.sh restart
```

### 清理 Docker 资源

```bash
# 清理所有未使用的容器、镜像和网络
./devops/docker-helper.bat cleanup
./devops/docker-helper.sh cleanup
```

## ⚠️ 注意事项

1. **运行位置**: 脚本需要在项目根目录运行，因为 Docker Compose 文件使用了相对路径
2. **权限**: Linux/macOS 用户可能需要给脚本添加执行权限
   ```bash
   chmod +x devops/docker-helper.sh
   ```
3. **Docker 要求**: 确保已安装 Docker 和 Docker Compose
4. **环境变量**: 确保已正确配置环境变量

## 🔍 故障排除

### 脚本无法运行

```bash
# 检查文件权限
ls -la devops/docker-helper.sh

# 添加执行权限
chmod +x devops/docker-helper.sh
```

### Docker 命令失败

```bash
# 检查 Docker 是否运行
docker --version
docker-compose --version

# 检查服务状态
./devops/docker-helper.sh status
```

### 路径问题

如果遇到路径问题，请确保：
1. 在项目根目录运行脚本
2. Docker Compose 文件路径正确
3. 构建上下文设置正确

## 📚 相关文档

- [Docker 部署指南](DOCKER_README.md) - 详细的部署说明
- [DevOps 配置说明](README.md) - DevOps 文件夹说明
- [项目主文档](../README.md) - 项目完整文档 