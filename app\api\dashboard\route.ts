import { NextResponse } from 'next/server'
import { getDocumentTypeCounts } from '@/lib/db/queries'
import { db } from '@/lib/db'
import { chat, document, user } from '@/lib/db/schema'
import { count } from 'drizzle-orm'

export const dynamic = 'force-dynamic'

export async function GET() {
  // 统计总数
  const [chatCountResult] = await db.select({ count: count() }).from(chat)
  const [documentCountResult] = await db.select({ count: count() }).from(document)
  const [userCountResult] = await db.select({ count: count() }).from(user)
  // repository数量如有表可查，否则mock
  const repositoryCount = 9

  // 文档类型统计
  const documentTypeCounts = await getDocumentTypeCounts()

  return NextResponse.json({
    stats: [
      { title: 'Chat数量', value: chatCountResult.count, percent: '+12.5%' },
      { title: 'Document数量', value: documentCountResult.count, percent: '+8.2%' },
      { title: '用户数量', value: userCountResult.count, percent: '+3.1%' },
      { title: 'Repository数量', value: repositoryCount, percent: '+1.0%' },
    ],
    documentTypeCounts,
  })
} 