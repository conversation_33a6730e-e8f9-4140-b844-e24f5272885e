import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/app/auth/auth.config';
import {
  createFolder,
  getFolders,
  getTestCasesByFolder,
  deleteTestCase,
} from '@/lib/db/queries';
import { db } from '@/lib/db';
import { folder } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get('parentId');

    const folders = await getFolders(parentId || undefined);
    return NextResponse.json(folders);
  } catch (error) {
    console.error('Get folders error:', error);
    return NextResponse.json(
      { error: 'Failed to get folders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, parentId } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Folder name is required' },
        { status: 400 }
      );
    }

    const newFolder = await createFolder({
      name,
      description,
      parentId,
      createdBy: session.user?.email || 'unknown'
    });

    return NextResponse.json(newFolder, { status: 201 });
  } catch (error) {
    console.error('Create folder error:', error);
    return NextResponse.json(
      { error: 'Failed to create folder' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, description } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'Folder ID and name are required' },
        { status: 400 }
      );
    }

    const now = new Date();
    await db
      .update(folder)
      .set({
        name,
        description,
        updatedAt: now,
        updatedBy: session.user?.email || 'unknown'
      })
      .where(eq(folder.id, id));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Update folder error:', error);
    return NextResponse.json(
      { error: 'Failed to update folder' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const force = searchParams.get('force') === 'true'; // 是否强制删除（递归删除）

    if (!id) {
      return NextResponse.json(
        { error: 'Folder ID is required' },
        { status: 400 }
      );
    }

    // 递归删除文件夹及其所有内容的函数
    const deleteFolderRecursively = async (folderId: string) => {
      // 1. 删除文件夹内的所有测试用例
      const testCases = await getTestCasesByFolder(folderId);
      for (const testCase of testCases) {
        await deleteTestCase(testCase.id, session.user?.email || 'system');
      }

      // 2. 递归删除子文件夹
      const subFolders = await getFolders(folderId);
      for (const subFolder of subFolders) {
        await deleteFolderRecursively(subFolder.id);
      }

      // 3. 删除文件夹本身
      await db.delete(folder).where(eq(folder.id, folderId));
    };

    if (force) {
      // 强制删除：递归删除文件夹及其所有内容
      await deleteFolderRecursively(id);
    } else {
      // 普通删除：只删除空文件夹
      const subFolders = await getFolders(id);
      const testCases = await getTestCasesByFolder(id);

      if (subFolders.length > 0 || testCases.length > 0) {
        return NextResponse.json(
          {
            error: 'Cannot delete folder with content',
            message: `文件夹包含 ${subFolders.length} 个子文件夹和 ${testCases.length} 个测试用例。请使用强制删除或先清空文件夹。`,
            hasSubFolders: subFolders.length > 0,
            hasTestCases: testCases.length > 0,
            subFoldersCount: subFolders.length,
            testCasesCount: testCases.length
          },
          { status: 400 }
        );
      }

      await db.delete(folder).where(eq(folder.id, id));
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete folder error:', error);
    return NextResponse.json(
      { error: 'Failed to delete folder' },
      { status: 500 }
    );
  }
}
